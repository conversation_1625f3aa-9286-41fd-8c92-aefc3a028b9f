class DropArea {
	static instances = new Array();
    static snippets = {};

	static init(){
		var area = document.querySelectorAll('[data-dropArea]');
		//console.log(area)
		for (var i = 0; i < area.length; i++) {

			//console.log(area[i].dataset);
			DropArea.instances[i] = new DropArea(area[i], area[i].dataset.droparea, area[i].dataset.dropareaUrl);

		}
	}

	constructor(dropArea, dropAreaId, dropAreaUrl) {
		this.dropAreaId = dropAreaId;
		this.dropAreaUrl = dropAreaUrl;

		this.previewId = this.dropAreaId + "-gallery"

		this.dropArea = dropArea;//document.getElementById(this.dropAreaId);
        this.dropArea.setAttribute("id", this.dropAreaId);

		this.uploadProgress = []
		this.progressBar = document.querySelector("#" + this.dropAreaId + " .droparea-progress-bar");//document.getElementById(this.progressBarId)



		this.fileInput = document.querySelector("#" + this.dropAreaId +" input[type='file']");

		let self = this;
		this.fileInput.addEventListener("change", function (e){
			self.handleFiles(e.target.files);
		});

		// Prevent default drag behaviors
		;['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
			self.dropArea.addEventListener(eventName, self.preventDefaults, false)
			document.body.addEventListener(eventName, self.preventDefaults, false)
		})

// Highlight drop area when item is dragged over it
		;['dragenter', 'dragover'].forEach(eventName => {
			self.dropArea.addEventListener(eventName, function (e){
				self.dropArea.classList.add('highlight')
			}, false)
		})

		;['dragleave', 'drop'].forEach(eventName => {
			self.dropArea.addEventListener(eventName, function (e){
				self.dropArea.classList.remove('highlight')
			}, false)
		})

		// Handle dropped files
		this.dropArea.addEventListener('drop', function(e){
			var dt = e.dataTransfer
			var files = dt.files
			self.handleFiles(files)
		}, false)

		console.log(this);
	}

	preventDefaults (e) {
		e.preventDefault()
		e.stopPropagation()
	}

    previewFile(file) {
        let reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onloadend = function() {
            let img = document.createElement('img')
            img.src = reader.result
            document.querySelector("#" + this.dropAreaId + ' .droparea-gallery').appendChild(img)
        }
    }



	handleFiles(files) {
        //DropArea.snippets = {};
        //window["dropArea"] = { snippets: {}};
        spinnerOn();
		files = [...files]
		this.initializeProgress(files.length)
		let self = this;
        //console.log(files);
		files.forEach(function (file, i){
            //console.log(file);
			self.uploadFile(file, i);
		});

        //console.log(DropArea.snippets,  window["dropArea"]["snippets"]);
        /*files.forEach(function (file, i){
            self.previewFile(file);
        })*/

	}

	initializeProgress(numFiles) {
		this.progressBar.value = 0
		this.uploadProgress = []

		for(let i = numFiles; i > 0; i--) {
			this.uploadProgress.push(0)
		}
	}
	updateProgress(fileNumber, percent) {
		this.uploadProgress[fileNumber] = percent
		let total = this.uploadProgress.reduce((tot, curr) => tot + curr, 0) / this.uploadProgress.length
		this.progressBar.value = total
	}
	uploadFile(file, i) {
		var formData = new FormData()
		formData.append('file', file)
		let self = this;

		$.nette.ajax({
			url: self.dropAreaUrl,
			data: formData,
			processData: false,
			contentType: false,
			type: 'POST',
            off: ['unique'],
			xhr: function (){
				var xhr = $.ajaxSettings.xhr();
				//console.log(xhr);
				xhr.upload.onprogress = function (e) {
					// For uploads
					if (e.lengthComputable) {
						//console.log(e.loaded / e.total);
						self.updateProgress(i, (e.loaded * 100.0 / e.total) || 100)
					}
				};
				return xhr;
			},
		})
	}
}


