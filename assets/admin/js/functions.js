function checkIsExportDone() {
	$.nette.ajax({
		type  : "GET",
		url   : checkIsExportDoneLink,
	}).done(function (data){
		if(data.done){
			clearInterval(controlExportCheckIsDone);
		}
	})
};

function initSelect2(){
	Application.select2();
}
function initSelect2Ajax(){
	Application.select2Ajax();
}
function initSelect2Tag(){
	Application.select2Tag();
}
function getIdFormHref(el){
	let href = el.attr("href");
	let e = href.split("/");
	return parseInt(e[e.length - 1]);
}
function getJsonData(json){
	//console.log(json);
	try{
		return JSON.parse(json);
	} catch(e) {
		console.error("invalid json data");
	}
}
function fillSelect(el,json, trigger){
	if (json.redirect) {
		window.location.href = json.redirect;
		return false;
	}
	el.html("");
	let selected = 0;
	json.forEach(function(item, index){
		let element = $('<option value="' + item.id + '">'+item.value+'</option>');
		if(index == 0) {
			selected = item.id;
		}
		if(item.selected){
			selected = item.id;
		}
		//console.log(trigger, item.id);
		if(typeof trigger != "undefined" && trigger == item.id){
			selected = item.id;
		}
		el.append(element);

	});
	el.val(selected).change();
}

function nl2br(str, is_xhtml) {
	// http://kevin.vanzonneveld.net
	// +   original by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)
	// +   improved by: Philip Peterson
	// +   improved by: Onno Marsman
	// +   improved by: Atli Þór
	// +   bugfixed by: Onno Marsman
	// +      input by: Brett Zamir (http://brett-zamir.me)
	// +   bugfixed by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)
	// +   improved by: Brett Zamir (http://brett-zamir.me)
	// +   improved by: Maximusya
	// *     example 1: nl2br('Kevin\nvan\nZonneveld');
	// *     returns 1: 'Kevin<br />\nvan<br />\nZonneveld'
	// *     example 2: nl2br("\nOne\nTwo\n\nThree\n", false);
	// *     returns 2: '<br>\nOne<br>\nTwo<br>\n<br>\nThree<br>\n'
	// *     example 3: nl2br("\nOne\nTwo\n\nThree\n", true);
	// *     returns 3: '<br />\nOne<br />\nTwo<br />\n<br />\nThree<br />\n'
	var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br ' + '/>' : '<br>'; // Adjust comment to avoid issue on phpjs.org display

	return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
}