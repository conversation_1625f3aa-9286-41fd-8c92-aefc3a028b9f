<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.4.0/css/font-awesome.min.css">
  <link rel="stylesheet" href="../../css/froala_editor.css">
  <link rel="stylesheet" href="../../css/froala_style.css">
  <link rel="stylesheet" href="../../css/plugins/code_view.css">
  <link rel="stylesheet" href="../../css/plugins/colors.css">
  <link rel="stylesheet" href="../../css/plugins/emoticons.css">
  <link rel="stylesheet" href="../../css/plugins/image_manager.css">
  <link rel="stylesheet" href="../../css/plugins/image.css">
  <link rel="stylesheet" href="../../css/plugins/line_breaker.css">
  <link rel="stylesheet" href="../../css/plugins/table.css">
  <link rel="stylesheet" href="../../css/plugins/char_counter.css">
  <link rel="stylesheet" href="../../css/plugins/video.css">
  <link rel="stylesheet" href="../../css/plugins/fullscreen.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.3.0/codemirror.min.css">

  <style>
    body {
      text-align: center;
    }

    section {
      width: 81%;
      margin: auto;
      text-align: left;
    }
  </style>
</head>

<body>
  <section id="editor">
    <div id='edit' style="margin-top: 30px;">
      <h1>RTL LTR Direction Buttons</h1>

      <p>Custom buttons for RTL and LTR to change direction for the current selection.</p>

      <img class="fr-fir" src="../../img/photo1.jpg" alt="Old Clock" width="300" />

      <p>The rich text editor has full RTL support. Set the <a
          href="https://www.froala.com/wysiwyg-editor/v2.0/docs/options#direction" title="direction option"
          target="_blank">direction</a> option to rtl and writing in Arabic or Farsi will feel naturally.</p>

      <a href="http://froala.com" title="Aenean sed hendrerit" target="_blank">Aenean sed hendrerit</a> velit. Nullam eu
      mi dolor. Maecenas et erat risus. Nulla ac auctor diam, non aliquet ante. Fusce ullamcorper, ipsum id tempor
      lacinia, sem tellus malesuada libero, quis ornare sem massa in orci. Sed dictum dictum tristique. Proin eros
      turpis, ultricies eu sapien eget, ornare rutrum ipsum. Pellentesque eros nisl, ornare nec ipsum sed, aliquet
      sollicitudin erat. Nulla tincidunt porta <strong>vehicula.</strong><br />

      <table style="width: 60%;">
        <tr>
          <td style="width: 25%;">testing rtl</td>
          <td style="width: 25%;">rtl</td>
          <td style="width: 25%;">testing rtl</td>
          <td style="width: 25%;">rtl</td>
        </tr>
        <tr>
          <td style="width: 25%;">testing rtl</td>
          <td style="width: 25%;">rtl</td>
          <td style="width: 25%;">testing rtl</td>
          <td style="width: 25%;">rtl</td>
        </tr>
      </table>
    </div>
  </section>

  <script type="text/javascript"
    src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.3.0/codemirror.min.js"></script>
  <script type="text/javascript"
    src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.3.0/mode/xml/xml.min.js"></script>
  <script type="text/javascript" src="../../js/froala_editor.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/align.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/code_beautifier.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/code_view.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/colors.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/draggable.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/emoticons.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/font_size.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/font_family.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/image.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/image_manager.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/line_breaker.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/link.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/lists.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/paragraph_format.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/paragraph_style.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/video.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/table.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/url.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/entities.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/char_counter.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/inline_style.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/save.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/fullscreen.min.js"></script>
  <script type="text/javascript" src="../../js/languages/ro.js"></script>

  <script>
    (function () {
      var changeDirection = function (dir, align) {
        this.selection.save()
        var elements = this.selection.blocks()
        for (var i = 0; i < elements.length; i++) {
          var element = elements[i]
          if (element != this.$el.get(0)) {
            element.style.direction = dir
            element.style.textAlign = align
          }
        }

        this.selection.restore()
      }

      FroalaEditor.DefineIcon('rightToLeft', { NAME: 'long-arrow-left', SVG_KEY: 'undo' })
      FroalaEditor.RegisterCommand('rightToLeft', {
        title: 'RTL',
        focus: true,
        undo: true,
        refreshAfterCallback: true,
        callback: function () {
          changeDirection.apply(this, ['rtl', 'right'])
        }
      })

      FroalaEditor.DefineIcon('leftToRight', { NAME: 'long-arrow-right', SVG_KEY: 'redo' })
      FroalaEditor.RegisterCommand('leftToRight', {
        title: 'LTR',
        focus: true,
        undo: true,
        refreshAfterCallback: true,
        callback: function () {
          changeDirection.apply(this, ['ltr', 'left'])
        }
      })

      new FroalaEditor("#edit", {
        toolbarButtons: [ ['undo', 'redo', 'bold', 'formatOL', 'formatUL'], ['rightToLeft', 'leftToRight'] ]
      })
    })()
  </script>
</body>

</html>