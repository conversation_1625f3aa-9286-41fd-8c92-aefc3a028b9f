<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.4.0/css/font-awesome.min.css">
  <link rel="stylesheet" href="../../css/froala_editor.css">
  <link rel="stylesheet" href="../../css/froala_style.css">
  <link rel="stylesheet" href="../../css/plugins/table.css">

  <style>
    body {
      text-align: center;
    }

    section {
      width: 81%;
      margin: auto;
      text-align: left;
    }

    .class1 tbody tr:nth-child(2n) {
      background: #f9f9f9;
    }

    .class2 thead tr th,
    .class2 tbody tr td {
      border-style: dashed;
    }
  </style>
</head>

<body>
  <section id="editor">
    <div id='edit' style="margin-top: 30px;">
      <h1>Table Style</h1>

      <p>The <code>table.min.js</code> plugin it is possible to add custom style on the selected table inside the
        WYSIWYG HTML editor.also offers the possibility to resize a table in the WYSIWYG HTML editor.</p>

      <p>The classes should be defined in CSS, otherwise no changes will be visible on the table's appearance.</p>

      <table style="width: 100%;">
        <tbody>
          <tr>
            <td style="width: 25%;">
              <br>
            </td>
            <td style="width: 25%;">
              <br>
            </td>
            <td style="width: 25%;">
              <br>
            </td>
            <td style="width: 25%;">
              <br>
            </td>
          </tr>
          <tr>
            <td style="width: 25%;">
              <br>
            </td>
            <td style="width: 25%;">
              <br>
            </td>
            <td style="width: 25%;">
              <br>
            </td>
            <td style="width: 25%;">
              <br>
            </td>
          </tr>
        </tbody>
      </table>

      <p>You can define your own table styles using the <a
          href="http://www.froala.dev/wysiwyg-editor/docs/options#tableStyles" title="tableStyles">tableStyles</a>
        option. This option is an Object where the key represents the class name and its value is the style name that
        appears in the dropdown list. It is important to have unique keys otherwise they will not work properly.</p>
      <p>By default you can select multiple table styles at a time. If you want to toggle them and allow only one style
        to be selected at a time use the <a href="http://www.froala.dev/wysiwyg-editor/docs/options#tableMultipleStyles"
          title="tableMultipleStyles">tableMultipleStyles</a> option.</p>
    </div>
  </section>

  <script type="text/javascript" src="../../js/froala_editor.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/link.min.js"></script>
  <script type="text/javascript" src="../../js/plugins/table.min.js"></script>

  <script>
    (function () {
      new FroalaEditor("#edit", {
        toolbarButtons: [ ['bold', 'italic', 'underline', 'strikeThrough', 'table'], ['undo', 'redo'] ],

        // Define new table cell styles.
        tableStyles: {
          class1: 'Class 1',
          class2: 'Class 2'
        }
      })
    })()
  </script>
</body>

</html>