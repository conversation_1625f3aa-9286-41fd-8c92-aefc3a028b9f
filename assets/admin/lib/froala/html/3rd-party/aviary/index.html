<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.4.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="../../../css/froala_editor.css">
    <link rel="stylesheet" href="../../../css/froala_style.css">
    <link rel="stylesheet" href="../../../css/plugins/colors.css">
    <link rel="stylesheet" href="../../../css/plugins/emoticons.css">
    <link rel="stylesheet" href="../../../css/plugins/image_manager.css">
    <link rel="stylesheet" href="../../../css/plugins/image.css">
    <link rel="stylesheet" href="../../../css/plugins/line_breaker.css">
    <link rel="stylesheet" href="../../../css/plugins/table.css">
    <link rel="stylesheet" href="../../../css/plugins/char_counter.css">
    <link rel="stylesheet" href="../../../css/plugins/video.css">
    <link rel="stylesheet" href="../../../css/plugins/fullscreen.css">
    <link rel="stylesheet" href="../../../css/plugins/file.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/at.js/1.4.0/css/jquery.atwho.min.css">

    <!-- Code Mirror CSS file. -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.3.0/codemirror.min.css">

    <!-- Include the plugin CSS file. -->
    <link rel="stylesheet" href="../../../css/plugins/code_view.css">

    <style>
        body {
            text-align: center;
        }

        div#editor {
            width: 81%;
            margin: auto;
            text-align: left;
        }

        div#froala-details {
            text-align: left;
        }
    </style>
</head>

<body>
    <div id="editor">
        <div id="edit" style="margin-top: 30px;">
            <h1>Aviary integration</h1>

            <img class="fr-fir fr-dii" src="../../../img/photo1.jpg" alt="Old Clock" width="300" />

            <p>Click on the image then apply rich image editing using the advanced edit button. This will launch the
                aviary image editor plugin.</p>
            <p>To integrate the plugin an api key it will be needed but it's easily obtained from</p>

        </div>
    </div>
    <div id="froala-details">
        <h3>Use Aviary plugin instructions.</h3>
        <p>To integrate the Aviary plugin on your own domain/website there are 3 simple steps to follow.</p>
        <p>1. Go to <a href="https://creativesdk.adobe.com/docs/web/#/index.html">Adobe Creative</a> and register for a
            new account or sign if you already have one.</p>
        <p>2. Follow the steps to <a
                href="https://creativesdk.zendesk.com/hc/en-us/articles/*********-Why-and-how-to-register-my-app-">
                register your new app</a>, this will generate an api key that will be used later on.</p>
        <p>3. You should have your api key by now, so what's left to do is to initialize the Froala Editor and use your
            own api key.
            To do this you should pass <code> aviaryKey: 'your api key'</code> to the Froala Editor on init.</p>
    </div>

    <script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.3.0/codemirror.min.js"></script>
    <script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.3.0/mode/xml/xml.min.js"></script>
    <script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/at.js/1.4.0/js/jquery.atwho.min.js"></script>

    <script type="text/javascript" src="../../../js/froala_editor.min.js"></script>

    <script type="text/javascript" src="../../../js/plugins/align.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/code_beautifier.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/code_view.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/font_size.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/font_family.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/image.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/image_manager.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/link.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/lists.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/paragraph_format.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/url.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/entities.min.js"></script>
    <script type="text/javascript" src="../../../js/plugins/save.min.js"></script>
    <script type="text/javascript" src="../../../js/third_party/image_aviary.min.js"></script>

    <script>
        (function () {
            new FroalaEditor("#edit")
        })()
    </script>
</body>

</html>