<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>jQuery UI Tooltip - Forms</title>
	<link rel="stylesheet" href="../../themes/base/all.css">
	<link rel="stylesheet" href="../demos.css">
	<style>
	label {
		display: inline-block; width: 5em;
	}
	fieldset div {
		margin-bottom: 2em;
	}
	fieldset .help {
		display: inline-block;
	}
	.ui-tooltip {
		width: 210px;
	}
	</style>
	<script src="../../external/requirejs/require.js"></script>
	<script src="../bootstrap.js" data-modules="button">
		var tooltips = $( "[title]" ).tooltip({
			position: {
				my: "left top",
				at: "right+5 top-5",
				collision: "none"
			}
		});
		$( "<button>" )
			.text( "Show help" )
			.button()
			.on( "click", function() {
				tooltips.tooltip( "open" );
			})
			.insertAfter( "form" );
	</script>
</head>
<body>

<form>
	<fieldset>
		<div>
			<label for="firstname">Firstname</label>
			<input id="firstname" name="firstname" title="Please provide your firstname.">
		</div>
		<div>
			<label for="lastname">Lastname</label>
			<input id="lastname" name="lastname" title="Please provide also your lastname.">
		</div>
		<div>
			<label for="address">Address</label>
			<input id="address" name="address" title="Your home or work address.">
		</div>
	</fieldset>
</form>

<div class="demo-description">
<p>Use the button below to display the help texts, or just focus or mouseover the indivdual inputs.</p>
<p>A fixed width is defined in CSS to make the tooltips look consistent when displayed all at once.</p>
</div>
</body>
</html>
