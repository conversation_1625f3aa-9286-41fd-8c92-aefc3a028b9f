<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>jQuery UI Draggable - Cursor style</title>
	<link rel="stylesheet" href="../../themes/base/all.css">
	<link rel="stylesheet" href="../demos.css">
	<style>
	#draggable, #draggable2, #draggable3 { width: 100px; height: 100px; padding: 0.5em; float: left; margin: 0 10px 10px 0; }
	</style>
	<script src="../../external/requirejs/require.js"></script>
	<script src="../bootstrap.js">
		$( "#draggable" ).draggable({ cursor: "move", cursorAt: { top: 56, left: 56 } });
		$( "#draggable2" ).draggable({ cursor: "crosshair", cursorAt: { top: -5, left: -5 } });
		$( "#draggable3" ).draggable({ cursorAt: { bottom: 0 } });
	</script>
</head>
<body>

<div id="draggable" class="ui-widget-content">
	<p>I will always stick to the center (relative to the mouse)</p>
</div>

<div id="draggable2" class="ui-widget-content">
	<p>My cursor is at left -5 and top -5</p>
</div>

<div id="draggable3" class="ui-widget-content">
	<p>My cursor position is only controlled for the 'bottom' value</p>
</div>

<div class="demo-description">
<p>Position the cursor while dragging the object. By default the cursor appears in the center of the dragged object; use the <code>cursorAt</code> option to specify another location relative to the draggable (specify a pixel value from the top, right, bottom, and/or left).  Customize the cursor's appearance by supplying the <code>cursor</code> option with a valid CSS cursor value: default, move, pointer, crosshair, etc.</p>
</div>
</body>
</html>
