<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>jQuery UI Spinner - Currency</title>
	<link rel="stylesheet" href="../../themes/base/all.css">
	<link rel="stylesheet" href="../demos.css">
	<script src="../../external/requirejs/require.js"></script>
	<script src="../bootstrap.js" data-modules="external/globalize/globalize external/globalize/globalize.culture.de-DE external/globalize/globalize.culture.ja-JP external/jquery-mousewheel/jquery.mousewheel">
		$( "#currency" ).on( "change", function() {
			$( "#spinner" ).spinner( "option", "culture", $( this ).val() );
		});

		$( "#spinner" ).spinner({
			min: 5,
			max: 2500,
			step: 25,
			start: 1000,
			numberFormat: "C"
		});
	</script>
</head>
<body>

<p>
	<label for="spinner">Amount to donate:</label>
	<input id="spinner" name="spinner" value="5">
</p>

<p>
	<label for="currency">Currency to donate</label>
	<select id="currency" name="currency">
		<option value="en-US">US $</option>
		<option value="de-DE">EUR €</option>
		<option value="ja-JP">YEN ¥</option>
	</select>
</p>

<div class="demo-description">
<p>Example of a donation form, with currency selection and amount spinner.</p>
</div>
</body>
</html>
