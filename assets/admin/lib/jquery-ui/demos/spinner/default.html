<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>jQuery UI Spinner - Default functionality</title>
	<link rel="stylesheet" href="../../themes/base/all.css">
	<link rel="stylesheet" href="../demos.css">
	<script src="../../external/requirejs/require.js"></script>
	<script src="../bootstrap.js" data-modules="external/jquery-mousewheel/jquery.mousewheel">
		var spinner = $( "#spinner" ).spinner();

		$( "#disable" ).on( "click", function() {
			if ( spinner.spinner( "option", "disabled" ) ) {
				spinner.spinner( "enable" );
			} else {
				spinner.spinner( "disable" );
			}
		});
		$( "#destroy" ).on( "click", function() {
			if ( spinner.spinner( "instance" ) ) {
				spinner.spinner( "destroy" );
			} else {
				spinner.spinner();
			}
		});
		$( "#getvalue" ).on( "click", function() {
			alert( spinner.spinner( "value" ) );
		});
		$( "#setvalue" ).on( "click", function() {
			spinner.spinner( "value", 5 );
		});

		$( "button" ).button();
	</script>
</head>
<body>

<p>
	<label for="spinner">Select a value:</label>
	<input id="spinner" name="value">
</p>

<p>
	<button id="disable">Toggle disable/enable</button>
	<button id="destroy">Toggle widget</button>
</p>

<p>
	<button id="getvalue">Get value</button>
	<button id="setvalue">Set value to 5</button>
</p>

<div class="demo-description">
<p>Default spinner.</p>
</div>
</body>
</html>
