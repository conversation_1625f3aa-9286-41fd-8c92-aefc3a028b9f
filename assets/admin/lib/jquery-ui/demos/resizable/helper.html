<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>jQuery UI Resizable - Helper</title>
	<link rel="stylesheet" href="../../themes/base/all.css">
	<link rel="stylesheet" href="../demos.css">
	<style>
	#resizable { width: 150px; height: 150px; padding: 0.5em; }
	#resizable h3 { text-align: center; margin: 0; }
	.ui-resizable-helper { border: 2px dotted #00F; }
	</style>
	<script src="../../external/requirejs/require.js"></script>
	<script src="../bootstrap.js">
		$( "#resizable" ).resizable({
			helper: "ui-resizable-helper"
		});
	</script>
</head>
<body>

<div id="resizable" class="ui-widget-content">
	<h3 class="ui-widget-header">Helper</h3>
</div>

<div class="demo-description">
<p>Display only an outline of the element while resizing by setting the <code>helper</code> option to a CSS class.</p>
</div>
</body>
</html>
