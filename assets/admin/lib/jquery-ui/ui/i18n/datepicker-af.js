/* Afrikaans initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON>. */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.af = {
	closeText: "Selekteer",
	prevText: "Vorige",
	nextText: "Volgende",
	currentText: "Vandag",
	monthNames: [ "<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","April","<PERSON>","<PERSON><PERSON>",
	"<PERSON>","<PERSON>","September","Ok<PERSON><PERSON>","November","Des<PERSON>ber" ],
	monthNamesShort: [ "Jan", "Feb", "Mrt", "Apr", "Mei", "Jun",
	"Jul", "Aug", "Sep", "Okt", "Nov", "Des" ],
	dayNames: [ "<PERSON>dag", "<PERSON>andag", "<PERSON><PERSON>dag", "Woensdag", "<PERSON>derdag", "<PERSON>rydag", "Saterdag" ],
	dayNamesShort: [ "<PERSON>", "Ma<PERSON>", "<PERSON>", "Woe", "Don", "Vry", "Sat" ],
	dayNamesMin: [ "So","Ma","Di","Wo","Do","Vr","Sa" ],
	weekHeader: "Wk",
	dateFormat: "dd/mm/yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.af );

return datepicker.regional.af;

} ) );
