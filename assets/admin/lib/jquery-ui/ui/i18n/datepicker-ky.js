/* Kyrgyz (UTF-8) initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> (<EMAIL>). */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.ky = {
	closeText: "Жабуу",
	prevText: "&#x3c;Мур",
	nextText: "Кий&#x3e;",
	currentText: "Бүгүн",
	monthNames: [ "Январь","Февраль","Март","Апрель","Май","Июнь",
	"Июль","Август","Сентябрь","Октябрь","Но<PERSON><PERSON>рь","Декабрь" ],
	monthNamesShort: [ "<PERSON><PERSON><PERSON>","<PERSON>ев","<PERSON>ар","<PERSON><PERSON>р","<PERSON>а<PERSON>","<PERSON>юн",
	"Июл","Авг","<PERSON>е<PERSON>","Окт","Ноя","Дек" ],
	dayNames: [ "жекшемби", "дүйшөмбү", "шейшемби", "шаршемби", "бейшемби", "жума", "ишемби" ],
	dayNamesShort: [ "жек", "дүй", "шей", "шар", "бей", "жум", "ише" ],
	dayNamesMin: [ "Жк","Дш","Шш","Шр","Бш","Жм","Иш" ],
	weekHeader: "Жум",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ""
};
datepicker.setDefaults( datepicker.regional.ky );

return datepicker.regional.ky;

} ) );
