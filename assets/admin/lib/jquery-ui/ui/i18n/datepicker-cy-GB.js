/* Welsh/UK initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON>. */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional[ "cy-GB" ] = {
	closeText: "Done",
	prevText: "Prev",
	nextText: "Next",
	currentText: "Today",
	monthNames: [ "<PERSON>wr","Chwefror","Maw<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","<PERSON>he<PERSON>",
	"Gorffennaf","Awst","<PERSON><PERSON>","Hydref","Tachwedd","Rhagfyr" ],
	monthNamesShort: [ "Ion", "Chw", "Maw", "Ebr", "<PERSON>", "<PERSON>h",
	"Gor", "<PERSON><PERSON>", "<PERSON>d", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ha" ],
	dayNames: [
		"<PERSON>yd<PERSON>",
		"<PERSON><PERSON><PERSON>lu<PERSON>",
		"<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON> Mercher",
		"<PERSON>yd<PERSON>au",
		"<PERSON><PERSON><PERSON>",
		"<PERSON>ydd Sadwrn"
	],
	dayNamesShort: [ "Sul", "Llu", "Maw", "Mer", "Iau", "Gwe", "Sad" ],
	dayNamesMin: [ "Su","Ll","Ma","Me","Ia","Gw","Sa" ],
	weekHeader: "Wy",
	dateFormat: "dd/mm/yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional[ "cy-GB" ] );

return datepicker.regional[ "cy-GB" ];

} ) );
