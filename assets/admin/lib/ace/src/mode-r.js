define("ace/mode/tex_highlight_rules",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/mode/text_highlight_rules"], function(require, exports, module){/*
 * tex_highlight_rules.js
 *
 * Copyright (C) 2009-11 by RStudio, Inc.
 *
 * The Initial Developer of the Original Code is
 * Ajax.org B.V.
 * Portions created by the Initial Developer are Copyright (C) 2010
 * the Initial Developer. All Rights Reserved.
 *
 * Distributed under the BSD license:
 *
 * Copyright (c) 2010, Ajax.org B.V.
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright
 *       notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *       notice, this list of conditions and the following disclaimer in the
 *       documentation and/or other materials provided with the distribution.
 *     * Neither the name of Ajax.org B.V. nor the
 *       names of its contributors may be used to endorse or promote products
 *       derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL AJAX.ORG B.V. BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 *
 */
"use strict";
var oop = require("../lib/oop");
var lang = require("../lib/lang");
var TextHighlightRules = require("./text_highlight_rules").TextHighlightRules;
var TexHighlightRules = function (textClass) {
    if (!textClass)
        textClass = "text";
    this.$rules = {
        "start": [
            {
                token: "comment",
                regex: "%.*$"
            }, {
                token: textClass,
                regex: "\\\\[$&%#\\{\\}]"
            }, {
                token: "keyword",
                regex: "\\\\(?:documentclass|usepackage|newcounter|setcounter|addtocounter|value|arabic|stepcounter|newenvironment|renewenvironment|ref|vref|eqref|pageref|label|cite[a-zA-Z]*|tag|begin|end|bibitem)\\b",
                next: "nospell"
            }, {
                token: "keyword",
                regex: "\\\\(?:[a-zA-Z0-9]+|[^a-zA-Z0-9])"
            }, {
                token: "paren.keyword.operator",
                regex: "[[({]"
            }, {
                token: "paren.keyword.operator",
                regex: "[\\])}]"
            }, {
                token: textClass,
                regex: "\\s+"
            }
        ],
        "nospell": [
            {
                token: "comment",
                regex: "%.*$",
                next: "start"
            }, {
                token: "nospell." + textClass,
                regex: "\\\\[$&%#\\{\\}]"
            }, {
                token: "keyword",
                regex: "\\\\(?:documentclass|usepackage|newcounter|setcounter|addtocounter|value|arabic|stepcounter|newenvironment|renewenvironment|ref|vref|eqref|pageref|label|cite[a-zA-Z]*|tag|begin|end|bibitem)\\b"
            }, {
                token: "keyword",
                regex: "\\\\(?:[a-zA-Z0-9]+|[^a-zA-Z0-9])",
                next: "start"
            }, {
                token: "paren.keyword.operator",
                regex: "[[({]"
            }, {
                token: "paren.keyword.operator",
                regex: "[\\])]"
            }, {
                token: "paren.keyword.operator",
                regex: "}",
                next: "start"
            }, {
                token: "nospell." + textClass,
                regex: "\\s+"
            }, {
                token: "nospell." + textClass,
                regex: "\\w+"
            }
        ]
    };
};
oop.inherits(TexHighlightRules, TextHighlightRules);
exports.TexHighlightRules = TexHighlightRules;

});

define("ace/mode/r_highlight_rules",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/mode/text_highlight_rules","ace/mode/tex_highlight_rules"], function(require, exports, module){/*
 * r_highlight_rules.js
 *
 * Copyright (C) 2009-11 by RStudio, Inc.
 *
 * The Initial Developer of the Original Code is
 * Ajax.org B.V.
 * Portions created by the Initial Developer are Copyright (C) 2010
 * the Initial Developer. All Rights Reserved.
 *
 * Distributed under the BSD license:
 *
 * Copyright (c) 2010, Ajax.org B.V.
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright
 *       notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *       notice, this list of conditions and the following disclaimer in the
 *       documentation and/or other materials provided with the distribution.
 *     * Neither the name of Ajax.org B.V. nor the
 *       names of its contributors may be used to endorse or promote products
 *       derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL AJAX.ORG B.V. BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 *
 */
var oop = require("../lib/oop");
var lang = require("../lib/lang");
var TextHighlightRules = require("./text_highlight_rules").TextHighlightRules;
var TexHighlightRules = require("./tex_highlight_rules").TexHighlightRules;
var RHighlightRules = function () {
    var keywords = lang.arrayToMap(("function|if|in|break|next|repeat|else|for|return|switch|while|try|tryCatch|stop|warning|require|library|attach|detach|source|setMethod|setGeneric|setGroupGeneric|setClass")
        .split("|"));
    var buildinConstants = lang.arrayToMap(("NULL|NA|TRUE|FALSE|T|F|Inf|NaN|NA_integer_|NA_real_|NA_character_|" +
        "NA_complex_").split("|"));
    this.$rules = {
        "start": [
            {
                token: "comment.sectionhead",
                regex: "#+(?!').*(?:----|====|####)\\s*$"
            },
            {
                token: "comment",
                regex: "#+'",
                next: "rd-start"
            },
            {
                token: "comment",
                regex: "#.*$"
            },
            {
                token: "string",
                regex: '["]',
                next: "qqstring"
            },
            {
                token: "string",
                regex: "[']",
                next: "qstring"
            },
            {
                token: "constant.numeric",
                regex: "0[xX][0-9a-fA-F]+[Li]?\\b"
            },
            {
                token: "constant.numeric",
                regex: "\\d+L\\b"
            },
            {
                token: "constant.numeric",
                regex: "\\d+(?:\\.\\d*)?(?:[eE][+\\-]?\\d*)?i?\\b"
            },
            {
                token: "constant.numeric",
                regex: "\\.\\d+(?:[eE][+\\-]?\\d*)?i?\\b"
            },
            {
                token: "constant.language.boolean",
                regex: "(?:TRUE|FALSE|T|F)\\b"
            },
            {
                token: "identifier",
                regex: "`.*?`"
            },
            {
                onMatch: function (value) {
                    if (keywords[value])
                        return "keyword";
                    else if (buildinConstants[value])
                        return "constant.language";
                    else if (value == '...' || value.match(/^\.\.\d+$/))
                        return "variable.language";
                    else
                        return "identifier";
                },
                regex: "[a-zA-Z.][a-zA-Z0-9._]*\\b"
            },
            {
                token: "keyword.operator",
                regex: "%%|>=|<=|==|!=|\\->|<\\-|\\|\\||&&|=|\\+|\\-|\\*|/|\\^|>|<|!|&|\\||~|\\$|:"
            },
            {
                token: "keyword.operator",
                regex: "%.*?%"
            },
            {
                token: "paren.keyword.operator",
                regex: "[[({]"
            },
            {
                token: "paren.keyword.operator",
                regex: "[\\])}]"
            },
            {
                token: "text",
                regex: "\\s+"
            }
        ],
        "qqstring": [
            {
                token: "string",
                regex: '(?:(?:\\\\.)|(?:[^"\\\\]))*?"',
                next: "start"
            },
            {
                token: "string",
                regex: '.+'
            }
        ],
        "qstring": [
            {
                token: "string",
                regex: "(?:(?:\\\\.)|(?:[^'\\\\]))*?'",
                next: "start"
            },
            {
                token: "string",
                regex: '.+'
            }
        ]
    };
    var rdRules = new TexHighlightRules("comment").getRules();
    for (var i = 0; i < rdRules["start"].length; i++) {
        rdRules["start"][i].token += ".virtual-comment";
    }
    this.addRules(rdRules, "rd-");
    this.$rules["rd-start"].unshift({
        token: "text",
        regex: "^",
        next: "start"
    });
    this.$rules["rd-start"].unshift({
        token: "keyword",
        regex: "@(?!@)[^ ]*"
    });
    this.$rules["rd-start"].unshift({
        token: "comment",
        regex: "@@"
    });
    this.$rules["rd-start"].push({
        token: "comment",
        regex: "[^%\\\\[({\\])}]+"
    });
};
oop.inherits(RHighlightRules, TextHighlightRules);
exports.RHighlightRules = RHighlightRules;

});

define("ace/mode/matching_brace_outdent",["require","exports","module","ace/range"], function(require, exports, module){"use strict";
var Range = require("../range").Range;
var MatchingBraceOutdent = function () { };
(function () {
    this.checkOutdent = function (line, input) {
        if (!/^\s+$/.test(line))
            return false;
        return /^\s*\}/.test(input);
    };
    this.autoOutdent = function (doc, row) {
        var line = doc.getLine(row);
        var match = line.match(/^(\s*\})/);
        if (!match)
            return 0;
        var column = match[1].length;
        var openBracePos = doc.findMatchingBracket({ row: row, column: column });
        if (!openBracePos || openBracePos.row == row)
            return 0;
        var indent = this.$getIndent(doc.getLine(openBracePos.row));
        doc.replace(new Range(row, 0, row, column - 1), indent);
    };
    this.$getIndent = function (line) {
        return line.match(/^\s*/)[0];
    };
}).call(MatchingBraceOutdent.prototype);
exports.MatchingBraceOutdent = MatchingBraceOutdent;

});

define("ace/mode/r",["require","exports","module","ace/unicode","ace/range","ace/lib/oop","ace/mode/text","ace/mode/text_highlight_rules","ace/mode/r_highlight_rules","ace/mode/matching_brace_outdent"], function(require, exports, module){/*
 * r.js
 *
 * Copyright (C) 2009-11 by RStudio, Inc.
 *
 * The Initial Developer of the Original Code is
 * Ajax.org B.V.
 * Portions created by the Initial Developer are Copyright (C) 2010
 * the Initial Developer. All Rights Reserved.
 *
 * Distributed under the BSD license:
 *
 * Copyright (c) 2010, Ajax.org B.V.
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright
 *       notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *       notice, this list of conditions and the following disclaimer in the
 *       documentation and/or other materials provided with the distribution.
 *     * Neither the name of Ajax.org B.V. nor the
 *       names of its contributors may be used to endorse or promote products
 *       derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL AJAX.ORG B.V. BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 *
 */
"use strict";
var unicode = require("../unicode");
var Range = require("../range").Range;
var oop = require("../lib/oop");
var TextMode = require("./text").Mode;
var TextHighlightRules = require("./text_highlight_rules").TextHighlightRules;
var RHighlightRules = require("./r_highlight_rules").RHighlightRules;
var MatchingBraceOutdent = require("./matching_brace_outdent").MatchingBraceOutdent;
var Mode = function () {
    this.HighlightRules = RHighlightRules;
    this.$outdent = new MatchingBraceOutdent();
    this.$behaviour = this.$defaultBehaviour;
};
oop.inherits(Mode, TextMode);
(function () {
    this.lineCommentStart = "#";
    this.tokenRe = new RegExp("^[" + unicode.wordChars + "._]+", "g");
    this.nonTokenRe = new RegExp("^(?:[^" + unicode.wordChars + "._]|\s])+", "g");
    this.$id = "ace/mode/r";
    this.snippetFileId = "ace/snippets/r";
}).call(Mode.prototype);
exports.Mode = Mode;

});                (function() {
                    window.require(["ace/mode/r"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            