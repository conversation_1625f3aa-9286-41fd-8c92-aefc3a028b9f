<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>Editor</title>
  <style type="text/css" media="screen">

	.ace_editor {
		border: 1px solid lightgray;
		margin: auto;
		height: 200px;
		width: 80%;
	}
	.scrollmargin {
		height: 80px;
        text-align: center;
	}
    </style>
</head>
<body>
<pre id="editor1">autoresizing editor</pre>
<div class="scrollmargin"></div>
<pre id="editor2">minHeight = 2 lines</pre>
<div class="scrollmargin"></div>
<pre id="editor3" style="width: 40%;"></pre>
<div class="scrollmargin"></div>
<pre id="editor"></pre>

<!-- load ace -->
<script src="../src/ace.js"></script>
<script>
    var editor1 = ace.edit("editor1", {
        theme: "ace/theme/tomorrow_night_eighties",
        mode: "ace/mode/html",
        maxLines: 30,
        wrap: true,
        autoScrollEditorIntoView: true
    });

    var editor2 = ace.edit("editor2", {
        theme: "ace/theme/tomorrow_night_blue",
        mode: "ace/mode/html",
        autoScrollEditorIntoView: true,
        maxLines: 30,
        minLines: 2
    });

    var editor = ace.edit("editor3");
    editor.setOptions({
        autoScrollEditorIntoView: true,
        maxLines: 8
    });
    editor.renderer.setScrollMargin(10, 10, 10, 10);
    
    var editor = ace.edit("editor");
    editor.setTheme("ace/theme/tomorrow");
    editor.session.setMode("ace/mode/html");
    editor.setAutoScrollEditorIntoView(true);
    editor.setOption("maxLines", 100);
</script>

<script src="./show_own_source.js"></script>

</body>
</html>
