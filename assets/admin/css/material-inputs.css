
.form-label-group {
	position: relative;
	margin-bottom: 1rem;
}

.form-label-group > input,
.form-label-group > select,
.form-label-group .select2-container--default .select2-selection, /* .select2-selection--single,*/
.form-label-group label,
.form-label-group .input-group input{
	height: 3.125rem;
	padding: .75rem;
}

.form-label-group > input.form-control-sm,
.form-label-group > select.form-control-sm,
.form-label-group label {
	height: 2.525rem;
}

.form-label-group .select2-container--default .select2-selection--multiple {
	min-height: 4rem;
	height: auto;
}

.form-label-group  label {
	position: absolute;
	top: 2px;
	left: 1px;
	display: block;
	/*width: 100%;*/
	margin-bottom: 0; /* Override default `<label>` margin */
	line-height: 1.5;
	color: #495057;
	pointer-events: none;
	cursor: text; /* Match the input under the label */
	border: 1px solid transparent;
	border-radius: .25rem;
	transition: all .1s ease-in-out;
	z-index: 10000;
}

.form-label-group input.form-control-sm + label {
	font-size: 13px;
	padding: .5rem .75rem;
}

.form-label-group textarea:placeholder-shown ~ label {
	top:-2px;
}

.form-label-group input::-webkit-input-placeholder, .form-label-group textarea::-webkit-input-placeholder {
	color: transparent;
}

.form-label-group input:-ms-input-placeholder, .form-label-group textarea:-ms-input-placeholder {
	color: transparent;
}

.form-label-group input::-ms-input-placeholder, .form-label-group textarea::-ms-input-placeholder {
	color: transparent;
}

.form-label-group input::-moz-placeholder, .form-label-group textarea::-moz-placeholder {
	color: transparent;
}

.form-label-group input::placeholder, .form-label-group textarea::placeholder {
	color: transparent;
}

.form-label-group input:not(:placeholder-shown), .form-label-group textarea:not(:placeholder-shown) {
	padding-top: 1.25rem;
	padding-bottom: .25rem;
}

.form-label-group select:not(.select2) {
	padding-top: 1.25rem;
	padding-bottom: 0.25rem;
	padding-left: 0.5rem;
}

.form-label-group textarea:not(:placeholder-shown) {
	padding-top: 1.45rem;
}

.form-label-group input:not(:placeholder-shown) ~ label,
.form-label-group textarea:not(:placeholder-shown) ~ label
  {
	padding-top: .15rem;
	padding-bottom: .0rem;
	font-size: 11px;
	font-weight: bold;
	color: #777;/*#777;*/
	height:auto;
	background: #FFF;
}
.form-label-group input.form-control-sm:not(:placeholder-shown) ~ label
{
	font-size: 9px;
	padding-top: .1rem;
}
.form-label-group input:read-only:not(:placeholder-shown) ~ label,
.form-label-group textarea:read-only:not(:placeholder-shown) ~ label,
.form-label-group input:disabled:not(:placeholder-shown) ~ label,
.form-label-group textarea:disabled:not(:placeholder-shown) ~ label{
	background: transparent;
}

.form-label-group input:focus:not(:placeholder-shown) ~ label,
.form-label-group textarea:focus:not(:placeholder-shown) ~ label {
	color:#55a6f8;
}

.form-label-group input.is-invalid:not(:placeholder-shown) ~ label ,
.form-label-group textarea.is-invalid:not(:placeholder-shown) ~ label ,
.form-label-group select.is-invalid:not(:placeholder-shown) ~ label {
	color: #DC3545;
}

.form-label-group input.is-warning:not(:placeholder-shown) ~ label ,
.form-label-group textarea.is-warning:not(:placeholder-shown) ~ label ,
.form-label-group select.is-warning:not(:placeholder-shown) ~ label {
	color: #F49917;
}

.form-label-group select.is-invalid + .select2-container--default  .select2-selection {
	border-color: #DC3545;
}

.form-label-group select ~ label {
	padding-top: .25rem;
	padding-bottom: .25rem;
	font-size: 11px;
	font-weight: bold;
	color: #777;/*#777;*/

}
.form-label-group select.form-control-sm ~ label {
	padding-top: .1rem;
}
.form-label-group .select2-container--default .select2-selection/*.select2-selection--single*/ .select2-selection__rendered {
	padding-left:0;
	padding-top:.5rem
}
.form-label-group .select2-container--default  .select2-selection/*.select2-selection--single*/ {
	padding-bottom:0;
}

.select2-container--open .select2-dropdown {
	z-index: 10007!important;
}

.modal-backdrop {
	z-index: 10004;
}
.modal {
	z-index: 10006;
}
#flashHolder {
	z-index: 10007;
}
.br-header {
	z-index: 10004
;
}
.dropdown-menu {
	z-index: 10005;
}

.datepicker {
	z-index: 10015 !important;
}
.form-label-group .input-group .btn {
	padding-top: .85rem!important;
}
.ui-autocomplete {
	z-index: 10006 !important;
}

select.select2-tag-sm + .select2-container--default .select2-selection--multiple .select2-selection__choice {
	margin-top:1px;
	margin-right: 2px;
	padding:2px 5px 2px 12px;
	font-size: 11px;
}
select.select2-tag-sm + .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
	top:2px;
	left:3px;
}
select.select2-tag-sm + .select2-container--default .select2-selection--multiple {
	min-height: 3.125rem;
	height: auto;

}

select.select2-tag-sm + .select2-container--default .select2-search--inline .select2-search__field {
	font-size: 11px;
	line-height: 16px;
	margin-top:3px;
}

input + i.fa-eye, input + i.fa-eye-slash {
	position: absolute;
	right:15px;
	top:19px;
	cursor: pointer;
}

.select-remove-value {
	position: absolute;right: 30px;top:50%;margin-top: 0px;
}

.select2-container--disabled + div.select-remove-value,
select:disabled + div.select-remove-value {
	display: none;
}