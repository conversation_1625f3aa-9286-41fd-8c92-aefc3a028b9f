.fa-gear:before {
	content: "\f085";
}
.fa-repeat:before {
	content: "\f01e";
}
.fa-arrows:before {
	content: "\f0b2";
}
.fa-remove:before{
	content: "\f00d";
}
.fa-check-square-o:before{
	content: "\f14a";
}
.fa-square-o:before {
	content: "\f0c8";
}
.datagrid table thead tr.row-group-actions th {
	border-bottom-width: 1px !important;
}
/*.table-bordered {
	border: 1px solid #dee2e6 !important;
}*/
.table {
	border-collapse: collapse !important;
}
.datagrid .row-grid-bottom {
	border: none;
	border-top:1px solid #dee2e6 !important;
}
#flashHolder {
	position: fixed;
	top: 75px;
	right:15px;
	z-index: 9999;
	min-width: 550px;

}
#flashHolder .alert {
	margin-bottom: 0;
	box-shadow: 0px 4px 58px -16px rgba(0,0,0,0.75);
}

.btn-p {
	padding: 11px 45px;
}
.btn-xs, .btn-group-xs > .btn {
	padding: 1px 5px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px;
}
/*.btn-sm {
	padding: 0.25rem 0.5rem !important;
	font-size: 0.76563rem;
	line-height: 1.5;
	border-radius: 0.2rem;
}*/
.br-mainpanel {
	margin-top:0;
	padding-top: 60px !important;
}

.ckbox span:after {
	font-size:9.5px;
}

/*.tablesaw-bar {
	display: none;
}*/

@media (max-width: 1180px) {
	.tablesaw-bar {
		display: block;
		background: #f9f9f9;
		height: 32px;
		padding-right: 7px;
	}
	/*.card-body,.card {
		overflow: auto;
	}*/
	/*.datagrid .datagrid-toolbar {
		float:left;
	}*/
	.datagrid .datagrid-toolbar > span {
		margin-left: 0;
	}
}

.datagrid .datagrid-tree .datagrid-tree-item .datagrid-tree-item-content .datagrid-tree-item-right .datagrid-tree-item-right-actions {
	min-width: 150px;
	justify-content: flex-end;
}

.ui-autocomplete-loading {
	background:url("../img/spinner.gif") no-repeat center right;
	background-size: contain;
}

.datagrid table thead tr .bootstrap-select:not([class*=col-]):not(.input-group-btn) > .btn, .datagrid table thead tr .bootstrap-select:not([class*=col-]):not(.input-group-btn) {
	width:100%;

}

[aria-disabled='true'].select2-results__option {
	background: #eaeaea;
}

.datagrid .datagrid-tree .datagrid-tree-item .datagrid-tree-item-content .datagrid-tree-item-left > .chevron {
	line-height: 23px;
}

.select2-dropdown{
	z-index: 999!important;
}
.select2-container {
	width: 100% !important;
}

select.select2.is-invalid + span.select2 > span.selection > span.select2-selection {
	border-color: #DC3545 !important;
}
select.select2.is-warning + span.select2 > span.selection > span.select2-selection {
	border-color: #F49917 !important;
}

.ckbox span {
	padding-left:0;
}

.ui-autocomplete, .ui-timepicker-standard {
	z-index: 10006 !important;
}

.fc-unthemed .fc-content {
	color:#FFF;
}


.modal-body .select2-container {
	z-index: 9999;
}
.modal-backdrop {
	z-index: 554;
}
.modal {
	z-index: 555;
}

.datagrid .select2-container--default .select2-selection--multiple,.datagrid .select2-container--default  {
	min-height: auto;
}
.datagrid .select2-container--default .select2-search--inline .select2-search__field{
	line-height: 14px !important;
}
.datagrid .select2-container--default .select2-selection--multiple .select2-selection__choice {
	font-size: 9px!important;
	margin-top: 3px;
	padding: 2px 10px 2px 20px;
}
.datagrid .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
	top:1px;
}

.datagrid-inline-edit .select2-container--default .select2-selection--single {
    font-size: 10px!important;
    margin-top: 0px;
    padding: 2px 10px 2px 0px;
    height: calc(1.65rem + 2px);
}
.datagrid-inline-edit .select2-container--default .select2-selection--single .select2-selection__arrow {
    width: 30px;
    height: calc(1.65rem + 2px);
    line-height: calc(1.65rem + 2px);
}

.image-mapper {
	position: relative;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none
}

.image-mapper-img {
	max-width: 100%;
	max-height: 100%
}

.image-mapper-svg {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	height: 100%;
	width: 100%
}


#image-map-wrapper {
	text-align: center
}

#image-map-container {
	display: inline-block;
	border: 2px solid #DDD;
	padding: 2px;
	border-radius: 3px;
	max-width: 100%
}

#image-map {
	display: inline-block;
	max-width: 100%
}

@-ms-keyframes spin {
	from {
		-ms-transform: rotate(0deg)
	}
	to {
		-ms-transform: rotate(360deg)
	}
}

@-moz-keyframes spin {
	from {
		-moz-transform: rotate(0deg)
	}
	to {
		-moz-transform: rotate(360deg)
	}
}

@-webkit-keyframes spin {
	from {
		-webkit-transform: rotate(0deg)
	}
	to {
		-webkit-transform: rotate(360deg)
	}
}

@keyframes spin {
	from {
		transform: rotate(0deg)
	}
	to {
		transform: rotate(360deg)
	}
}

.help-block-meeting {
	font-size: 11px;
	color: #000;
	margin-left: 22px;
	margin-top: -15px;
}
.presentation-meeting {
	margin-left: 22px;
	font-size: 12px;
	z-index: 999;
	position: relative;;
}
.w-90 {
	width: 90%;
}
.w-10 {
	width: 10%;
}

.table-light {
	opacity:0.5;
}

.copy-tooltip {
	position: relative;
	display: inline-block;
}

.copy-tooltip .tooltiptext {
	visibility: hidden;
	width: 140px;
	background-color: #555;
	color: #fff;
	text-align: center;
	border-radius: 6px;
	padding: 5px;
	position: absolute;
	z-index: 1;
	bottom: 120%;
	left: 50%;
	margin-left: -75px;
	opacity: 0;
	transition: opacity 0.3s;
}

.copy-tooltip .tooltiptext::after {
	content: "";
	position: absolute;
	top: 100%;
	left: 50%;
	margin-left: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: #555 transparent transparent transparent;
}

.copy-tooltip:hover .tooltiptext {
	visibility: visible;
	opacity: 1;
}

li.select2-results__option strong.select2-results__group:hover {
	background-color: #ddd;
	cursor: pointer;
}


#ajax-spinner {
	visibility: hidden;
	position: fixed;
	top:0;
	bottom:0;
	right:0;
	left:0;
	padding: 3px;
	background: rgba(255,255,255,0.90);
	z-index: 123456;
	text-align: center;
	font-size: 22px;
}
#ajax-spinner img {
	width:50px;
	margin-top:20%;
	margin-bottom:15px;
}

@media (max-width: 991px) {
	.fc-toolbar.fc-header-toolbar {
		display: block;
		text-align: center;
	}
	.fc-center {
		margin:.75rem 0;
	}
}
.form-control-sm + .select2-container--default .select2-selection--single,
.form-control-sm + .select2-container--default .select2-selection--single .select2-selection__arrow
{
	height:30px;
}


.card.article img {
	max-width: 100%;
	height: auto;
}

.square-15 {
	 display: inline-block;
	 height: 17px;
	min-width: 17px;
	padding: 0 5px;
 }

#onlineRefresh {
	color:#666;
	text-decoration: underline;
}
#onlineRefresh:hover {
	color:#fff;
	text-decoration: none;
}

.br-sideright-open {
	right:0;
	top:60px;
}
.br-sideright-open-main {
	margin-right: 280px;
}
.show-right-open .br-sideright {
	right:-280px;
}

.modal {
	top: 40px;
}

.col-md-2_5 {
	position: relative;
	width: 100%;
	min-height: 1px;
	padding-right: 15px;
	padding-left: 15px;
}

.modal-dialog-wide {
	max-width: 900px;
	margin: 1.75rem auto;
}
.modal-dialog-semiwide {
	max-width: 700px;
	margin: 1.75rem auto;
}
@media (min-width: 768px) {
	.col-md-2_5 {
		flex: 0 0 20%;
		max-width: 20%;
	}

}

@media (max-width: 450px) {
	#flashHolder {
		min-width: 92%;
		max-width: 92%;
	}

}

.card-header-tabs-gray .nav-link.active, .card-header-tabs-gray .nav-link.active:hover, .card-header-tabs-gray .nav-link.active:focus {
	background: #e9ecef !important;
	border-bottom-color: transparent !important;
	/*border-bottom: none;*/
}

.documentViewer-sortable-placeholder {
	background-color: rgb(253, 249, 226);
	height: 30px;
}

.documentViewer, .documentViewer-panels {
	color:#000;
}

.documentViewer-filename, .documentViewer-panels-filename {
	font-size: 12px;
}
.documentViewer-filename {
	min-height: 40px;
}
.documentViewer-panels-filename {
	font-weight: 600;
}
.documentViewer-filesize, .documentViewer-panels-filesize {
	font-size:11px;
	color:#ccc;
}
.documentViewer-extension, .documentViewer-panels-extension {
	color:#888888;
	font-size:11px;
	margin-right: .5rem;
}

.documentViewer-panels-icon,.documentViewer-panels-image {
	min-height: 58px;
}

.documentViewer-panels img {
	max-height: 58px;
}
.documentViewer img {
	max-height: 102px;
}
.documentViewer-list-image img {
	max-height: 16px;
	width: auto;
}
.documentViewer-list-icon {
	min-width: 16px;

}

/*.documentViewer-image {
	margin-top:1.25rem;
}*/

.form-control-xs {
	height: calc(1.14844rem + 2px);
	padding: 0.15rem 0.25rem;
	font-size: 0.66563rem;
	line-height: 1.5;
	border-radius: 0.2rem;
}

.documentViewer-actions {
	background: #ced4da;
	margin-bottom: .5rem;
	text-align: right;
	border-bottom: 3px solid #ced4da;
}

.br-header-right .dropdown:last-child .dropdown-menu {
	padding: 10px 20px 25px;
	width: 330px;
}

.navicon-right .dropdown-menu a {
	font-size:12px;
	color:#666666;
}
.navicon-right .dropdown-menu a.active {
	color:#FFF;
}

@media (max-width: 768px) {
	.navicon-right {
		width: 50px;
	}
}

.unread-circle:hover span {
	border:3px solid #0866C6;
}

.hidden-content {
	overflow: hidden;
	height: 3em;
}

.visible-content {
	height: auto;
}

.show-more-container {
	position: relative;
	overflow:hidden
}
.show-more-button {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: url(../img/gradient.png) bottom
}

.input-group  + .invalid-feedback {
	display:block;
}
/*
@media (max-width: 768px) {
	.table-mobile, .datagrid .table {
		min-width: 768px;
		position: relative;
	}


	.table-mobile tr > th,
	.datagrid .table tr > th {
		z-index: 1;
	}


	.table-mobile tr > td:first-child,
	.table-mobile tr > th:first-child,
	.datagrid .table tr > td:first-child,
	.datagrid .table tr > th:first-child
	{
		position:sticky;
		left:0;
		background: #eaeaea;
		z-index: 2;
	}
	.table-mobile tr > th:first-child,
	.table-mobile tfoot > tr > td:first-child,
	.datagrid .table tr > th:first-child,
	.datagrid .table tfoot > tr > td:first-child
	{
		background: #FFF;
	}
	.datagrid {
		padding:0;
	}
	.datagrid .table {
		border: 0 !important;
	}
}*/


.datagrid .datagrid-row-inline-add.datagrid-row-inline-add-hidden {
    display: table-row;
}

.datagrid {
	padding:0;
}

.sidebar-label {
    padding: 15px 12px;
    margin:0 !important;;
}

label + div.invalid-feedback {
	display:block!important;
}

.modal-header {
	align-items: center !important;
}
.modal-header .close {
	color:red;
}


select[readonly].select2-hidden-accessible + .select2-container {
	pointer-events: none;
	touch-action: none;
}

select[readonly].select2-hidden-accessible + .select2-container .select2-selection {
	background: #eee;
	box-shadow: none;
}

select[readonly].select2-hidden-accessible + .select2-container .select2-selection__clear
/*,select[readonly].select2-hidden-accessible + .select2-container .select2-selection__arrow*/  {
   display: none;
}
.fc-content {
	position: relative;
	/*z-index: 999 !important;*/
}
.fc-content > a > i {
	color:#FFF;
}

.fc-event-menu {
	z-index: 9;
	background: #FFF;
	box-shadow: 0px 0px 5px 5px rgba(0,0,0, 0.2);
	color:#000 !important;
	right:0;
	padding:4px;
	margin-top:1px;
	/*border-radius: 8px;*/
	font-size: 10px;
	width:auto;
	text-align: right;
	max-width: 80px;
}
.fc-day-grid .fc-event-menu {
	width:180px;
}

.fc-event-container .fc-completed {

	background: rgba(0,0,0,0.1) !important;
	border-color: rgba(0,0,0,0.1) !important;

}
.fc-event-container .fc-completed .fc-content .fc-title,.fc-event-container .fc-completed .fc-content .fc-time, .fc-event-container .fc-completed .fc-content > a > i  {
	color: #000!important;;
	text-decoration: line-through !important;
}

.fc-list-item.fc-completed {
	opacity: 0.8;
	background: #ECECEC;
	text-decoration: line-through;
}
/*.fc-completed:hover,*/ .fc-unthemed .fc-list-item.fc-completed:hover td {
							 background: #ececec !important;
						 }

.fc-event-container .fc-event[href]:hover {
	background-color: #FFF !important;

}
.fc-event-container .fc-event[href]:hover .fc-content,.fc-event-container .fc-event[href]:hover .fc-content > a > i {
	color:#000 !important;
}

.user-profile-nav a.active {
	background: #eaeaea;
}
.br-mailbox-body {
	margin-left:420px;
	margin-top:0;
}

.br-mailbox-list-item::before {
	content: '';
	position: absolute;
	top: 50%;
	bottom: 50%;
	right: 0;
	left: auto;
	width: 5px;
	background-color: #adb5bd;
	opacity: 0;
	transition: all 0.2s ease-in-out;
}

.br-mailbox-list-item.active::before, .br-mailbox-list-item.active:hover::before, .br-mailbox-list-item.active:focus::before {
	background-color: #17A2B8;
	top: -1px;
	bottom: -1px;
	opacity: 1;
}

.br-mailbox-list-item.active::before, .br-mailbox-list-item.active:hover::before, .br-mailbox-list-item.active:focus::before {
	background-color: #0866C6;
}

.br-mailbox-list {
	width: 420px;
	padding-bottom: 60px;
}
.br-mailbox-list-actions {
	display:none;
	position: absolute;
	right:5px;
	top:27px;
	/*bottom:0;*/
	background: #f2f4f6;
}
.br-mailbox-list-item.unread .br-mailbox-list-actions, .br-mailbox-list-item.active .br-mailbox-list-actions  {
	background: #FFF;
}
.br-mailbox-list-item:hover > .br-mailbox-list-actions {
	display: block;;
}


.br-mailbox-list-item.unread, .br-mailbox-list-item.unread:hover, .br-mailbox-list-item.unread:focus {

}

.br-mailbox-list-item.closed {
	background: #dcf5d7;
}
.br-mailbox-list-paginator {
	position: fixed;
	bottom:0;
	left:230px;
	width:419px;
	z-index: 9999;
	background: #FFF;
}

/* modal backdrop fix */
/*.modal:nth-of-type(even) {
	z-index: 10052 !important;
}
.modal-backdrop.show:nth-of-type(even) {
	z-index: 10051 !important;
}*/

.bg-success-shape {
	background: linear-gradient(90deg, rgba(35,191,8,1) 0%, rgba(35,191,8,1) 40%, rgba(255,255,255,1) 40%, rgba(255,255,255,1) 60%, rgba(35,191,8,1) 60%, rgba(35,191,8,1) 100%);
}
.bg-success-shape-rotate {
	background: linear-gradient(140deg, rgba(35,191,8,1) 0%, rgba(35,191,8,1) 15%, rgba(255,255,255,1) 15%, rgba(255,255,255,1) 30%, rgba(35,191,8,1) 30%, rgba(35,191,8,1) 100%);
}

.bg-danger-shape-rotate {
	background: linear-gradient(140deg, #DC3545 0%, #DC3545 15%, rgba(255,255,255,1) 15%, rgba(255,255,255,1) 30%, #DC3545 30%, #DC3545 100%);
}

.bg-warning-shape-rotate {
	background: linear-gradient(140deg, #F49917 0%, #F49917 15%, rgba(255,255,255,1) 15%, rgba(255,255,255,1) 30%, #F49917 30%, #F49917 100%);
}

.bg-black {
	background: #000;
}

.bg-black-success {
	background: linear-gradient(90deg, rgba(35,191,8,1) 35%, rgba(0,0,0,1) 65%);
}

.own-option {
	background: #bad4ef;
}

.br-menu-sub .sub-link-domain {
	display: block;
	font-size: 13px;
	color: #adb5bd;
	padding: 7px 0 7px 2px;
	white-space: nowrap;
	position: relative;
	transition: all 0.2s ease-in-out;
}
.br-menu-sub .sub-link-domain:hover, .br-menu-sub .sub-link-domain:focus {
	color: #17A2B8;
}

.fi-en{
	background-image:url(https://cdn.jsdelivr.net/gh/lipis/flag-icons@6.6.6/flags/4x3/gb.svg)
}
.fi-en.fis{
	background-image:url(https://cdn.jsdelivr.net/gh/lipis/flag-icons@6.6.6/flags/1x1/gb.svg)
}

.br-menu-sub .sub-link {
	white-space: normal;
}

.br-toggle + div {
	margin-left: 0.5rem;
	color:#000;
}
.br-toggle.on + div {
	font-weight: bold;
}
.br-toggle.br-toggle-success.on + div {
	color:#23BF08;
}
.br-toggle.br-toggle-warning.on + div {
	color:#F49917;
}

/*input.datetime {
	position: relative;
}*/
.tempus-dominus-widget.show {
	z-index: 10000;
}

@media (max-width: 479px) {
	.br-header-right .dropdown:last-child .dropdown-menu {
		transform: none !important; /*translateX(145px) !important;*/
	}
}

.table-disabled td {
	background-color: #e9ecef;
	color: rgba(148, 161, 174, 0.75);
}

.btn-with-icon.dropdown-toggle{
	display:flex!important;
}

.btn-with-icon.dropdown-toggle:after{
	display:block;
	margin: 1.1rem .5rem 1.1rem auto;
}

.invoice-wrapper .table thead th {
	vertical-align: middle !important;
}
.collapsed-menu .br-sideleft {
	z-index: 10001!important;
}

.ac-item {
	margin: .15rem;
	display: block;
	padding:.5rem;
}
.ac-item:hover {
	background:#ebf8ff ;
}

.ac-item .company-name {
	color: #111419;
	font-size: 16px;
}
.ac-item .additional_info {
	color:#bababd
}

.ui-autocomplete {
	max-height: 300px;
	overflow-y: auto;
	/* prevent horizontal scrollbar */
	overflow-x: hidden;
	/* add padding to account for vertical scrollbar */
	padding-right: 20px;
}

[data-dropArea].highlight {
	border-color: #0866C6 !important;
	border-style: dotted;
}
[data-dropArea] input[type='file'] {
	display: none;
}
[data-dropArea-progress="0"] progress {
	display: none;
}


@media (min-width: 992px) {
	.without-menu .br-mainpanel {
		margin-left: 0px;
	}
	.without-menu .br-header-left > *,
	.without-menu .br-sideleft {
		display: none;
	}

	.without-menu .br-logo {
		left:0;
		border-right: none;
	}
}

select[class*='select-flag-']{
	background-size: 16px;
	background-repeat: no-repeat;
	/*background-position-y: center;
	background-position-x: 5px;*/
	background-position: right 20px center;
	padding-right:30px;
}

.select-flag-sk, select[name="lang"] > option[value="sk"] {
	background: url(../img/flags/sk.svg);
}
.select-flag-en, select[name="lang"] > option[value="en"] {
	background: url(../img/flags/en.svg);
}
.select-flag-de, select[name="lang"] > option[value="de"] {
	background: url(../img/flags/de.svg);
}

.select-flag-pl, select[name="lang"] > option[value="pl"] {
	background: url(../img/flags/pl.svg);
}
.select-flag-cz, select[name="lang"] > option[value="cz"] {
	background: url(../img/flags/cz.svg);
}
.select-flag-hu, select[name="lang"] > option[value="hu"] {
	background: url(../img/flags/hu.svg);
}
.select-flag-nl, select[name="lang"] > option[value="nl"] {
	background: url(../img/flags/nl.svg);
}




.form-label-group > input.tx-bold {
	border-color:#000;
}

.help-block {
	font-size: 11px;
	margin-left:1rem;
	padding-top:2px;
}

.spinner {
	font-size: 20px;
	color: #343a40;
	font-weight: 700;
	text-align: center ;
	padding-bottom: 3rem ;
	padding-top: 3rem ;
}

.scan-qr-container .spinner {
	font-size: 16px;
	padding-bottom: 1.75rem;
	padding-top: 1.25rem;
}

.ajax-modal-spinner{
	position: absolute;
	top: 1rem;
	bottom: 1rem;
	left: 1rem;
	right: 1rem;
	z-index: 99999;
	background: rgba(0,0,0,0.2);
	border-radius:5px;
	display: flex;
	justify-content: center;
	align-items: center;
}