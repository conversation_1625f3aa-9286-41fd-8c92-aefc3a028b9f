<?php
declare(strict_types=1);
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Components;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Utils\DateTime;
use Nette\Database\Row;
use Nette\Security\User;
use Webtec\Models\MailingModel;
use Webtec\Models\MailingSendModel;
use Webtec\Models\UserModel;

abstract class BaseWarning implements Warning {
	protected User $user;
	protected ?Row $company;
	protected MailingModel $mailingModel;
	protected MailingSendModel $mailingSendModel;
	protected array $parameters;
	protected bool $fatal = false;
	protected array|string|null $notify = null;

	public abstract function isVisible() : bool;
	public abstract function getMessage(): string;

	public function getIcon() : ?string
	{
		return 'fas fa-exclamation-triangle';
	}
	public function getClass() : string
	{
		return "alert alert-danger";
	}
	public function getName() : string
	{
		return static::class;
	}
	public function getTitle() : ?string
	{
		return null;
	}
	public function getLink() : ?string
	{
		return null;
	}


	public function setUser( User $user ): void
	{
		$this->user = $user;
	}

	/**
	 * @param Row|null $company
	 */
	public function setCompany( ?Row $company ): void
	{
		$this->company = $company;
	}

	/**
	 * @param array $parameters
	 */
	public function setParameters( array $parameters ): void
	{
		$this->parameters = $parameters;
	}

	public function setMailingSendModel(MailingSendModel $mailingSendModel): void
	{
		$this->mailingSendModel = $mailingSendModel;
	}

	public function setMailingModel(MailingModel $mailingModel): void
	{
		$this->mailingModel = $mailingModel;
	}

	/**
	 * @return bool
	 */
	public function isFatal(): bool {
		return $this->fatal;
	}

	public function notify():void {
		if($this->notify !== null){

			$exists = $this->mailingModel->findBy(["subject" => $this->getTitle()])->count("id");
			if(!$exists){
				bdump($this->getTitle());
				$data = [
					"datetime" => new DateTime(),
					"subject" => $this->getTitle(),
					"text" => $this->getMessage(),
					"to" => MailingModel::TYPE_USERS,
					"from" => $this->parameters["app"]["defaultSmtp"]["username"], // "<EMAIL>",
					"attachments_dir" => time(),
					"visibility" => 1,
					"type" => UserModel::MARKETING_REMINDER,
				];

				$mailing = $this->mailingModel->insert($data);

				if(is_string($this->notify)){
					$this->notify = [$this->notify];
				}
				$mails = [];
				foreach ($this->notify as $email){
					$mails[] = ["mailing_id" => $mailing->id, "email" => $email];
				}
				$this->mailingSendModel->insert($mails);
			}
		}

	}



}