<?php declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Components;

use Contributte\Mailing\IMailBuilderFactory;

final class SmtpWarning extends BaseWarning {

	public function __construct(private IMailBuilderFactory $mailBuilderFactory) {

	}

	public function isVisible() : bool {
		return ($this->company !== null &&
		                 $this->company->id > 1 &&
		                 $this->user->getAuthorizator()->isSuperadmin()  &&
		                 $this->parameters["app"]["defaultSmtp"]["username"] === $this->mailBuilderFactory->getDefaultSmtpEmail());
	}

	public function getTitle(): ?string {
		return "Nastavenie e-mailov";
	}

	public function getMessage(): string {
		return sprintf('Aktuálne nemáte nastavený e-mailový účet na odosielanie správ. Všetky správy sa pošlu z e-mailu <strong>%s</strong>', $this->parameters["app"]["defaultSmtp"]["username"]);
	}

	public function getLink() : ?string
	{
		return ":Backend:Setting:Config:smtp";
	}



}