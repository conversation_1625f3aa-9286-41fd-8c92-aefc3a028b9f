<?php

namespace Webtec\UI\Forms\Controls;

use Nette\Utils\DateTime,
	Nette\Forms\Controls\TextInput;
use Nette\Utils\Html;

class Date extends TextInput
{
	public $format = "d.m.Y";
	public $class = "datepicker form-control";


	/**
	 * @param string $format
	 */
	public function setFormat( string $format ): void {
		$this->format = $format;
	}
    /**
     * @return mixed
     */
    public function getValue()
    {
		try {
			return $this->value ? DateTime::from($this->value) : NULL;

		} catch (\RuntimeException $e) {
			return NULL;
		}
    }



    /**
     * @param string|\DateTime
     * @return void
     */
    public function setValue($value)
    {
		if ($value instanceof \DateTime) {
			$value = $value->format($this->format);

		} else if ($value) {
			$value = date($this->format, strtotime($value));
		}

		return parent::setValue($value);
    }



    /**
     * @return \Nette\Utils\Html
     */
    public function getControl(): Html
    {
		$control = parent::getControl();
		$control->addAttributes(["class" => $this->class, "autocomplete" => "off"]);
		$control->setAttribute("placeholder","DD.MM.YYYY");
		#$control->type = 'date';
		#$control->data('datepicker', 'true');
		return $control;
    }

	/**
	 * @param string $class
	 *
	 * @return Date
	 */
	public function setClass( string $class ): Date {
		$this->class = $class;

		return $this;
}
}
