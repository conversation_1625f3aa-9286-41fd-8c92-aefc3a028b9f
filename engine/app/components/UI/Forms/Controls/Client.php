<?php

namespace Webtec\UI\Forms\Controls;

use BackendModule\BasePresenter;
use Nette\Application\UI\Presenter;
use Nette\Forms\Container;
use Nette\Forms\Controls\BaseControl;
use Nette\Utils\Html,
	Nette\Forms\Controls\TextInput;
use Nette\Utils\Json;
use Webtec\UI\Control;

class Client extends TextInput
{
	protected $fill = [];

	/**
	 * Client constructor.
	 *
	 * @param null $label
	 * @param array $fill
	 */
	public function __construct($label = NULL, array $fill = [])
	{
		parent::__construct($label);
		$this->fill = $fill;
		//$this->setType("hidden");
		$this->setHtmlType("hidden");
	}



	/**
	 * @return int
	 */
	public function getValue()
	{
		return (int) parent::getValue();
	}



	/**
	 * @return \Nette\Utils\Html
	 */
	public function getControl() :Html
	{
		$control = parent::getControl();
		$container = Html::el();
		$container->addHtml($control);
		/** @var BasePresenter $presenter */
		$presenter = $this->lookup(Presenter::class);
		//bdump($control);
		$template = $presenter->createTemplate();
		$template->setFile(__DIR__ . '/templates/client.latte');
		$template->control = $this;
		$template->input = $control;
		$template->client = $presenter->modelFactory->create("user")->findBy(["id" => (int) $this->getValue()])->fetch();

		$components = $components_json = [];
		/** @var BaseControl $component */
		foreach($this->lookup(Container::class)->getComponents() as $component){
			if(in_array($component->getName(), $this->fill)) {
				$components[ $component->getName() ] = $component->getHtmlId();
				$components_json[]                   = [
					"name" => $component->getName(),
					"id"   => $component->getHtmlId(),
					"type" => $component->getControlPrototype()->type
				];
			}
		}
		//bdump($components_json);
		$template->components = $components;
		$template->components_json = Json::encode($components_json);
		//$template->customer = $this->getForm()->getPresenter()->getContext()->modelFactory->create('customer')->findBy(['id' => (int) $this->getValue()])->select('id,email')->fetch();
		$container->addHtml((string) $template);

		return $container;
	}
}
