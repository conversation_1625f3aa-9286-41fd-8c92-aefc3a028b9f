{varType Nette\Utils\Html $input}
<div class="select-remove-value">
    <a href="javascript:;" style="{if $control->getValue() === null}display:none{/if}" data-selectbox="{$input->getAttribute("id")}" class="tx-gray-600">
        <i class="fas fa-times"></i>
    </a>
</div>
<script>
    $(document).on("click","a[data-selectbox='"+ {$input->getAttribute("id")} +"']", function (){
		let id = $(this).data("selectbox");
		let $form = document.getElementById({$control->getForm()->getElementPrototype()->getAttribute("id")});
		$("#" + id).val('').change();
		$(this).hide();
		Nette.validateForm($form);
	})
</script>