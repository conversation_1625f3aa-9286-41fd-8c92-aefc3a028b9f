<i class="fas fa-eye-slash" id="{$input->getAttribute("id")}-show"></i>
<script>
	var togglePassword = document.querySelector("#" + {$input->getAttribute("id")} + "-show");
	var password = document.querySelector("#" + {$input->getAttribute("id")});
	togglePassword.addEventListener("click", function () {
		// toggle the type attribute
		const type = password.getAttribute("type") === "password" ? "text" : "password";
		password.setAttribute("type", type);
        if(type === "password"){
	        this.classList.remove("fa-eye");
	        this.classList.add("fa-eye-slash");
        } else {
	        this.classList.remove("fa-eye-slash");
	        this.classList.add("fa-eye");
        }
	});
</script>