{? $selected = false}
<select id="{$input->id}-options" class="form-control">
	<optgroup label="{_'Choose option'}" n:inner-foreach="$items as $key => $item">
		<option n:if="$item" n:attr="'selected' => $item == $input->value" value="{$item}">{$item}</option>
		{if $item == $input->value}{? $selected = true}{/if}
	</optgroup>
	<optgroup label="---">
		<option value="" n:attr="'selected' => ($input->value && !$selected) || (count($_POST) && !in_array($input->value, $items))">{_'Custom option'}</option>
	</optgroup>
</select>

{$input}

<script>
//<![CDATA[
{? $cName = $control->form->name . '-' . $control->name}
define({$cName}, [], { });

require([{$cName}, "../backend"], function() {
	require(["jquery", {$cName}], function($) {
		var input = $("#" + {$input->id});
		var item = $("#" + {$input->id} + "-options");
		var options = {$items};

		{if $input->value && !array_key_exists($input->value, $items) && !$selected}
		input.prop("type", "text").val({$input->value}).show(0);
		{/if}

		if (item.val().length != 0) {
			input.val(item.val());
		}

		item.on("change", function () {
			if (item.val().length == 0) {
				if (input.prop("type") == "hidden") {
					input.val("");
				}
				input.prop("type", "text");
			} else {
				input.prop("type", "hidden");
				input.val(item.val());
			}
		});
	});
});
//]]>
</script>
