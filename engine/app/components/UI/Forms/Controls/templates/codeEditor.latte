{*hp dump(get_defined_vars())*}

{var $editorId = $input->getAttribute("id")."-ace"}
{*var $editorJsVariable = "editor_".md5($input->getAttribute("id"))*}
{*php dump(get_defined_vars())*}
<div class="position-relative" style="min-height: 550px">
    <pre id="{$editorId}">{*php echo htmlspecialchars($editor)*}</pre>
</div>

<style>
	#{$editorId} {
		margin: 0;
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
	}
</style>
<script>
	ace.require("ace/ext/language_tools");
	var {$editorJsVariable|noescape} = ace.edit({$editorId});
    {$editorJsVariable|noescape}.setTheme("ace/theme/twilight");
    {$editorJsVariable|noescape}.session.setMode({$mode});
    {$editorJsVariable|noescape}.setOptions({
		enableBasicAutocompletion: true,
		enableSnippets: true,
		enableLiveAutocompletion: true,
		minLines: 15,
		autoScrollEditorIntoView: true
	});
    {$editorJsVariable|noescape}.getSession().setValue($("#" + {$input->getAttribute("id")}).val());
    {$editorJsVariable|noescape}.getSession().on("change",function (){
		$("#" + {$input->getAttribute("id")}).val({$editorJsVariable|noescape}.getSession().getValue());
	});
</script>