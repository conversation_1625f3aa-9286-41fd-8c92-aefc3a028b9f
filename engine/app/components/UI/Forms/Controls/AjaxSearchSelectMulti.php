<?php

namespace Webtec\UI\Forms\Controls;



use Nette\Forms\Controls\MultiSelectBox;
use Nette\Utils\Json;

class AjaxSearchSelectMulti extends MultiSelectBox {

	protected string $metadataKey;
	protected string $class = 'select2-ajax form-control';

	public function __construct( $label = null, string $metadataKey = null ) {
		parent::__construct( $label, [] );
		if($metadataKey === null){
			throw new InvalidArgumentException("Argument #2 (metadata) must be specified.");
		}
		$this->metadataKey = $metadataKey;
		$this->getControlPrototype()->addAttributes(["class" => $this->class]);
		$this->getControlPrototype()->addAttributes(["data-metadata-key" => $this->metadataKey]);
		$this->checkDefaultValue(false);
	}

	public function getValue(): array {
		return $this->value;
	}

	/**
	 * @param string $class
	 *
	 * @return AjaxSearchSelect
	 */
	public function setClass( string $class ): AjaxSearchSelect {
		$this->class = $class;
		$this->getControlPrototype()->setAttribute("class", $this->class);
		return $this;
	}
}