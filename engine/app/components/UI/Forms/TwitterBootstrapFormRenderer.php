<?php

namespace Webtec\UI\Forms;

use Nette,
	Nette\Utils\Html;

class TwitterBootstrapFormRenderer extends Nette\Forms\Rendering\DefaultFormRenderer
{
	/** @var array of HTML tags */
	public $wrappers = [
		'form' => [
			'container' => NULL,
		],

		'error' => [
			'container' => 'div class="alert alert-danger"',
			'item' => 'p',
		],

		'group' => [
			'container' => 'fieldset',
			'label' => 'legend',
			'description' => 'p',
		],

		'controls' => [
			'container' => NULL,
		],

		'pair' => [
			'container' => 'div class=form-group',
			'.required' => 'required',
			'.optional' => NULL,
			'.odd' => NULL,
		],

		'control' => [
			'container' => 'div class=col-lg-5',
			'.odd' => NULL,

			'description' => 'p class=help-block',
			'requiredsuffix' => NULL,
			'errorcontainer' => 'div class=has-error',
			'erroritem' => 'strong class=text-danger',

			'.required' => 'required',
			'.text' => 'form-control',
			'.password' => 'form-control',
			'.file' => 'form-control',
			'.submit' => 'btn btn-primary',
			'.image' => 'imagebutton',
			'.button' => 'btn btn-primary',
		],

		'label' => [
			'container' => 'div class="control-label col-lg-2"',
			'suffix' => NULL,
			'requiredsuffix' => NULL
		],

		'hidden' => [
			'container' => 'div',
		],
	];



	/**
	 * @param \Nette\Forms\IControl
	 * @return string
	 */
	public function renderPair(Nette\Forms\Control $control) : string
	{
		$pair = $this->getWrapper('pair container');
		$pair->add($this->renderLabel($control));
		$pair->add($this->renderControl($control));
		$pair->class($this->getValue($control->isRequired() ? 'pair .required' : 'pair .optional'), TRUE);

		if (++$this->counter % 2) {
			$pair->class($this->getValue('pair .odd'), TRUE);
		}

		$pair->id = $control->getOption('id');
		return $pair->render(0);
	}



	/**
	 * @param \Nette\Forms\IControl
	 * @return string
	 */
	public function renderLabel(Nette\Forms\Control $control) : Html
	{
		$suffix = $this->getValue('label suffix') . ($control->isRequired() ? $this->getValue('label requiredsuffix') : '');
		$label = $control->getLabel();
		if ($label instanceof Html) {
			$label->add($suffix);
		} elseif ($label != NULL) { // @intentionally ==
			$label .= $suffix;
		}

		// tooltip helpers
		$tooltip = $control->getOption('tooltip');
		if ($tooltip && $label instanceof Html) {
			$label->data('container', 'body')
				->data('toggle', 'tooltip')
				->data('placement', 'top')
				->title($tooltip);
			$label->add(Html::el(NULL, ' '));
			$label->add(Html::el('i')->class('glyphicon glyphicon-question-sign'));
		}

		// popover helpers
		$popover = $control->getOption('popover');
		if ($popover && $label instanceof Html) {
			$label->data('container', 'body')
				->data('toggle', 'popover')
				->data('trigger', 'hover')
				->data('html', 'true')
				//->data('title', $control->caption)
				->data('placement', 'top')
				->data('content', $popover);
			$label->add(Html::el(NULL, ' '));
			$label->add(Html::el('i')->class('glyphicon glyphicon-question-sign'));
		}

		return $this->getWrapper('label container')->setHtml($label);
	}



	/**
	 * @param \Nette\Forms\IControl
	 * @return string
	 */
	public function renderControl(\Nette\Forms\Control $control) : Html
	{
		$body = $this->getWrapper('control container');

		if ($control instanceof Nette\Forms\Controls\TextArea) {
			$body->class = NULL;
			$body->class('col-lg-10', true);
		}

		if ($this->counter % 2) {
			$body->class($this->getValue('control .odd'), TRUE);
		}

		$description = $control->getOption('description');
		if ($description instanceof Html) {
			$description = ' ' . $description;

		} elseif (is_string($description)) {
			$description = ' ' . $this->getWrapper('control description')->setText($control->translate($description));

		} else {
			$description = '';
		}

		if ($control->isRequired()) {
			$description = $this->getValue('control requiredsuffix') . $description;
		}

		$el = $control->getControl();
		if ($el instanceof Html && $el->getName() === 'input') {
			$el->class($this->getValue("control .$el->type"), TRUE);
		}
		if ($control instanceof Nette\Forms\Controls\Checkbox) {
			#$body->class('checkbox', TRUE);
			$wrapper = Html::el('div')->class('checkbox');
			$el = $wrapper->add($el);
		} else if ($control instanceof Nette\Forms\Controls\Radiolist) {
			$wrapper = Html::el('div')->class('radio');
			$el = $wrapper->add($el);
			#$body->class('radio', TRUE);
		} else if (!isset($el->attrs['class']) || empty($el->attrs['class'])) {
			$el->class('form-control');
		}

		return $body->setHtml($el . $description . $this->renderErrors($control));
	}
}
