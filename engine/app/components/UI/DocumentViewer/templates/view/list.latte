<div class="row no-gutters align-items-center justify-content-between py-1 bd-b">
    <div class="col-auto col-lg-auto order-0" n:if="$control->isSortable() ">
                                <span class="handle-sort px-3 py-1 tx-primary" style="cursor: s-resize" n:if="!$item->isDir()">
                                    <i class="fas fa-arrows-v fa-arrows-alt-v"></i>
                                </span>
        <div style="width: 39px;"n:if="$item->isDir()"></div>
    </div>
    <div class="col-auto col-lg-auto order-1">
        <div class="text-center documentViewer-list-icon d-flex justify-content-center align-items-center" n:ifset="$item->icon">
            <i class="{$item->icon} tx-16"></i>
        </div>
        <div class="documentViewer-list-image d-flex justify-content-center align-items-center" n:ifset="$item->image">
            <img src="{$item->image}" title="{$item->fileName}" class="img-fluid m-auto">
        </div>
    </div>
    <div class="col-md px-2 col order-2">
        <a n:tag-if="$control->isShowPreview()" href="{$item->href|nocheck}" data-caption="{$item->fileName}" title="{$item->fileName}" {if $item->isFancyBox()}data-fancybox="gallery"{/if} {ifset $item->target}target="{$item->target}"{/ifset} class="documentViewer-list">
            {$item->name}
        </a>
    </div>
    <div class="col-12 d-block d-md-none order-4"></div>
    <div class="col-md-2 col-auto order-5" n:if="$control->isShowDetails()">
        {$item->date_create|date:"d.m.Y H:i:s"}
    </div>
    <div class="col-md-1 col-auto order-6" n:if="$control->isShowDetails()">
        {if !$item->isDir()}
            {$item->extension}
        {else}
            <strong>DIR</strong>
        {/if}
    </div>

    <div class="col-md-1 text-right col-auto order-7" n:if="$control->isShowDetails()">
        {if !$item->isDir()}
            {$item->size|filesize}
        {/if}
    </div>
    <div class="col-md-1 text-right order-3 order-md-7 col-auto">
        <a n:if="!$item->isDir() && isset($item->downloadLink) && $control->isAllowDownload()" href="{$item->downloadLink|nocheck}" class="tx-14 d-inline-block px-1"><i class="fas fa-download"></i></a>
        <a n:if="!$control->isDirProtected($item) && $control->isAllowDelete() && isset($item->deleteLink)" href="{$item->deleteLink|nocheck}" data-file="{$item->fileName}" class="text-danger tx-14 d-inline-block px-1" data-dv-remove><i class="fas fa-times"></i></a>
    </div>
</div>