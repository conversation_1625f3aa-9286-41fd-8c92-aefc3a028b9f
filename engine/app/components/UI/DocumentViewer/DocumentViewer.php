<?php

namespace <PERSON><PERSON><PERSON><PERSON>ta\Components;


use BackendModule\BasePresenter;
use Google\Exception;
use IPub\FlashMessages\Entities\IMessage;
use <PERSON><PERSON><PERSON><PERSON>ta\DropzoneUploader\DropzoneUploader;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\FileManager\Adapters\S3Adapter;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\FileManager\FileManager;
use Kubo<PERSON>kita\Services\Imgcache;
use Kubo<PERSON>kita\Utils\DateTime;
use Kubomikita\Utils\Strings;
use Nette\Application\IPresenter;
use Nette\Application\Responses\FileResponse;
use Nette\Application\UI\Control;
use Nette\Application\UI\Presenter;
use Nette\Database\Table\ActiveRow;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\InvalidArgumentException;
use Nette\Utils\ArrayHash;
use Nette\Utils\FileSystem;
use Nette\Utils\Finder;
use Nette\Utils\Json;
use Webtec\UI\Forms\Form;

/**
 * @property-read BasePresenter $parent
 */
class DocumentViewer extends Control {

	const VIEW_BIGICON = 2;
	const VIEW_PANELS = 1;
	const VIEW_LIST = 3;

	protected string $templateFile = __DIR__."/templates/documentViewer.latte";
	protected string $templateFilePopup = __DIR__."/templates/popup.latte";
	protected string $documentDir;
	protected array $itemClass = [
		self::VIEW_BIGICON => "col-md-2 col-6",
		self::VIEW_PANELS => "col-xl-3 col-lg-6",
		self::VIEW_LIST => "col-12"
	];

	protected int $imageWidth = 480;
	protected int $imageHeight = 480;
	protected ?int $imageFit = 5;


	protected ?ActiveRow $trade;
	protected ?string $title = null;
	protected $subDir;

	public $popup;
	/** @var Session */
	public $session;

	protected array $protectedDirs = [];
	protected ?string $dropzoneName = null;
	protected ?DropzoneUploader $dropzone;

	/** @var int[]  */
	protected array $allowedViews = [self::VIEW_BIGICON, self::VIEW_LIST, self::VIEW_PANELS];
	protected bool $allowDelete = false;
	protected bool $allowDownload = true;
	protected bool $allowFolders = true;
	protected bool $allowOuterDiv = true;
	protected bool $allowCopy = false;

	protected bool $showTitle = true;

	protected bool $showContainer = true;
	protected bool $showDetails = true;
	protected bool $showPreview = true;
	protected bool $sortable = false;
	protected bool $spinner = false;
	protected array $order = [];
	protected string $orderFile = 'order.json';

	protected string $sortingFolder = WWW_DIR."/uploads/filemanager/sorting";
	public function __construct(protected Presenter $presenter,public Imgcache $imgcache, protected FileManager $fileManager) {
		if(method_exists($presenter,"isSuperadmin") && $this->presenter->isSuperadmin()){
			$this->setSortable();
		}
	}

	public function isS3() : bool {
		return $this->fileManager->getAdapter() instanceof S3Adapter;
	}

	/**
	 * @param string $documentDir
	 *
	 * @return $this
	 */
	public function setDocumentDir( string $documentDir ): self {
		$this->documentDir = str_replace("\\","/", $documentDir);

		if(!$this->fileManager->dirExists($this->documentDir) /*&& !$this->isS3()*/) {
			$this->fileManager->createDir($this->documentDir);
		}
		return $this;
	}

		/**
	 * @return string
	 */
	public function getDocumentDir(): string {
		if($this->subDir !== null && $this->subDir !== $this->documentDir && Strings::contains($this->subDir, $this->documentDir)){
			return $this->subDir;
		}
		$this->subDir = null;
		return $this->documentDir;
	}

	public function getFolder($parent = false) : string {
		//bdump($this->getDocumentDir(),"GET FOLDER");
		$uploadsDir = str_replace( "\\", "/", WWW_DIR ) . "/uploads/";
		$folder = str_replace( $uploadsDir , "", $this->getDocumentDir() );
		$folder = str_replace("//","/",$folder);
		//bdump($folder,"GET_FOLDER");
		if(!$parent) {
			return $folder;
		}
		$folder = trim($folder, "/");
		$e = explode("/",$folder);

		unset($e[count($e)-1]);
		return implode("/",$e);
	}

	public function getPath() : string{
		return "/".ltrim(str_replace($this->documentDir, "", $this->getDocumentDir()), "/");//$this->documentDir ." - ".$this->getDocumentDir();
	}

	public function getPathArray() : array {
		$path = trim($this->getPath(),"/");
		$e = explode("/",$path);
		$a = [];
		$baseDir = ($this->getPath() !== "/" ? str_replace($this->getPath(), "", $this->getDocumentDir()) : $this->getDocumentDir());

		$d = new DocumentViewerItem([
			"name"        => 'root',
			"path"        => $this->fileManager->getAdapter()->normalizeKey(($baseDir)),
			"size"        => 0,
			"date_create" => new DateTime(),
			'src' => $this->fileManager->getAdapter()->getBaseUrl().$this->fileManager->getAdapter()->normalizeKey($baseDir),
		], $this);
		$d->setFolder(str_replace("uploads/","",$this->fileManager->getAdapter()->normalizeKey($baseDir)));
		$a[] = [
			"name" => '<i class="fas fa-home"></i>',
			"href" => $d->href
		];
		$baseDir = str_replace($this->getPath(), "", $this->getDocumentDir());
		$i = 0;
		foreach ($e as $item){
			if(strlen($item) == 0){
				continue;
			}
			$baseDir = $baseDir."/".$item;
			$d = new DocumentViewerItem([
				"name"        => $item,
				"path"        => $this->fileManager->getAdapter()->normalizeKey(($baseDir)),
				"size"        => 0,
				"date_create" => new DateTime(),
				'src' => $this->fileManager->getAdapter()->getBaseUrl().$this->fileManager->getAdapter()->normalizeKey($baseDir),
			], $this);
			$d->setFolder(str_replace("uploads/","",$this->fileManager->getAdapter()->normalizeKey($baseDir)));
			$data = [
				"name" => $item,
				"href" => $d->href
			];
			if($i == (count($e) - 1)){
				unset($data["href"]);
			}
			$a[] = $data;
			$i++;
		}

		return $a;
	}

	/**
	 * @param int $type
	 * @param string $itemClass
	 *
	 * @return $this
	 */
	public function setItemClass(int $type, string $itemClass ): self {
		$this->itemClass[$type] = $itemClass;
		return $this;
	}

	/**
	 * @return array
	 */
	public function getItemClass(): array {
		return $this->itemClass;
	}

	/**
	 * @param int $imageWidth
	 *
	 * @return $this
	 */
	public function setImageWidth( int $imageWidth ): self {
		$this->imageWidth = $imageWidth;
		return $this;
	}

	/**
	 * @return int
	 */
	public function getImageWidth(): int {
		return $this->imageWidth;
	}

	/**
	 * @param int $imageHeight
	 *
	 * @return $this
	 */
	public function setImageHeight( int $imageHeight ): self {
		$this->imageHeight = $imageHeight;
		return $this;
	}

	/**
	 * @return int
	 */
	public function getImageHeight(): int {
		return $this->imageHeight;
	}

	/**
	 * @param int|null $imageFit
	 *
	 * @return $this
	 */
	public function setImageFit( ?int $imageFit ): self {
		$this->imageFit = $imageFit;
		return $this;
	}

	/**
	 * @return int|null
	 */
	public function getImageFit(): ?int {
		return $this->imageFit;
	}

	/**
	 * @param ActiveRow $trade
	 *
	 * @return $this
	 */
	public function setTrade( ActiveRow $trade ): self {
		$this->trade = $trade;
		return $this;
	}

	/**
	 * @return ActiveRow
	 */
	public function getTrade(): ?ActiveRow {
		return $this->trade;
	}

	/**
	 * @param bool $allowDelete
	 */
	public function setAllowDelete( bool $allowDelete ): void {
		$this->allowDelete = $allowDelete;
	}

	/**
	 * @return bool
	 */
	public function isAllowDelete(): bool {
		return $this->allowDelete;
	}

	/**
	 * @return int
	 */
	public function getCount() : int
	{
		if($this->getDocumentDir() === null){
			throw new InvalidArgumentException("Document dir must be setted.");
		}

		return count($this->fileManager->listFiles($this->getDocumentDir(), true));
	}

	/**
	 * @return int
	 */
	public function getUnseenCount() : int
	{
		$dc = $this->getCount();
		if($this->getTrade() === null){
			throw new InvalidArgumentException("Trade ActiveRow must be setted.");
		}
		$documentsCountNew = 0;
		$dw = $this->trade->related("trade_document_visit")->where(["user"=>$this->getParent()->getUser()->getId()])->fetch();
		if(!$dw){
			$documentsCountNew = $dc;
		} else {
			$documentsCountNew = ($dc - $dw->count);
		}
		return $documentsCountNew;
	}

	protected function getDirs() : array {
		$dirs = [];
		// Ak som v podadresári pridat sipku hore
		if($this->subDir !== null){
			$fi = new \SplFileInfo($this->subDir);
			$d = new DocumentViewerItem([
				"name"        => $fi->getFilename(),
				"path"        => $this->fileManager->getAdapter()->normalizeKey(dirname((string) $fi)),
				"size"        => $fi->getSize(),
				"date_create" => DateTime::from($fi->getCTime()),
				'src' => $this->fileManager->getAdapter()->getBaseUrl().$this->fileManager->getAdapter()->normalizeKey((string) $fi),
			], $this);
			$d->setFolder($this->getFolder(true));
			$d->setParent();
			$d->setName("..");
			$dirs[$d->src] = $d;
		}
		foreach ($this->fileManager->listFolders($this->getDocumentDir()) as $dir){
			$f = new DocumentViewerItem($dir, $this);
			$f->setFolder($this->getFolder() . "/" . $f->fileName );
			$dirs[$f->src] = $f;
		}
		return $dirs;
	}
	protected function getFiles() : array {
		$files = [];
		$i = 0;
		//bdump($this->fileManager->listFiles($this->getDocumentDir()));
		//bdump($this->fileManager);
		foreach ($this->fileManager->listFiles($this->getDocumentDir()) as $file){
			$f = new DocumentViewerItem($file, $this);
			$files[$f->src] = $f;
			if ( ! isset( $this->order[ $f->src ] ) ) {
				$this->order[ $f->src ] = 1000 + $i;
				$i ++;
			}
		}

		uksort($files, function ($k, $k2){
			$value1 = isset($this->order[$k]) ? $this->order[$k] : 1;
			$value2 = isset($this->order[$k2]) ? $this->order[$k2] : 1;
			if($value1 == $value2){
				return 0;
			}
			return $value1 > $value2 ? 1 : -1;
		});

		return $files;
	}
	protected function getOrder() : array {
		if(empty($this->order)) {
			$order = [];
			$jsonOrderFile = $this->sortingFolder."/".md5($this->getDocumentDir()).".json";
			if ( file_exists( $jsonOrderFile )) {
				try {
					$order = Json::decode( file_get_contents( $jsonOrderFile ),
						Json::FORCE_ARRAY );

				} catch ( \Throwable $e ) {

				}
			}
			$this->order = $order;
			return $order;
		} else{
			return $this->order;
		}

	}

	public function render() : void
	{
		//$this->presenter->getSession("documentViewer")->{$this->getSessionName("subDir")} = null;
		if($this->getDocumentDir() === null){
			throw new InvalidArgumentException("Document dir must be setted.");
		}

		if($this->getSession("subDir") !== null && $this->getDocumentDir() !== $this->getSession("subDir")){
			$this->subDir = $this->getSession("subDir");
			$this->template->changeFolder = $this->getFolder();

		}
		//bdump($this->subDir, $this->template->changeFolder);

		$this["folderAddForm"]->setDefaults(["dir" => $this->getDocumentDir()]);

		$this->template->setFile($this->templateFile);
		$this->template->path = $this->getPath();
		$this->template->pathArray = $this->getPathArray();
		$this->template->documentDir = $this->getDocumentDir();
		$this->template->itemClass = $this->getItemClass();
		$this->template->imageWidth = $this->getImageWidth();
		$this->template->imageHeight = $this->getImageHeight();
		$this->template->imageFit = $this->getImageFit();

		$this->template->items = [];

		if($this->fileManager->dirExists($this->getDocumentDir())) {
			$this->getOrder();
			$dirs  = $this->getDirs();
			$files = $this->getFiles();

			$this->template->items = $dirs + $files;
		}

		if(!isset($this->presenter->getSession("documentViewer")->{$this->getSessionName()})){
			$this->presenter->getSession("documentViewer")->{$this->getSessionName()} = self::VIEW_PANELS;
		}
		$this->template->viewType = $this->presenter->getSession("documentViewer")->{$this->getSessionName()};
		if(!in_array($this->template->viewType, $this->allowedViews)){
			$this->template->viewType = $this->allowedViews[0];
		}
		$this->template->render();
	}

	public function getSession($key) {
		return $this->presenter->getSession("documentViewer")->{$this->getSessionName($key)};
	}
	public function setSession($key, $value) : void {
		$this->presenter->getSession("documentViewer")->{$this->getSessionName($key)}  = $value;
	}

	public function handleChangeDir($src){
		$this->subDir = $src;
		if(!file_exists($src)){
			FileSystem::createDir($src);
		}

		$this->presenter->getSession("documentViewer")->{$this->getSessionName("subDir")} = $src;
		$this->redrawControl("items");
		$this->redrawControl("folderAddForm");
	}

	protected function getSessionName(string $type = "ViewType") : string
	{
		return $this->getName().$type;
	}

	public function renderPopup(string $src) : void
	{
		$this->template->src = $src;
		$this->template->setFile($this->templateFilePopup);
		$this->template->render();
	}

	public function renderJs() : void
	{
		$this->template->setFile(__DIR__."/templates/js.latte");
		$this->template->render();
	}

	public function handleChangeView(int $type) : void
	{
		$this->presenter->getSession("documentViewer")->{$this->getSessionName()} = $type;
		$this->template->viewType = $type;
		$this->redrawControl();
	}

	public function handleDownload(string $file,bool $aws = false){

		if($this->isS3() && $aws){
			$this->presenter->redirectUrl($this->fileManager->downloadUrl($file));
			exit;
		}
		$fileResponse = new FileResponse($file);
		$this->presenter->sendResponse($fileResponse);
	}

	public function handleDeleteFile(string $file){
		if($this->isAllowDelete()) {
			FileSystem::delete($file);
			// call event onRemove from DropzoneUploader
			if($this->dropzone instanceof DropzoneUploader){
				$ef = explode("/", $file);
				$f = end($ef);
				foreach($this->dropzone->onRemove as $callback){
					$callback($this->dropzone, $f, $file);
				}
			}
			$this->redrawControl("items");
		}
	}

	public function handleReload(){
		$fulldir = $this->fileManager->getAdapter()->normalizeKey($this->presenter->getSession("documentViewer")->{$this->getSessionName("subDir")} ?? $this->getDocumentDir());
		$this->fileManager->cleanCache($fulldir);;
		$this->redrawControl("items");

	}

	/**
	 * @return string
	 */
	public function getTitle(): string {
		return ($this->title === null ? "Náhľady k nahratým dokumentom" : $this->title);
	}

	/**
	 * @param string $title
	 *
	 * @return DocumentViewer
	 */
	public function setTitle( string $title ): self {
		$this->title = $title;
		return $this;
	}

	/**
	 * @param mixed $subDir
	 *
	 * @return DocumentViewer
	 */
	public function setSubDir( $subDir ) :self {
		$this->subDir = str_replace("\\","/",$subDir);
		if(!file_exists($this->subDir)) {
			FileSystem::createDir($this->subDir);
		}
		return $this;
	}

	/**
	 * @param array $protectedDirs
	 */
	public function setProtectedDirs( array $protectedDirs ): void {
		$this->protectedDirs = $protectedDirs;
	}

	public function isDirProtected(DocumentViewerItemInterface $item) : bool{
		if($item->isDir()){
			return in_array($item->dirName($this->documentDir), $this->protectedDirs) || $item->name == '..';
		}
		return false;
	}

	/**
	 * @return string
	 */
	public function getDropzoneName(): string {
		return $this->dropzoneName !== null ? $this->dropzoneName : $this->getName();
	}

	/**
	 * @param string $dropzoneName
	 *
	 * @return DocumentViewer
	 */
	public function setDropzoneName( string $dropzoneName ): self {
		$this->dropzoneName = $dropzoneName;
		$this->dropzone = $this->presenter->getComponent($this->getDropzoneName(), false);
		return $this;
	}

	public function createComponentFolderAddForm() : Form {
		$f = $this->presenter->formFactory->create();
		$f->addHidden("dir");
		$f->addText("folder","Priečinok")->setRequired();
		$f->addSubmit("save","Uložiť");
		$f->onSuccess[] = function (Form $form, ArrayHash $values){
			if(strlen($values->dir) > 0){
				$values->folder = Strings::webalize($values->folder);
				$dir = rtrim($values->dir,"/")."/".ltrim($values->folder,"/");

				if(!is_dir($dir)) {
					$this->fileManager->createDir($dir);
					$this->presenter->flashMessage("Priečinok vytvorený", IMessage::LEVEL_SUCCESS);
				} else {
					$this->presenter->flashMessage("Priečinok už existuje", IMessage::LEVEL_ERROR);
				}

				$this->redrawControl("items");
				$this->redrawControl("folderAddForm");
			}
		};
		return $f;
	}
	public function handleShowFolderAddForm(){
		$this->template->showFolderAddForm = true;
		$this->redrawControl("folderAddForm");
	}

	/**
	 * @param int[] $allowedViews
	 *
	 * @return DocumentViewer
	 */
	public function setAllowedViews( array $allowedViews ): DocumentViewer {
		$this->allowedViews = $allowedViews;
		//bdump($this->presenter->getSession("documentViewer")->{$this->getSessionName()});
		return $this;
	}

	/**
	 * @return int[]
	 */
	public function getAllowedViews(): array {
		return $this->allowedViews;
	}

	/**
	 * @param bool $allowFolders
	 *
	 * @return DocumentViewer
	 */
	public function setAllowFolders( bool $allowFolders ): DocumentViewer {
		$this->allowFolders = $allowFolders;

		return $this;
	}

	/**
	 * @return bool
	 */
	public function isAllowFolders(): bool {
		return $this->allowFolders;
	}

	public function isSuperSuperadmin() : bool {
		if(method_exists($this->presenter, 'isSuperSuperadmin')){
			return $this->presenter->isSuperSuperadmin();
		}
		return false;
	}

	/**
	 * @param bool $allowOuterDiv
	 *
	 * @return DocumentViewer
	 */
	public function setAllowOuterDiv( bool $allowOuterDiv ): DocumentViewer {
		$this->allowOuterDiv = $allowOuterDiv;

		return $this;
	}

	/**
	 * @return bool
	 */
	public function isAllowOuterDiv(): bool {
		return $this->allowOuterDiv;
	}

	/**
	 * @return bool
	 */
	public function isShowTitle(): bool {
		return $this->showTitle;
	}

	/**
	 * @param bool $showTitle
	 *
	 * @return DocumentViewer
	 */
	public function setShowTitle( bool $showTitle ): DocumentViewer {
		$this->showTitle = $showTitle;

		return $this;
	}

	/**
	 * @param bool $showDetails
	 *
	 * @return DocumentViewer
	 */
	public function setShowDetails( bool $showDetails ): DocumentViewer {
		$this->showDetails = $showDetails;

		return $this;
	}

	/**
	 * @return bool
	 */
	public function isShowDetails(): bool {
		return $this->showDetails;
	}

	/**
	 * @param bool $showPreview
	 *
	 * @return DocumentViewer
	 */
	public function setShowPreview( bool $showPreview ): DocumentViewer {
		$this->showPreview = $showPreview;

		return $this;
	}

	/**
	 * @return bool
	 */
	public function isShowPreview(): bool {
		return $this->showPreview;
	}

	/**
	 * @param DropzoneUploader $dropzone
	 *
	 * @return DocumentViewer
	 */
	public function setDropzone( DropzoneUploader $dropzone ): DocumentViewer {
		$this->dropzone = $dropzone;
		$this->dropzoneName = $dropzone->getName();
		return $this;
}

	public function handleSort(){
		try {
			$folder = rtrim($this->presenter->getHttpRequest()->getQuery("folder"),"\/");


			$json = Json::decode($this->presenter->getHttpRequest()->getQuery("order"), Json::FORCE_ARRAY);
			$i = 1; $order = [];
			foreach ($json as $item){
				$order[$item] = $i;
				$i++;
			}


			FileSystem::write($this->sortingFolder."/".md5($folder).".json", Json::encode($order));
			//file_put_contents($folder."/order.json", Json::encode($order));
		} catch (\Throwable $e){

		}
		$this->presenter->flashMessage("Poradie bolo zmenené.");
		$this->redrawControl();
		//bdump($order);
	}

	/**
	 * @return bool
	 */
	public function isSortable(): bool {
		return $this->sortable && $this->getCount() > 1;
	}

	/**
	 * @param bool $sortable
	 */
	public function setSortable( bool $sortable = true ): void {
		FileSystem::createDir($this->sortingFolder);
		$this->sortable = $sortable;
	}

	/**
	 * @param bool $allowDownload
	 *
	 * @return DocumentViewer
	 */
	public function setAllowDownload(bool $allowDownload): DocumentViewer
	{
		$this->allowDownload = $allowDownload;
		return $this;
	}

	/**
	 * @return bool
	 */
	public function isAllowDownload(): bool
	{
		return $this->allowDownload;
	}

	/**
	 * @param bool $spinner
	 *
	 * @return DocumentViewer
	 */
	public function setSpinner(bool $spinner = true): DocumentViewer
	{
		$this->spinner = $spinner;
		return $this;
	}

	/**
	 * @return bool
	 */
	public function isSpinner(): bool
	{
		return $this->spinner;
	}

	/**
	 * @param bool $allowCopy
	 *
	 * @return DocumentViewer
	 */
	public function setAllowCopy(bool $allowCopy = true): DocumentViewer
	{
		$this->allowCopy = $allowCopy;
		return $this;
	}

	/**
	 * @return bool
	 */
	public function isAllowCopy(): bool
	{
		return $this->allowCopy;
	}

	/**
	 * @param bool $showContainer
	 *
	 * @return DocumentViewer
	 */
	/*public function setShowContainer(bool $showContainer): DocumentViewer
	{
		$this->showContainer = $showContainer;
		return $this;
	}*/

}