<?php

namespace <PERSON><PERSON><PERSON><PERSON>ta\Components;

use JetBrains\PhpStorm\Pure;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\FileManager\Adapters\Adapter;
use <PERSON>bo<PERSON>kita\Utils\Strings;
use Nette\Application\UI\Link;
use Nette\Application\UI\Presenter;
use Nette\InvalidArgumentException;
use Nette\SmartObject;
use Nette\Utils\Html;

/**
 * @property-read string $src
 * @property-read $baseSrc
 * @property-read $href
 * @property-read $name
 * @property-read $extension
 * @property-read $icon
 * @property-read $image
 * @property-read $target
 * @property-read $fancy
 * @property-read $downloadLink
 * @property-read $deleteLink
 * @property-read $fileName
 * @property-read $size
 * @property-read $date_create
 */
class DocumentViewerItem implements DocumentViewerItemInterface {

	use SmartObject;


	protected string $path;

	public $src;
	public $baseSrc;
	public $href;
	/** @var string */
	public $name;
	public $extension;
	public $icon = 'fas fa-file tx-dark';
	public $image;
	public $target = '_self';
	public $fancy = false;
	public $downloadLink;
	public $deleteLink;
	public $fileName;
	public $date_create;
	public $size = 0;
	//public


	/** @var string */
	protected $folder;
	/** @var bool */
	protected $parent = false;

	protected bool $imageAsIcon = true;
	protected ?Presenter $presenter = null;


	public function __construct(array $result,protected ?DocumentViewer $documentViewer = null) {


		$this->src = $result["src"];
		$this->baseSrc = $result["path"];
		$this->path = $this->normalizeUri(WWW_DIR."/".$this->baseSrc);
		$this->href = $this->src;
		$this->size = $result["size"];
		$this->date_create = $result["date_create"];
		if($this->documentViewer !== null){
			$this->initProperties();
		}

	}

	public function getPreviewElementPrototype() : Html {
		$el = Html::el("a");
		$el->setAttribute("data-caption", $this->name);
		$el->setAttribute("href", $this->href);
		$el->setAttribute("title",$this->name);
		if($this->isFancyBox()){
			$el->setAttribute("data-fancybox", "gallery");
		}
		if($this->target !== null){
			$el->setAttribute("target", $this->target);
		}
		return $el;
	}

	public function getIconElementPrototype() : Html {
		if($this->icon !== null){
			return Html::el("i")->setAttribute("class", $this->icon);
		} elseif ($this->image !== null) {
			return Html::el("img")->setAttribute("src", $this->image)->setAttribute("class","img-fluid m-auto");
		}
	}

	private function normalizeUri(string $string) : string {
		return str_replace('\\',"/", $string);
	}

	public static function formatFileName(string $filename){
		preg_match('/^[0-9]+[_](.+)(\.[a-zA-Z]+)$/m',$filename, $match);
		if(!empty($match)) {
			return $match[1];
		}
		return $filename;
	}

	public function link(string $destination, $args = []) : ?Link
	{
		if($this->documentViewer !== null)
		{
			return $this->documentViewer->lazyLink($destination, $args);
		}
		//throw new InvalidArgumentException('Document viewer not set.');
		return null;
	}

	public function presenterLink(string $destination, $args = []) : ?Link
	{
		if($this->documentViewer !== null){
			return $this->documentViewer->getPresenter()->lazyLink($destination, $args);
		} elseif($this->presenter !== null){
			return $this->presenter->lazyLink($destination,$args);
		}
		return null;
	}

	public function resizedCachedUrl( string $src, ?int $width = null, ?int $height = null, ?int $fit = null ){
		//bdump($this);
		return $this->documentViewer->imgcache->link($src, $width, $height, $fit);
	}

	public function initProperties(): void {
		$pathinfo = pathinfo($this->baseSrc);
		$this->fileName = $pathinfo["basename"]; //str_replace("uploads/".rtrim($this->documentViewer->getFolder(),"/")."/","", $this->baseSrc);//$this->baseSrc;
		//list($name, $ext) = $this->isDir() ? [$this->fileName, ""] : explode(".",$this->fileName);
		$this->name = $pathinfo["filename"]; //rtrim($name,"/");
		$this->extension = isset($pathinfo["extension"]) ? ".".$pathinfo["extension"] : null;
		preg_match('/^[0-9]+[_](.+)(\.[a-zA-Z]+)$/m',$this->fileName, $match);

		if(!empty($match)) {
			$this->name = $match[1];
			$this->extension = $match[2];
		}

		$this->deleteLink = "javascript:dwDeleteFile('".$this->link("deleteFile!", ["file" => $this->normalizeUri($this->baseSrc)])."');";

		if($this->documentViewer?->isSpinner()){
			$this->deleteLink .= "spinnerOn();";
		}
		//bdump($pathinfo);



		if($this->isDir()){
			if($this->documentViewer !== null) {
				$this->href = "javascript:dwChangeDir('" . $this->link("changeDir!",
						["src" => $this->path]) . "', '" . $this->documentViewer->getDropzoneName() . "','" . $this->getFolder() . "');";
			}
			$this->icon = $this->hasParent() ? 'fas fa-level-up-alt' :'fas fa-folder';
		}else {
			if($this->documentViewer?->isS3()){
				$this->baseSrc = $this->src;
			}
			if ( in_array( Strings::lower( ltrim($this->extension,".") ), [ 'jpg', 'jpeg', 'png' ] ) ) {
				//bdump($this->imageAsIcon);
				if($this->imageAsIcon && $this->documentViewer !== null) {
					$this->image = $this->resizedCachedUrl($this->baseSrc,
						$this->documentViewer->getImageWidth(), $this->documentViewer->getImageHeight(),
						$this->documentViewer->getImageFit());
					$this->icon  = null;
				} else {
					$this->icon = 'fas fa-file-image';
				}
			} elseif (in_array( Strings::lower( ltrim($this->extension,".") ), [ 'pdf' ] ) ) {
				$this->icon = 'fas fa-file-pdf tx-danger';
			} elseif (in_array( Strings::lower( ltrim($this->extension,".") ), [ 'xls', 'xlsx' ] ) ) {
				$this->icon = 'fas fa-file-excel tx-success';
				$this->target = '_blank';
				$this->href = $this->presenterLink(":Backend:Default:documentPreview", ["src" => $this->src]);

			} elseif (in_array( Strings::lower( ltrim($this->extension,".") ), [ 'doc', 'docx' ] ) ) {
				$this->icon = 'fas fa-file-word tx-primary';
				$this->target = '_blank';
				//$this->href = $this->documentViewer->presenter->link(":Backend:Default:documentPreview", ["src" => $this->src]);
				$this->href = $this->presenterLink(":Backend:Default:documentPreview", ["src" => $this->src]);
			}
			$this->fancy = $this->isFancyBox();
			$this->downloadLink = $this->link("download!", ["file" => $this->baseSrc,"aws" => true]);

		}

	}

	#[Pure] public function isDir(): bool {
		if(Strings::contains($this->baseSrc, ".")){
			return false;
		}
		return true;
	}

	public function dirName(string $documentDir = '') : ?string {
		return ltrim(str_replace($documentDir, "", $this->path), "\\/");
	}

	/**
	 * @return bool
	 */
	#[Pure] public function isFancyBox() :bool {
		return in_array(Strings::lower(ltrim($this->extension ?? '',".")), ['jpg','jpeg','png','pdf']);
	}

	public function getFolder(): ?string {
		return $this->folder === null ? null : str_replace("//","/",trim($this->folder,"/"));
	}

	/**
	 * @param string $folder
	 */
	public function setFolder( string $folder ): void {
		$this->folder = $folder;
		$this->initProperties();
	}

	/**
	 * @return bool
	 */
	public function hasParent(): bool {
		return $this->parent;
	}

	/**
	 * @param bool $parent
	 */
	public function setParent( bool $parent = true ): void {
		$this->parent = $parent;
		$this->initProperties();
	}

	/**
	 * @param string $name
	 */
	public function setName( string $name ): void {
		$this->name = $name;
	}

	/**
	 * @param bool $imageAsIcon
	 *
	 * @return DocumentViewerItem
	 */
	public function setImageAsIcon(bool $imageAsIcon): DocumentViewerItem
	{
		$this->imageAsIcon = $imageAsIcon;
		$this->initProperties();
		return $this;
	}

	/**
	 * @param Presenter|null $presenter
	 *
	 * @return DocumentViewerItem
	 */
	public function setPresenter(?Presenter $presenter): DocumentViewerItem
	{
		$this->presenter = $presenter;
		return $this;
	}
}