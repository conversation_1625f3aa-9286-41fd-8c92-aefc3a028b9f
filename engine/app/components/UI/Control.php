<?php

namespace Webtec\UI;

use Nette;

class Control extends Nette\Application\UI\Control
{
	/**
	 * @param string
	 * @return Nette\Templating\ITemplate
	 */
	/*protected function createTemplate($class = NULL): Nette\Application\UI\ITemplate
	{
		$presenter = $this->getPresenter();

		$template = parent::createTemplate($class);
		//dumpe($presenter->getTemplate()->getLatte()->getFilters());
		//$template->setTranslator($presenter->context->translator);
		/*foreach ($presenter->getTemplate()->getLatte()->getFilters() as $name => $value) {
			//dump($name,$value);
			$template->addFilter($name, $value);
		}*/
		//dumpe($template);
	/*	return $template;
	}*/
}
