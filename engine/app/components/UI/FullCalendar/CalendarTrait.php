<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\UI;


use Webtec\Models\UserModel;

trait CalendarTrait {
	public function createComponentCalendar() : FullCalendar{
		$cal = new FullCalendar();
		$cal->setDefaultView("weekGrid");
		$cal->setCalendarModel($this->modelFactory->create("calendar"));
		$cal->setUsers([$this->getUser()->getId() => UserModel::formatName($this->selfUserModel)]);
		if(!$this->isSuperadmin()){
			$cal->setSearchMetadata([
				"model" => "user",
				"where" => [
					"user.type" => [UserModel::TYPE_COWORKER,UserModel::TYPE_SPECIALIST],
					"user.id" => $this->userModel->getPath($this->getUser()->getId(), true, UserModel::LIST_TYPE_ID)
				],
				"columns" => [
					"name_list.name",
					"name_list.surname",
					"name_list.company_name"
				]
			]);
		}
		return $cal;
	}



}