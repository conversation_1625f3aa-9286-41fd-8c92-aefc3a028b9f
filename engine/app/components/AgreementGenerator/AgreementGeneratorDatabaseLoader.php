<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Loaders;

use <PERSON><PERSON>mi<PERSON><PERSON>\Factory\CacheFactory;
use Kubomi<PERSON><PERSON>\Utils\Strings;
use Nette\DI\Container;
use Webtec\Models\AgreementModel;
use Webtec\Models\ModelFactory;

class AgreementGeneratorDatabaseLoader implements IAgreementGeneratorLoader {
	/** @var ModelFactory */
	protected $modelFactory;
	/** @var CacheFactory */
	protected $cacheFactory;

	public function __construct(ModelFactory $modelFactory, CacheFactory $cacheFactory) {
		$this->modelFactory = $modelFactory;
		$this->cacheFactory = $cacheFactory;
	}

	public function setDir( string $dir ): void {	}

	public function getInputsConfig(): array {
		$config = [];

		foreach($this->modelFactory->create("dgFields")->findAll()->select("*")->order("ordr") as $field){
			$input_key = $field->key;
			$selection = $field->related("dg_items");
			$config[$input_key] = $this->modelFactory->create("dgItems")->getConfigArray($field, $selection);
		}
		//dumpe($config);
		bdump($config, "INPUTS");
		return $config;
	}

	public function getFieldsConfig() : array {
		$config = [];
		foreach($this->modelFactory->create("dgFields")->findAll()->select("*")->order("ordr") as $field){
			$config[$field->dg_groups][$field->key] = $field->name;
		}
		bdump($config, "FIELDS");
		return $config;
	}

	public function getTypesConfig(): array {
		$config = [];
		foreach ($this->modelFactory->create("dgGroups")->findAll() as $type){
			$config[$type->id] = $type->name;
		}
		bdump($config, "TYPES");
		return $config;
	}

	public function getFieldsCountConfig() : array  {
		$config = [];
		foreach($this->modelFactory->create("dgFields")->findAll()->select("*") as $field){
			$config[$field->key] = $field->count;
		}
		bdump($config, "FIELDS_COUNT");
		return $config;
	}


	public function getPlaceholder( string $key, string $name, Container $context ): string {
		$item = $this->modelFactory->create("dgFields")->findBy(["key" => $key])->fetch();
		if($item && $item->placeholder !== null){
			return $item->placeholder;
		}
		return '<span class="editor-placeholder">{$'.$key.'}</span>';
	}
}