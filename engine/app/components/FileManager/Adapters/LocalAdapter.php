<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\FileManager\Adapters;


use JetBrains\PhpStorm\ArrayShape;
use <PERSON>bomi<PERSON>ta\Utils\DateTime;
use Nette\InvalidArgumentException;
use Nette\Utils\FileSystem;
use Nette\Utils\Finder;

class LocalAdapter extends Adapter  {

	public function dirExists(string $source): bool
	{
		return file_exists($source);
	}

	public function getContent(string $source) : string {
		/*$result = $this->client->getObject([
			"Bucket" => $this->bucket,
			"Key" => $this->normalizeKey($source),
		]);*/
		return (string) file_get_contents($this->normalizeKey($source));
	}

	public function save(string $source, ?string $name = null): string|bool{
		try {
			if($this->isFileContent($source)){
				bdump("isFileContent", $name);
				if($name === null){
					throw new InvalidArgumentException('Destination of file must be specified!');
				}
				FileSystem::write($name, $source);
			} else {
				$name = ($name === null) ? $this->normalizePath( $source ) : $this->normalizePath( $name );
				$source = $this->normalizePath($source);
				FileSystem::copy($source, $name);
			}
			return $name;

		} catch (\Throwable $e){
			return false;
		}
	}

	public function delete(string $source) : bool {
		$source = $this->normalizePath($source);
		try {
			gc_collect_cycles();
			FileSystem::delete($source);
			return true;
		} catch (\Throwable $e){
			bdump($e);
			return false;
		}
	}
	public function createDir(string $source, int $mode = 0777) : bool {
		$source = $this->normalizePath($source);
		try {
			FileSystem::createDir($source, $mode);
			return true;
		} catch (\Throwable $e){
			return false;
		}
	}

	public function listFiles(string $prefix,bool $withDownloadUrls = false, bool $withContent = false) : array
	{
		$prefix = $this->normalizePath($prefix);
		//$tempName = $prefix.(($withDownloadUrls === true) ? "-urls":"").(($withContent === true) ? "-content" : "");
		$files = [];
		try {
			/** @var \SplFileInfo $f */
			foreach (Finder::findFiles("*")->in($prefix) as $f) {
				$data = [
					"name"        => $f->getFilename(),
					"path"        => $this->normalizeKey((string) $f),
					"size"        => $f->getSize(),
					"date_create" => DateTime::from($f->getCTime()),
					"src"         => $this->getBaseUrl() . $this->normalizeKey((string) $f),
					"ext"         => $f->getExtension(),
				];
				if ($withDownloadUrls) {
					$data["downloadUrl"] = $data["src"];
				}
				if ($withContent) {
					$content = FileSystem::read((string) $f);
					$mime    = mime_content_type((string) $f);
					$data    += ["content" => $content, "mimeType" => $mime];
				}
				$files[] = $data;
			}
		} catch (\Throwable $e){
			bdump($e);
		}
		return $files;
	}

	public function listFolders(string $prefix) : array {
		$prefix = $this->normalizePath($prefix);
		$folders = [];
		/** @var \SplFileInfo $d */
		foreach (Finder::findDirectories("*")->in($prefix) as $d){
			$data      = [
				"name"        => $d->getFilename(),
				"path"        => $this->normalizeKey((string) $d),
				"size"        => $d->getSize(),
				"date_create" => DateTime::from($d->getCTime()),
				'src' => $this->getBaseUrl().$this->normalizeKey((string) $d),
			];
			$folders[] = $data;
		}
		return $folders;
	}

	public function countFiles(string $prefix) : int {
		$prefix = $this->normalizePath($prefix);
		$fi = new \FilesystemIterator( $prefix, \FilesystemIterator::SKIP_DOTS );
		return iterator_count( $fi );
	}


	public function downloadUrl( string $source ): string {
		return $this->getBaseUrl().$this->normalizeKey($source);
	}
	public function getSrc(string $source):string
	{
		return $this->getBaseUrl().$this->normalizeKey($source);
	}

	public function fileExists(string $source): bool
	{
		return file_exists($source);
	}

	#[ArrayShape(["name"     => "string",
	              "src"      => "string",
	              "size"     => "int",
	              "content"  => "string",
	              "mimeType" => "string"
	])] public function getObject(string $soruce): ?array
	{
		// TODO: Implement getObject() method.
		throw new InvalidArgumentException('Not implemented yet.');
		return null;
	}
}