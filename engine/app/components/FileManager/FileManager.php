<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\FileManager;


use JetBrains\PhpStorm\ArrayShape;
use JetBrains\PhpStorm\Deprecated;
use Kubomikita\Factory\CacheFactory;
use Kubomikita\FileManager\Adapters\Adapter;
use <PERSON><PERSON>mi<PERSON><PERSON>\FileManager\Adapters\AdapterInterface;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Utils\FileSystem;
use function Symfony\Component\String\b;

class FileManager implements AdapterInterface {

	protected ?Cache $cache;

	public function __construct(protected Adapter $adapter)
	{
	}

	/**
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter): void
	{
		$this->adapter = $adapter;
	}
	public function getContent(string $source): string
	{
		return $this->adapter->getContent($source);
	}

	public function fileExists(string $source) : bool
	{
		return $this->adapter->fileExists($source);
	}

	#[ArrayShape(["name"     => "string",
	              "src"      => "string",
	              "size"     => "int",
	              "content"  => "string",
	              "mimeType" => "string"
	])] public function getObject(string $source) : ?array
	{
		return $this->adapter->getObject($source);
	}

	public function save( string $source, ?string $name = null ): string|bool {
		$result = $this->adapter->save($source, $name);
		$prefix = ($name === null) ?  $source  :  $name ;
		$this->cleanCache($prefix);
		return $result;
	}

	public function delete( string $source ): bool {
		$result = $this->adapter->delete($source);
		$this->cleanCache($source);
		return $result;
	}

	public function createDir( string $source, int $mode = 0777 ): bool {
		$result = $this->adapter->createDir($source,$mode);
		$this->cleanCache($source);
		return $result;
	}

	public function listFiles( string $prefix, bool $withDownloadUrls = false, bool $withContent = false ): array {
		try {
			if ($this->cache !== null) {
				$tempName = $prefix . (($withDownloadUrls === true) ? "-urls" : "") . (($withContent === true) ? "-content" : "");
				//bdump($this->adapter->normalizeKey($prefix), "LIST");
				return $this->cache->load($tempName,
					function (&$dependencies) use ($prefix, $tempName, $withDownloadUrls, $withContent) {
						$dependencies[Cache::EXPIRE] = "12 hour";
						$dependencies[Cache::TAGS]   = [$this->adapter->normalizeKey($prefix)];
						//bdump($dependencies,"Files dependencies");
						//bdump($tempName, "Nacitavame z uloziska - nie Cache");
						return $this->adapter->listFiles($prefix, $withDownloadUrls, $withContent);
					});
			}
			return $this->adapter->listFiles($prefix, $withDownloadUrls, $withContent);
		} catch (\Throwable $e){
			bdump($e);
		}
		return [];
	}

	public function listFolders( string $prefix ): array {
		//return [];
		if($this->cache !== null){
			return $this->cache->load($prefix."-folders",  function (&$dependencies) use ($prefix){
				$dependencies[ Cache::EXPIRE ] = "12 hour";
				$dependencies[ Cache::TAGS ]   = [ $this->adapter->normalizeKey($prefix) ];
				//bdump($dependencies, "folder dependencies");
				//bdump($prefix."-folders", "Nacitavame z uloziska - nie Cache");
				return $this->adapter->listFolders($prefix);
			});
		}
		return $this->adapter->listFolders($prefix);
	}

	public function countFiles( string $prefix ): int {
		//return 0;
		if($this->cache !== null){
			return $this->cache->load($prefix."-count", function (&$dependencies) use ($prefix){
				$dependencies[ Cache::EXPIRE ] = "12 hour";
				$dependencies[ Cache::TAGS ]   = [ $this->adapter->normalizeKey($prefix) ];
				//bdump($prefix."-count", "Nacitavame z uloziska - nie Cache");
				return $this->adapter->countFiles($prefix);
			});
		}
		return $this->adapter->countFiles($prefix);
	}

	public function cleanCache(string $prefix) : void {
		if($this->cache !== null) {
			$prefix = $this->adapter->normalizeKey( $prefix );

			$cleanDependencies = [ Cache::Tags => [ $prefix, $prefix."/", dirname( $prefix ), dirname( $prefix ) . "/" ] ];
			bdump($cleanDependencies, "CacheClean Dependencies");

			$this->cache->clean( $cleanDependencies );
		}
	}

	#[Deprecated('Please use setCacheStorage() method instead.')]
	public function setCache(CacheFactory $cacheFactory) : self {
		$reflection = new \ReflectionClass($this->adapter);
		$this->cache = $cacheFactory->create("fileManager-".$reflection->getShortName());
		return $this;
	}

	public function setCacheStorage(Storage $storage) : self {
		$reflection = new \ReflectionClass($this->adapter);
		$this->cache = new Cache($storage, "fileManager-".$reflection->getShortName());
		return $this;
	}


	public function downloadUrl( string $source ): string {
		return $this->adapter->downloadUrl($source);
	}

	public function attachments(string $prefix) : array {
		return $this->listFiles( $prefix, false, true );
	}

	public function getAdapter(): Adapter {
		return $this->adapter;
	}

	public function getSrc(string $source) : string {
		return $this->adapter->getSrc($source);
	}

	public function dirExists(string $source): bool
	{
		return $this->adapter->dirExists($source);
	}


	public static function storeFile(string $file, string $content) : void
	{
		$pathinfo = pathinfo($file);

		$dir = $pathinfo["dirname"];

		FileSystem::createDir($dir);
		FileSystem::write($file, $content);
	}
}