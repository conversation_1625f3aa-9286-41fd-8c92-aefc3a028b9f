{var $css = $basePath."assets/theme/default/css/stylesheet.css"}
{varType Webtec\Models\CmsLayoutModel $cmsLayoutModel}
<!doctype html>
<html dir="ltr" lang="sk">
<head>
    <meta charset="utf-8">
    <meta name="description" content="{_$description}">
    <meta name="robots" content="{$robots}">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="author" content="{$layout->company_name}">
    <title>{if $title}{_$title} | {/if}{_$layout->title}</title>
    {*dump $page*}
    <meta n:if="$page->og_image !== null" property="og:image" content="{$page->og_image}">

    {if count($locales) > 1 && isset($page)}
        <link rel="canonical" href="{link //this}">
        {foreach $cmsPageModel->getAlternates($page, $locales) as $lang => $langSlug}
            <link rel="alternate" hreflang="{$lang}" href="{link //Default:default, slug:$langSlug, locale:$lang}">
        {/foreach}
    {/if}
    <link rel="icon" type="image/png" sizes="32x32" href="{$layout->favicon}" n:if="$layout->favicon !== null">
    <link rel="apple-touch-icon" href="{$layout->apple_touch_icon}" n:if="$layout->apple_touch_icon !== null">
    <!-- fonts -->
    {if $layout->html_font !== null}
        {$layout->html_font|noescape}
    {else}
        <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700|Playfair+Display:400,400i,700,900,900i&amp;subset=latin-ext" rel="stylesheet">
    {/if}

    <script type="text/javascript" src="{$basePath}/assets/admin/lib/jquery/jquery.3-6-0.min.js"></script>


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
    {[
    "https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css",
    "https://cdn.jsdelivr.net/gh/fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.css",
    "assets/lib/bootstrap-datepicker/dist/css/bootstrap-datepicker3.css",
    $css,
    $layout->stylesheet
    ]|minify:css|noescape}
    {*$layout->stylesheet|minify:css,"custom"|noescape*}
    {$layout->html_head|noescape}
    {if $layout->google_analytics !== null}
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id={$layout->google_analytics}"></script>
        <script type="text/plain" data-cookiecategory="analytics">
            window.dataLayer = window.dataLayer || [];
            function gtag(){ dataLayer.push(arguments); }
            gtag('js', new Date());

            gtag('config', {$layout->google_analytics});
        </script>
    {/if}


</head>
<body>

{*_"test"*}

<!--[if lte IE 9]>
<p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade your browser</a> to improve your experience and security.</p>
<![endif]-->
{*<header>
    {block headline}{/block}
    <div class="container">
        <div class="row align-items-center">
            <div class="col-10 col-lg-2">
                <a class="navbar-brand py-lg-2" href="./">
                    <img n:if="($logo = $cmsLayoutModel->getLogo($layout)) !== null" src="{$basePath}{$layout->logo}" alt="{$layout->title}" class="img-fluid">
                    <div n:if="$logo === null">{$layout->title}</div>
                </a>
            </div>
            <div class="col-2 col-lg-10">
                <nav class="navbar navbar-expand-lg">
                    <button class="navbar-toggler mx-auto" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                        <div class="navbar-burger-icon">
                            <span></span> <span></span> <span></span> <span></span>
                        </div>
                    </button>
                    {block top}

                        {/block}
                </nav>
            </div>

        </div>

    </div>
</header>
*}


<div id="slider">
    {block slider}{/block}
</div>

<div class="container">
    <div n:snippet="flashes">
        {control flashMessages}
    </div>
    <div class="main-content py-3" n:ifcontent>
        {block content}{/block}
    </div>
</div>
{block contentFluid}{/block}
{*<footer>
    <div class="container text-center">
        {block footer}{/block}
        <div class="copy row justify-content-center no-gutters">
            <div class="col-lg-auto">
                {block copyright}
                    © {date('Y')} {$layout->title} Made with <i class="fas fa-heart"></i> by {$layout->company_name}
                {/block}
            </div>
            <div class="col-lg-auto px-2 d-none d-lg-block">|</div>
            <div class="col-lg-auto"><a href="javascript;" type="button" data-cc="c-settings">{_"Nastavenie cookies"}</a></div>
        </div>
    </div>

</footer>*}

<!-- scripts -->
{*<script src="{$basePath}/assets/admin/lib/jquery/jquery.3-6-0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js" integrity="sha384-9/reFTGAW83EW2RDu2S0VKaIzap3H66lZH81PoYlFhbGU+6BZp6G7niu735Sk7lN" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js" integrity="sha384-+sLIOodYLS7CIrQpBjl+C7nPvqq+FbNUBDunl/OZv93DB7Ln/533i8e/mZXLi/P+" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/gh/fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.js"></script>
<script src="{$basePath."/js/custom.js"|version}" type="text/javascript"></script>
<script src="{$basePath}/assets/app/netteAjax.js"></script>
<script src="{$basePath}/assets/app/live-form-validation.js"></script>
<script src="{$basePath."/assets/app/netteInit.js"|version}"></script>
<script src="{$basePath."/assets/app/frontend.js"|version}"></script>*}
{[
"https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js",
"https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js",
"https://cdn.jsdelivr.net/gh/fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.js",
"assets/admin/lib/jquery.mask/jquery.mask.js",
"assets/lib/bootstrap-datepicker/dist/js/bootstrap-datepicker.js",
"js/custom.js",
"assets/app/netteAjax.js",
"assets/app/live-form-validation.js",
"assets/app/netteInit.js",
"assets/app/recaptcha.invisible.ajax.js",
"assets/app/frontend.js",
$layout->javascript
]|minify:js|noescape}

{*$layout->javascript|minify:js,"custom"|noescape*}
{block scripts}{/block}
<script defer src="https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@v2.8.0/dist/cookieconsent.js"></script>
<script>
	let pageEmail = {$layout->cookieconsent_email};  {*'<EMAIL>';*}
</script>
<script defer src="{$basePath."/assets/app/cookieconsent-init.js"|version}"></script>




</body>
</html>