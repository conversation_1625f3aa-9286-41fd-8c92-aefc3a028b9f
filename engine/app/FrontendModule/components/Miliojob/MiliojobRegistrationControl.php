<?php

namespace App\Components;





use IPub\FlashMessages\Entities\Message;


use JetBrains\PhpStorm\NoReturn;
use <PERSON>bomikita\Events\CoworkerEvents;
use Kubomikita\Factory\FormFactory;
use <PERSON><PERSON>mikita\FileManager\FileManager;
use Kubomikita\Services\CompanyProvider;
use Kubomikita\Services\RegistrationNumberProvider;
use Nette\Application\UI\Multiplier;
use Nette\Caching\Cache;
use Nette\DI\Container;
use Nette\Http\FileUpload;
use Nette\Utils\ArrayHash;
use Nette\Utils\DateTime;
use Nette\Utils\Html;
use Nette\Utils\Json;
use Nette\Utils\Random;
use Tracy\Debugger;
use Tracy\Logger;
use Webtec\Models\UserModel;
use Webtec\UI\Forms\Form;

class MiliojobRegistrationControl extends RenderableControl {


	protected ?string $title = "Miliojob.com - Registrácia";
	protected UserModel $userModel;
	protected CompanyProvider $companyProvider;

	public function __construct(Container $container)
	{
		parent::__construct($container);
		$this->userModel = $this->modelFactory->create("user");
		$this->companyProvider = $this->container->getByType(CompanyProvider::class);
	}

	public function isMiliojob() : bool {
		return $this->companyProvider->isMiliojob();
	}

	#[NoReturn] public function startup(array $startupParams = []): void
	{
		parent::startup($startupParams);
		$referral = $this->getModuleByType(MiliojobReferalControl::class);
		$defaults = (array) $this->session->getSection("registrationForm")->get("values");
		bdump($defaults);
		$this["registrationForm"]->setDefaults($defaults + ["referral" => $referral?->getReferral()]);

		if($referral?->getReferral() !== null){
			$this["registrationForm"]["referral"]->setHtmlAttribute("readonly",true);
		}
		try {
			$this->getTemplate()->add("companyProvider", $this->companyProvider);
		} catch (\Throwable $e){

		}
	}

	public function handleFlash(){
		$this->flashMessage("Ahoj ". microtime(true), Message::LEVEL_SUCCESS, "LOOOL");
		if($this->getPresenterIfExists()->isAjax()) {
			$this->template->test = new DateTime();
			$this->redrawControl("time");

		}
	}

	public function createComponentRegistrationForm($name) : Form
	{
		$f = $form = $this->container->getByType(FormFactory::class)->create();
		//bdump($this->companyProvider->company->reg_state);
		/*$f->addSelect("l_person_type_id", "Budem spolupracovať ako", $this->modelFactory->create("lPersonType")->findAll()->fetchPairs("id", "name"))
		  ->setDefaultValue(1)
		  ->setRequired()->addCondition($form::IS_IN, [2,3])->toggle("company-container");*/
		$f->addHidden("l_person_type_id", 1);
		$f->addEmail("username","E-mail")->setHtmlAttribute("class","form-control")->setRequired();
		$f->addText("referral", $this->isMiliojob() ? "Do miliojob ma pozval" : "Pozvánka od")->setNullable();
		$f->addSelect("regstate", "Štát registrácie",$this->modelFactory->create("lCountry")->findAll()->fetchPairs("id","name"))->setRequired();
		$nameListContainer = $form->addContainer("name_list");
		$nameListContainer->addHidden("id");
		/*$nameListContainer->addText("company_name","Obchodné meno")
		                  ->addConditionOn($form["l_person_type_id"],Form::IS_IN, [2,3])->setRequired();
		$nameListContainer->addText("company_ico","IČO")
		                  ->addConditionOn($form["l_person_type_id"],Form::IS_IN, [2,3])->setRequired();;
		$nameListContainer->addText("company_dic","DIČ");
		$nameListContainer->addText("company_icdph","IČ DPH");
		$nameListContainer->addText("iban","IBAN")
		                  ->addCondition($form::FILLED,true)
		                  ->addRule(Form::PATTERN,"Zadajte správny IBAN. Prve dve písmena veľké a 22 čísel.", Form::REGEX_IBAN);//->setRequired();*/
		$nameListContainer->addText("name","Meno")->setRequired();
		$nameListContainer->addText("surname","Priezvisko")->setRequired();
		//$nameListContainer->addDate("birthdate","Dátum narodenia")->setRequired();


		$nameListContainer->addText("phone","Telefón")
		                  ->setRequired()
		                  ->addRule(Form::PATTERN,"Zadajte správne číslo s predvoľbou (napr. +421), bez medzier.",Form::REGEX_MOBILE);


		$nameListContainer->addText("street","Ulica")->setRequired();
		$nameListContainer->addText("street_number","Číslo")->setRequired();
		$nameListContainer->addText("zip","PSČ")->setRequired();
		$nameListContainer->addText("city","Mesto")->setRequired();
		$nameListContainer->addSelect("state","Štát", $this->modelFactory->create("lCountry")->findAll()->fetchPairs("id","name"))->setPrompt("-- nevybratý --")->setRequired();

		$f->addCheckbox("contract", Html::el()->addHtml($this->isMiliojob() ? "Súhlasím s podmienkami <a href='".($this->metadata["contract"] ?? '#')."' target='_blank'>miliojob</a>" : 'Súhlasím s <a href="'.($this->metadata["contract"] ?? '#').'" target="_blank">obchodnými podmienkami</a>'))->setRequired();
		//$f->addCheckbox("conditions", Html::el()->addHtml("Súhlasím s <a href='#' target='_blank'>obchodnými podmienkami</a>"))->setRequired();
		//$f->addCheckbox("gdpr", Html::el()->addHtml("Súhlasím so <a href='#' target='_blank'>spracovaním osobných údajov</a>"))->setRequired();

		$f->addSubmit("save", $this->isMiliojob() ? "Chcem sa zaregistrovať do miliojob" : "Zaregistrovať");
		$f->onSuccess[] = function (Form $form){
			//bdump($form->getValues());
			$this->session->getSection("registrationForm")->set("values", $form->getValues());
		};
		$f->onError[] = function (Form $form){
			$this->flashMessage(implode("<br>", $form->getErrors()),Message::LEVEL_ERROR);
		};
		$f->onSuccess[] = [$this,"validateUsername"];
		$f->onSuccess[] = function (Form $form, ArrayHash $values){

			$db = $this->userModel->getDatabaseConnection();
			$db->beginTransaction();
			try {

				//bdump($values);
				//$this->flashMessage("Idzeme ukladac");
				$values->name_list["email"] = $values->username;
				$values->name_list->created = new DateTime();
				$values->name_list->career_id = $this->userModel->getDefaultCareerId();

				$country = $this->modelFactory->create("lCountry")->find($values->regstate ?? 1)->fetch();

				$values->reg_id = $this->container->getByType(RegistrationNumberProvider::class)->generate($country); //Random::generate(6, "0-9");



				//$values->identifier = $country->short . Random::generate(9, "0-9");
				//$values->
				if($values->referral !== null) {
					$referralUser = $this->modelFactory->create("user")->findAll()->where(["reg_id" => $values->referral])->fetch();
					if($referralUser) {
						$values->users_id = $referralUser->id;
					} else {
						$owners = $this->getPresenter()->getOwner();
						$values->users_id = reset($owners);
						$values->note = "Pozvánka od: ".$values->referral;
					}
				} else {
					$owners = $this->getPresenter()->getOwner();
					$values->users_id = reset($owners);
				}

				unset($values->referral,$values->id,$values->name_list->id, $values->contract, $values->gdpr, $values->conditions, $values->regstate);
				//bdump($values);
				//exit;
				$nl = $this->modelFactory->create("nameList")->insert((array) $values->name_list);
				unset($values->name_list);
				$values->name_list_id = $nl->id;
				$values->type = UserModel::TYPE_COWORKER;
				$values->acl_role_id = $this->userModel->getCoworkerAclRoleId();

				$values->password = password_hash( Random::generate( 15 ), PASSWORD_DEFAULT );
				$values->enabled  = ((int) $values->type === UserModel::TYPE_PARTNER ? 1 : 0);
				$values->approved  = ((int) $values->type === UserModel::TYPE_PARTNER ? 1 : 0);
				$values->created  = new DateTime();



				$new = $this->userModel->insert( (array) $values);
				$id = $new->id;

				$new->update(["level" => count($this->userModel->getParents($id))]);
				//bdump($values);
				/** @var CoworkerEvents onCreate -  */
				$this->em->trigger("coworker.create", $new);
				$this->cacheFactory->create("user")->clean([Cache::TAGS => ["parents","childrens","coworkers","userPoints"]]);

				//$db->rollBack();
				$db->commit();
				$this->session->getSection("registrationForm")->remove("values");

				$this->template->thankYou = true;
				$this->getPresenterIfExists()->redrawControl("cmsContentControl");
				//$this->flashMessage("Kliknutím na odkaz, ktorý sme Vám poslali v registračnom e-maili, potvrdíte správnosť e-mailovej adresy. V registračnom e-maili nájdete aj odporúčací link a link na online dotazník ako podklad pre výpočet možnej provízie.", Message::LEVEL_SUCCESS, "Registrácia");


			} catch (\Throwable $e){
				$db->rollBack();
				bdump($e);
				Debugger::log($e, Logger::EXCEPTION);
				$this->flashMessage("Pri ukladaní nastala chyba.", Message::LEVEL_ERROR);

			}
		};

		return  $f;
	}
	public function validateUsername(Form $form){
		$values = $form->getValues();
		if ( isset( $values->username ) ) {
			$user = $this->userModel->findBy( [ "username" => $values["username"] ] )->fetch();
			if ( $user ) {
				$error = "Zadaný e-mail už je registrovaný.";
				$form["username"]->addError( $error );
			}
		}
	}

	public function createComponentAdminForm():Multiplier
	{
		return new Multiplier(function () {
			return $this->getAdminForm(function (Form $f) {
				$f->addUpload("contract", "Obchodné podmienky (pdf)")->addRule($f::MimeType, "Súbor músí byť vo formáte .pdf", "application/x-pdf,application/pdf");
				$f->onSuccess[] = function (Form $form, ArrayHash $values) {

					if (($part = $this->getPart($values->id, $values->model)) !== null) {
						$metadata         = Json::decode($part->metadata, Json::FORCE_ARRAY);
						/** @var FileUpload $file */
						$file = $values->contract;
						if($file->isOk()){
							$fileManager = $this->container->getByType(FileManager::class);
							$dir = UPLOAD_DIR."/cms/files/".time()."_".$file->getSanitizedName();
							$f = $fileManager->save($file->getContents(), $dir);
							$metadata["contract"] = $f;
						}

						$part->update([
							"metadata" => Json::encode($metadata)
						]);
					}
				};
			});
		});
	}

	public function handleDeleteContract(int $item_id,string $model){
		if(($part = $this->getPart($item_id, $model)) !== null) {
			$metadata = Json::decode($part->metadata, Json::FORCE_ARRAY);
			unset($metadata["contract"]);

			$part->update(["metadata" => Json::encode($metadata)]);
			$this->getPresenterIfExists()?->initVariables();
			$this->getPresenterIfExists()?->redrawControl("webPage");
			$this->getPresenterIfExists()?->redrawControl("cmsParts");
		}
	}
}