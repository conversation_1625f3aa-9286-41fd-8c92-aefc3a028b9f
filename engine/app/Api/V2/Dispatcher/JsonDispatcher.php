<?php declare(strict_types = 1);

namespace App\Api\V2\Dispatcher;

use Apitte\Core\Dispatcher\JsonDispatcher as ApitteJsonDispatcher;
use Apitte\Core\Exception\Api\ClientErrorException;
use Apitte\Core\Exception\Api\ServerErrorException;
use Apitte\Core\Exception\Api\ValidationException;
use Apitte\Core\Handler\IHandler;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Http\RequestAttributes;
use Apitte\Core\Router\IRouter;
use Apitte\Core\Schema\Endpoint;
use Kubomikita\Services\CompanyProvider;
use Kubomikita\Utils\DateTime;
use Nette\DI\Container;
use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Utils\Json;
use RuntimeException;
use Symfony\Component\Serializer\Exception\ExtraAttributesException;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Webtec\Models\ModelFactory;

class JsonDispatcher extends ApitteJsonDispatcher
{

	public function __construct(IRouter $router, IHandler $handler, protected SerializerInterface $serializer, protected ValidatorInterface $validator, protected Container $container)
	{
		parent::__construct($router, $handler);

	}

	protected function handle(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		try {
			$request = $this->transformRequest($request);

			$result = $this->handler->handle($request, $response);

			// Except ResponseInterface convert all to json
			if (!($result instanceof ApiResponse)) {
				$response = $this->transformResponse($result, $response);
			} else {
				$response = $result;
			}

		} catch (ClientErrorException | ServerErrorException $e) {
			$data = [];

			if ($e->getMessage()) {
				$data['message'] = $e->getMessage();
			}

			if ($e->getContext()) {
				$data['context'] = $e->getContext();
			}

			if ($e->getCode()) {
				$data['code'] = $e->getCode();
			}

			$response = $response->withStatus($e->getCode() ?: 500)
			                     ->withHeader('Content-Type', 'application/json');
			$response->getBody()->write(Json::encode($data));
		} catch (RuntimeException $e) {
			$response = $response->withStatus($e->getCode() ?: 500)
			                     ->withHeader('Content-Type', 'application/json');
			$response->getBody()->write(Json::encode([
				'message' => $e->getMessage() ?: 'Application encountered an internal error. Please try again later.',
			]));
		}

		// Delete nette header X-Powered-By
		$this->container->getByType(Response::class)->deleteHeader("X-Powered-By");
		// Add new header X-Powered-By with company name of licence
		$poweredBy = $this->container->getByType(CompanyProvider::class)->getCompany()->company_name;

		$httpRequest = $this->container->getByType(Request::class);

		$data = [
			"api_token" => null,
			"method" => $httpRequest->getMethod(),
			"datetime" => new DateTime(),
			"url" => $httpRequest->getUrl()->withQuery(["_access_token" => null])->getRelativeUrl(),
			"ip" => $httpRequest->getRemoteAddress(),
			"host" => $httpRequest->getRemoteHost(),
			"browser" => $_SERVER['HTTP_USER_AGENT'],
			"rawBody" => !empty($request->getParsedBody()) ? Json::encode($request->getParsedBody()) : null, //strlen($request->getBody()->getContents()) > 0 ? $request->getBody()->getContents() : null,
			"rawResponse" => strlen($response->getContents()) > 0 ? $response->getContents() : null,
			"token" => $request->getAttribute("app.invoice.supplier")->api_token  ?? null,
		];
		$this->container->getByType(ModelFactory::class)->create("apiTokenAccess")->insert($data);


		if(!$request->getAttribute("app.tracy.show")) {
			return $response->withHeader('Content-Type', 'application/json')->withHeader("X-Powered-By",$poweredBy);
		}
		return $response->withHeader("X-Powered-By",$poweredBy);
	}

	/**
	 * Transform incoming request to request DTO, if needed.
	 */
	protected function transformRequest(ApiRequest $request): ApiRequest
	{
		// If Apitte endpoint is not provided, skip transforming.
		if (!($endpoint = $request->getAttribute(RequestAttributes::ATTR_ENDPOINT))) {
			return $request;
		}

		// @safety
		assert($endpoint instanceof Endpoint);

		// Get incoming request entity class, if defined. Otherwise, skip transforming.
		if (!($entity = $endpoint->getTag('request.dto'))) {
			return $request;
		}
		//dumpe($request->getParameters(), $this->serializer->deserialize($request->getParameters(), $entity, "array"),$entity);
		//dumpe($this->serializer);
		try {
			// Create request DTO from incoming request, using serializer.
			$dto = $this->serializer->deserialize(
				$request->getBody()->getContents(),
				$entity,
				'json',
				['allow_extra_attributes' => false]
			);

			$request = $request->withParsedBody($dto);
		} catch (ExtraAttributesException $e) {
			//dumpe($e);
			throw ValidationException::create()
			                         ->withMessage($e->getMessage());
		}

		// Try to validate entity only if its enabled
		$violations = $this->validator->validate($dto);

		if (count($violations) > 0) {
			$fields = [];
			foreach ($violations as $violation) {
				$fields[$violation->getPropertyPath()][] = $violation->getMessage();
			}

			throw ValidationException::create()
			                         ->withMessage('Invalid request data')
			                         ->withFields($fields);
		}

		return $request;
	}

	/**
	 * Transform outgoing response data to JSON, if needed.
	 *
	 * @param mixed $data
	 */
	protected function transformResponse($data, ApiResponse $response): ApiResponse
	{
		$response = $response->withStatus(200)
		                     ->withHeader('Content-Type', 'application/json');
		// Serialize entity with symfony/serializer to JSON
		$serialized = $this->serializer->serialize($data, 'json');

		$response->getBody()->write($serialized);

		return $response;
	}

}