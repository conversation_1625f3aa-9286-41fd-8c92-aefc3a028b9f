<?php
namespace App\Api\Decorator;

use Apitte\Core\Decorator\IRequestDecorator;
use Apitte\Core\Exception\Runtime\EarlyReturnResponseException;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use function GuzzleHttp\Psr7\stream_for;

class AuthenticationDecorator implements IRequestDecorator
{

	/**
	 * @throws EarlyReturnResponseException If other request decorators and also deeper layers (endpoint) should be skipped
	 */
	public function decorateRequest(ApiRequest $request, ApiResponse $response): ApiRequest
	{
		if ($userAuthenticationFailed || false) {
			$body = stream_for(json_encode([
				'status' => 'error',
				'code' => 403,
				'message' => 'Invalid credentials, authentication failed.'
			]));

			$response = $response
				->withStatus(403)
				->withBody($body);
			throw new EarlyReturnResponseException($response);
		}

		return $request;
	}

}