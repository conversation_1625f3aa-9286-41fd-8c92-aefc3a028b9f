<?php

namespace Webtec\Models;

use Kubomikita\Utils\Strings;
use Nette\Application\IPresenter;
use Nette\Application\UI\ITemplate;
use Nette\Bridges\ApplicationLatte\TemplateFactory;
use Nette\Database\Table\ActiveRow;
use Nette\Forms\Controls\BaseControl;
use Nette\Forms\Controls\ChoiceControl;
use Nette\Forms\Controls\TextInput;
use Webtec\UI\Control;
use Webtec\UI\Forms\Form;

class MetadataModel extends BaseModelCompany {

	const INPUT_TEXT = 1;
	const INPUT_TEXTAREA = 2;
	const INPUT_DATE = 3;
	const INPUT_SELECT = 4;

	const INPUT_TYPES = [
		self::INPUT_TEXT => "textové pole",
		self::INPUT_DATE => "dátumové pole",
		self::INPUT_SELECT => "výber zo zoznamu"
	];


	public function addMetadata($form, ?int $containerIterator = null) {
		foreach ($this->findAll() as $item){
			$l_section_type_ids = array_values($item->related("metadata_l_section_type")->fetchPairs("id","l_section_type"));
			$form["l_section_type"]->addCondition(Form::IS_IN, $l_section_type_ids )
			                       ->toggle($this->formatContainerName($item->id, $containerIterator))
			                       ->endCondition();
			$container = $form->addContainer("metadata_".$item->id);
			foreach($item->related("metadata_item") as $input){
				$this->addInput( $input, $container, $l_section_type_ids );//->setOmitted(false);
			}
		}
		return $form;
	}

	private function addInput(ActiveRow $input, $form, array $l_section_type_ids ) : BaseControl{
		switch ($input->input_type){
			case self::INPUT_DATE:
				$function = "addDate";
				break;
			case self::INPUT_TEXTAREA:
				$function = "addTextArea";
				break;
			case self::INPUT_SELECT:
				$function = "addSelect";
				$items = explode(",",$input->items);
				break;
			default:
				$function = "addText";
		}

		$name = $this->formatInputName($input->name);
		$title = ($input->title !== null ? $input->title : $input->name);

		if(isset($items)) {
			$itms = [];
			foreach ($items as $itm){
				$itms[trim($itm)] = trim($itm);
			}

			$inp = $form->{$function}($name, $title, $itms)->setPrompt("-- vyberte --");
		} else {
			$inp = $form->{$function}($name, $title);
		}
		if($input->required){
			$parent = $form->getParent()["l_section_type"];
			$inp->addConditionOn($parent,Form::EQUAL, $l_section_type_ids)->setRequired();
		}

		if($inp instanceof ChoiceControl){
			$inp->checkDefaultValue(false);
		}

		return $inp;
	}

	public function formatInputName(string $string) : string {
		return str_replace("-","_",Strings::webalize($string));
	}

	public function formatContainerName( $group_id, ?int $containerIterator = null ) : string  {
		return "metadata-container-$group_id". ($containerIterator !== null ? "-".$containerIterator : "");
	}

	public function render(IPresenter $presenter, $form, ?int $iterator = null){
		/** @var ITemplate $template */
		$template = $presenter->createTemplate();
		$template->setFile(APP_DIR."/BackendModule/templates/@metadata.latte");
		$template->groups = $this->findAll();
		$template->model = $this;
		$template->form = $form;
		$template->containerIterator = $iterator;
		$template->render();
	}

	public function renderAdd(IPresenter $presenter, int $l_section_type, int $iterator){
		/** @var ITemplate $template */
		$template = $presenter->createTemplate();
		$template->setFile(APP_DIR."/BackendModule/templates/@metadataAdd.latte");
		$template->groups = $this->findBy(["id" => $this->context->getByType(ModelFactory::class)->create("metadataLSectionType")->findBy(["l_section_type" => $l_section_type])->select("metadata.id")]);
		$template->model = $this;
		$template->containerIterator = $iterator;
		$template->render();
	}

}