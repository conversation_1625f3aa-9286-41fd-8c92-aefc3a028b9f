<?php

namespace Webtec\Models;



use Nette\Database\Table\ActiveRow;
use Nette\Utils\Validators;

class InvoiceAutomaticMessageModel extends BaseModel
{

	public function getBccs(ActiveRow $invoiceAutomaticMessage) : array
	{
		$bccs = [];
		if($invoiceAutomaticMessage->bcc !== null) {
			foreach (explode(",", $invoiceAutomaticMessage->bcc) as $bcc) {
				$bcc = trim($bcc);
				if ( ! Validators::isEmail($bcc)) {
					continue;
				}
				$bccs[$bcc] = $bcc;
			}
		}
		return $bccs;
	}
}