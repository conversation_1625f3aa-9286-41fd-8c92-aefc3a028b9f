<?php

namespace Webtec\Models;


use Kubomikita\Utils\DateTime;
use Nette\Database\Table\ActiveRow;
use Nette\Security\User;

class NotificationModel extends BaseModel
{

	public function notifyTrade(ActiveRow $trade, int $user_added, string $message, string $link, ?\DateTimeInterface $dateTime = null) {
		if($dateTime === null){
			$dateTime = new DateTime();
		}

		$userKeys = ["user","specialist","dealer"];
		$added = [];
		foreach($userKeys as $uk){
			$user_id = $trade->{$uk};
			if($user_id !== $user_added && (int) $user_id > 0 && !in_array($user_id, $added)){
				$this->insert( [ "user"     => $user_id,
				                              "datetime" => $dateTime,
				                              "message"  => $message,
				                              "link"     => $link
				] );
				$added[] = $user_id;
			}
		}


	}

	public function notifyClient(ActiveRow $client, int $user_added, string $message, string $link, ?\DateTimeInterface $dateTime = null){
		if($dateTime === null){
			$dateTime = new DateTime();
		}

		$usersAdd = [];
		if($client->users_id !== $user_added) {
			$usersAdd[$client->users_id] = $client->users_id;
		}
		$specialists = $client->related("trade")->group("specialist")->fetchPairs("specialist","specialist");
		$usersAdd += $specialists;

		foreach($usersAdd as $user){
			if((int) $user > 0) {
				$this->insert( [
					"user"     => $user,
					"datetime" => $dateTime,
					"message"  => $message,
					"link"     => $link
				] );
			}
		}
	}

}