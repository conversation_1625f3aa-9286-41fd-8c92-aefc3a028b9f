<?php

namespace Webtec\Models;

use Kubomiki<PERSON>\Utils\Strings;
use Nette;
use Nette\Database\Table\ActiveRow;

class UserInvoiceAddressModel extends BaseModel
{

	/**
	 * @param ActiveRow $row
	 *
	 * @return string
	 */
	public static function formatCredentials( ActiveRow $row) : string {
		return $row->name;
	}

	public function fetchList(array $where = [], string $key = "id", string $separator = ', ',bool $escape = true,bool $withContact = false, bool $withDetails = false) : array {
		$list = [];
		foreach($this->findBy($where) as $p){
			if(Strings::contains($key, ".")){
				list($first,$second) = explode(".", $key);
				$k = $p->ref($first)->{$second};
			} else {
				$k = $p->{$key};
			}
			//dumpe($k);
			$list[$k] = $this::formatName($p,$separator, $escape, $withContact, $withDetails);
		}
		return $list;
	}

	public static function formatName( ActiveRow $row, $separator = '<br>', $escape = false, $withContact = true, $withDetails = true):string {
		$line = [
			"name" => '<strong>'.$row->name."</strong>",
		];
		if($withDetails){
			$line += ["address" => $row->street.", ".$row->post_code." ".$row->town,
			          "state" => '<span class="d-inline-block mb-1">'.$row->state."</span>"];
		}
		if($withContact) {
			if ( $row->email !== null ) {
				$line["email"] = "E-mail: " . $row->email;
			}
			if ( $row->phone !== null ) {
				$line["phone"] = "Telefón:  " . $row->phone;
			}
		}
		if($withDetails) {
			if($row->registration_id !== null){
				$line["reg"] = '<span class="d-inline-block mt-1">IČO: '.$row->registration_id."</span>";
			}

			if ( $row->tax_id !== null ) {
				$line["tax_id"] = "DIČ: " . $row->tax_id;
			}
			if ( $row->vat_id !== null ) {
				$line["vat_id"] = "IČDPH: " . $row->vat_id;
			}
			if ( $row->register !== null ) {
				$line["register"] = "Zapísaný v: " . $row->register;
			}
		}
		if(!$escape) {
			return implode( $separator, $line );
		}
		return strip_tags(implode( $separator, $line ));
	}
}
