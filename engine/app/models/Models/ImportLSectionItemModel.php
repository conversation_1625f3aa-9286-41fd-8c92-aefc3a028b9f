<?php

namespace Webtec\Models;


use JetBrains\PhpStorm\ArrayShape;
use Kubomikita\Utils\Csv;
use Kubomiki<PERSON>\Utils\DateTime;
use Nette\Database\Table\Selection;
use Nette\Schema\Elements\Structure;
use Nette\Schema\Expect;
use Nette\Utils\Json;


class ImportLSectionItemModel extends BaseModelCompany
{

	use ImportModelTrait;

	const CSV_MAP = [
		"id"          => 0,
		"active"        => 1,
		"l_section"         => 2,
		"l_section_type"      => 3,
		"partner"                => 4,
		"l_section_item"           => 5,
		"coefficient_career"      => 6,
		"coefficient_partner" => 7,
	];
	const CSV_VISIBLE = [
		/*"user_email",
		"client_email",
		"client_name",
		"client_surname",
		"l_section_item",
		"ident",
		"specialist_email",
		"dealer_email",
		"provision"*/
	];
	const CSV_DEFAULTS = [
		/*"division_user"       => 1.0,
		"division_specialist" => 0.0,
		"division_dealer"     => 0.0,*/
		"active" => 1,
	];

	public function getSchema(): Structure
	{
		return Expect::structure([
			"id"                    => Expect::int()->nullable(),
			"active"                => Expect::int()->min(0)->max(1)->required(),
			"l_section"             => Expect::string()->required(),
			"l_section_type"        => Expect::string()->required(),
			"partner"               => Expect::string()->required(),
			"l_section_item"        => Expect::string()->required(),
			"coefficient_career"    => Expect::float()->nullable(),
			"coefficient_partner"    => Expect::float()->nullable(),
		]);
	}




	#[ArrayShape(['id'                  => "string",
	              'active'              => "string",
	              'coefficient_career'  => "string",
	              'coefficient_partner' => "string"
	])] public function getNormalize(): array
	{
		return [
			'id'                  => Csv::NORMALIZE_INT,
			'active'              => Csv::NORMALIZE_INT,
			'coefficient_career'  => CSV::NORMALIZE_FLOAT,
			'coefficient_partner' => CSV::NORMALIZE_FLOAT,
		];
	}


	public function doImport(
		Selection $selection,
		\DateTimeInterface $import_date,
		?\DateTimeInterface $status_date = null
	): void {
		$processed = [];
		$careerId = $this->context->getByType(ModelFactory::class)->create("user")->getDefaultCareerId();
		foreach ($selection/*->limit(20)*/ as $item){
			$exists = $this->context->getByType(ModelFactory::class)->create("lSectionItem")->find($item->col_id)->fetch();
			if($exists){
				$commissions = $exists->related("commissions")->order("date DESC")->fetch();
				$now = [
					"active" => $exists->active,
					"name" => $exists->name,
					"coefficient_career" => $commissions?->coefficient_career,
					"coefficient_partner" => $commissions?->coefficient_partner
				];

				$new = [
					"active" => $item->col_active,
					"name" => $item->col_l_section_item,
					"coefficient_career" => $item->col_coefficient_career,
					"coefficient_partner" => $item->col_coefficient_partner
				];

				$diff = array_diff($new, $now);
				if(!empty($diff)) {
					$update = [];
					if(isset($diff["active"])) {
						$update["active"] = $diff["active"];
					}
					if(isset($diff["name"])){
						$update["name"] = $diff["name"];
					}
					if(!empty($update)){
						$exists->update($update);
					}
					if(isset($diff["coefficient_career"]) || isset($diff["coefficient_partner"])){
						$commissionsModel = $this->context->getByType(ModelFactory::class)->create("commissions");
						$commissionsModel->insert([
							"date" => new DateTime(),
							"l_section_item" => $exists->id,
							"career" => $careerId,
							"coefficient_career" => $item->col_coefficient_career,
							"coefficient_partner" => $item->col_coefficient_partner,
							"coefficient1" => $commissions?->coefficient1 !== null ? $commissions->coefficient1 : 0,
							"income_provision" => $commissions?->income_provision !== null ? $commissions->income_provision : 0
						]);
					}

					$item->update(["message" => "Zmenené: ". Json::encode($diff)]);
					//dump($exists, $now, $new, array_diff($new, $now));

				}
				$processed[] = $item->id;
			} else {
				$item->update(["message" => "Konkrétny obchod neexistuje."]);
			}


		}

		$selection->where(["id" => $processed])->update(["processed" => 1]);


	}
}