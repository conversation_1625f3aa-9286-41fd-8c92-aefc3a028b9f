<?php
namespace Webtec\Models;

use Nette\Database\Table\ActiveRow;

class TrainingModel extends BaseModelCompany {
	public function getChilds($id){
		$q = $this->findBy(["parent_id" => $id]);
		return $q;
	}
	public function hasChilds($id){
		return $this->getChilds($id)->count("id");
	}

	/**
	 * @param ActiveRow $dir
	 *
	 * @return array
	 */
	public function getPath(ActiveRow $dir) : array {
		$ret = [];
		$q = $dir;
		if((int) $q->parent_id > 0){
			$ret[] = $this->find($q->parent_id)->fetch();

		}
		$ret[] = $q;
		return $ret;
	}
}