<?php

namespace Webtec\Models;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Events\EventsManager;
use Kubo<PERSON>kita\Events\TradeStatusEvents;
use Kubomikita\Services\CompanyProvider;
use Nette\Database\Table\ActiveRow;
use Nette\Utils\Strings;

class TradeStatusModel extends BaseModelCompany {

	const TYPE_NEW = 1;
	const TYPE_INPROGRESS_WORKING = 4;
	const TYPE_INPROGRESS_WAITING = 5;
	const TYPE_INPROGRESS_CLOSED = 6;
	const TYPE_INPROGRESS = 2;
	const TYPE_CLOSED = 3;

	const TYPES = [
		self::TYPE_NEW => "nové",
		self::TYPE_INPROGRESS => "rozpracované",
		self::TYPE_CLOSED => "dokončené",
		self::TYPE_INPROGRESS_WORKING => "rozpracované",
		self::TYPE_INPROGRESS_WAITING => "čakáme",
		self::TYPE_INPROGRESS_CLOSED => "neúspešné"

	];

	public function hasEvent($event,$trade_status): bool
	{
		if(!($trade_status instanceof ActiveRow)){
			$trade_status = $this->find($trade_status)->fetch();
		}
		if(Strings::contains($trade_status->event,"[".$event."]")){
			return true;
		}
		return false;
	}

	/**
	 * @param int|null $company_id
	 */
	public function createDefaults(?int $company_id = null) : void {
		if($company_id !== null) {
			/** @var CompanyProvider $companyProvider */
			$companyProvider = $this->context->getByType( CompanyProvider::class );
			$this->setCompany($companyProvider->getCompanyById($company_id));
			$this->setAdminMode(false);
		}
		//bdump($this->company);
		//bdump($this->findAll()->getSql());
		if($this->findAll()->count("id") > 0){
			return;
		}


		$defaults = [
			[
				'ordr' => 1,
				'default' => 1,
				'name' => 'zaevidovaný',
				'event' => '[create]',
				'type' => self::TYPE_NEW,
				'automat' => 0,
				"class" => '',
				'date_required' => 0
			],[
				'ordr' => 2,
				'default' => 0,
				'name' => 'rozpracovaný',
				'event' => '',
				'type' => self::TYPE_INPROGRESS_WORKING,
				'automat' => 0,
				"class" => '',
				'date_required' => 0
			],[
				'ordr' => 3,
				'default' => 0,
				'name' => 'čaká sa na úhradu klientom',
				'event' => '[waiting_for_client]',
				'type' => self::TYPE_INPROGRESS_WAITING,
				'automat' => 1,
				"class" => '',
				'date_required' => 0
			],[
				'ordr' => 4,
				'default' => 0,
				'name' => 'uhradený',
				'event' => '[Vyplatená provízia][to_paid][to_paid_first][noedit]',
				'type' => self::TYPE_CLOSED,
				'automat' => 1,
				"class" => 'text-success',
				'date_required' => 0
			]

		];

		$this->insert($defaults);
	}

	/**
	 * @return int[]
	 */
	public function getFailedStatusId() : array {
		return array_keys($this->findAll()->where(["event LIKE '%[Nezrealizovaná]%' OR event LIKE '%[cancelled]%'"])->fetchPairs("id","id"));
	}

	public function handleEvent( int|ActiveRow $status, ActiveRow $trade, ?ActiveRow $statuses = null) : void
	{
		if(is_int($status)){
			$status = $this->find($status)->fetch();
		}
		foreach($this->getEvents($status) as $event) {
			try {
				/** @var TradeStatusEvents on{Event} */
				$this->context->getByType(EventsManager::class)->trigger("tradestatus." . str_replace("_", ".", $event), $trade, $status, $statuses);
			} catch (\ErrorException $e){
				bdump($e);
			}
		}
	}

	public function getEvents(ActiveRow $status) :array
	{
		if(strlen($status->event) > 2){
			$event = substr($status->event, 1,-1);
			return explode("][", $event);
		}
		return [];
	}
}