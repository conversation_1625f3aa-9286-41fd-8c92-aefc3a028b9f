<?php

namespace Webtec\Models;

use Kubomikita\Utils\Strings;
use Nette\Database\Table\ActiveRow;
use Nette\Database\Table\Selection;

class DgItemsModel extends BaseModel
{


	public function getConfigArray(ActiveRow $field, Selection $selection) : array{
		$all = $selection->fetchAll();
		$count = count($all);
		$config = [];

		$containers = [];
		foreach($all as $item){
			if($item->container !== null){
				$containers[$item->container] = $item->container;
				$config["containers"][$item->container][] = $item->key;
			}
		}
		if($field->copy !== null){
			$config["copy"] = $field->copy;
		}

		foreach($all as $item){
			if($count > 1 || /*$item->ref("dg_fields")->count*/ $field->count > 0) {
				$config["items"][ $item->key ] = [
					"name"     => $item->name,
				];
				if($item->required){
					$config["items"][ $item->key ]["required"] = true;
					//"" => (bool) $item->required,
				}
				if( $item->type !== null){
					$config["items"][ $item->key ]["type"] = "add".Strings::firstUpper($item->type);
				}
				if ( $item->container !== null ) {
					$config["items"][ $item->key ]["container"] = $item->container;
				}
				if($item->default_value !== null) {
					$config["items"][ $item->key ]["defaultValue"] = $item->default_value;
				}
				if($item->class !== null) {
					$config["items"][ $item->key ]["class"] = $item->class;
				}
				if ( $item->toggle !== null ) {
					foreach ( $containers as $container ) {
						$config["items"][ $item->key ]["toggle"][ $container ] = ( trim( $item->toggle ) === trim( $container ) ? true : false );
					}
				}
			} else {
				$config["name"] = $item->name;
				$config["required"] = (bool) $item->required;
				if( $item->type !== null){
					$config["type"] = "add".Strings::firstUpper($item->type);
				}
				if($item->default_value !== null) {
					$config["defaultValue"] = $item->default_value;
				}
				if($item->class !== null) {
					$config["class"] = $item->class;
				}
			}
		}

		return $config;
	}
}