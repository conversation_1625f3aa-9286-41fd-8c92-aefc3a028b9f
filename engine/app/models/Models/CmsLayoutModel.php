<?php

namespace Webtec\Models;

use Kubomikita\Services\CompanyProvider;
use Kubomikita\Utils\Strings;
use Nette;
use Nette\Database\Table\ActiveRow;
use Nette\Http\Request;
use Nette\Utils\FileSystem;
use Nette\Utils\Image;

class CmsLayoutModel extends BaseModelCompany
{

	public function getAvailableDomains() : array
	{
		$ret = [];

		if($this->getUser()->getAuthorizator()->isSuperadmin()){
			$selection = $this->findAll();
		} else {
			$selection = $this->findAll()->whereOr(["user" => $this->getUser()->getId()/*,"user IS NULL"*/, "system" => 1]);
		}

		foreach ($selection as $layout){
			foreach($this->getDomains($layout) as $domain){
				if($this->context->parameters["localhost"] && str_ends_with($domain, ".local")){
					$ret[] = $domain;
				} elseif (!$this->context->parameters["localhost"] && !str_ends_with($domain, ".local")){
					$ret[] = $domain;
				}
			}
		}
		return $ret;
	}

	public function getAvailableLocales(ActiveRow $row) : array
	{
		$systemLangs = $this->companyProvider->getLocales();
		$locales = Nette\Utils\Json::decode($row->langs);

		$return = [];

		foreach ($locales as $locale){
			if(isset($systemLangs[$locale])){
				$return[$locale] = $systemLangs[$locale];
			}
		}

		return $return;
	}

	public function getAvailableLayouts() : array
	{
		$layouts = [];
		$dir = APP_DIR."/FrontendModule/templates";
		/** @var \SplFileInfo $file */
		foreach (Nette\Utils\Finder::findFiles("@*.latte")->from($dir) as $file){
			//bdump();//$file->getFilename());
			$layouts[str_replace(["@",".latte"],["",""], $file->getFilename())] = $file->getFilename();
		}
		return $layouts;
	}
	public function checkDomain( string  $domain, int $layoutId) : bool {
		return $this->findBy(["domain LIKE ?" => '%"'.$domain.'"%', "id <>" => $layoutId])->count("id") > 0;
	}
	public function getDomains(ActiveRow $layout) : array
	{
		try{
			return $layout->domain === null ? [] : Nette\Utils\Json::decode($layout->domain, Nette\Utils\Json::FORCE_ARRAY);
		} catch (\Throwable $e){
			bdump($e);
		}
		return [];
	}
	public function getLogo(ActiveRow $layout) : ?string //Image
	{
		if($layout->logo !== null && file_exists(WWW_DIR.$layout->logo)){
			return $layout->logo; //Image::fromString(base64_decode($layout->logo));
		}
		return null;
	}

	public function hasLayouts() : bool {

		return $this->cache->load("count-".$this->company?->id ?? 'admin', function (&$dependencies){
			$dependencies[Nette\Caching\Cache::Expire] = "2 hour";
			return $this->findAll()->count("id") > 0;
		});

		$cacheKey = 'hasLayouts';
		if(!isset(self::$staticCache[$cacheKey])){
			self::$staticCache[$cacheKey] = $this->findAll()->count("id") > 0;
		}
		return self::$staticCache[$cacheKey];
	}



	/*public function getLogoUrl(ActiveRow $invoiceSupplier) : ?string
	{
		if(($image = $this->getLogo($invoiceSupplier)) !== null){
			$dir = UPLOAD_DIR."/invoice_supplier_images";
			FileSystem::createDir($dir);
			$file = $dir."/logo_".$invoiceSupplier->id."_".Strings::webalize($invoiceSupplier->name).".png";
			$image->save($file);


			$httpRequest = $this->context->getByType(Request::class);

			return rtrim($httpRequest->getUrl()->getBaseUrl(),"/").str_replace(WWW_DIR, "", $file);
		}

		return null;
	}*/
}