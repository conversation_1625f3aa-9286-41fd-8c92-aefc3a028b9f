<?php

namespace Webtec\Models;

use <PERSON>bomiki<PERSON>\Cron\ExportJob;
use <PERSON>bomiki<PERSON>\Utils\DateTime;
use Nette;
use Nette\Database\Table\ActiveRow;
use Nette\Utils\Json;

class ExportModel extends BaseModelCompany
{



	public function startExport(array $args) : void {
		$cronModel = $this->context->getByType(CronModel::class);

		$insert = [
			"user" => $args["user"],
			"datetime" => new DateTime(),
			"subject" => $args["subject"],
			"args" => Json::encode($args)
		];

		$export = $this->insert($insert);

		$cronModel->startIfExists(ExportJob::class, $args + ["exportId" => $export->id]);

	}
	public function isStarted(?int $userId = null) : bool
	{
		if($userId !== null){
			$cron = $this->context->getByType(CronModel::class)->getByType(ExportJob::class);
			if($cron) {
				try {
					$args = Json::decode($cron->args, Json::FORCE_ARRAY);
					if ($args["user"] !== $userId) {
						return false;
					}
					return $this->context->getByType(CronModel::class)->isStarted(ExportJob::class, $cron);
				} catch (\Throwable $e){
					return false;
				}
			} else {
				return false;
			}
		}
		return $this->context->getByType(CronModel::class)->isStarted(ExportJob::class);
	}
	public function isRunning() : bool
	{
		return $this->context->getByType(CronModel::class)->isRunning(ExportJob::class);
	}
	public function isWaitingOrStarted(): bool
	{
		return $this->context->getByType(CronModel::class)->isWaitingOrStarted(ExportJob::class);
	}

	public function getCronRow() : ActiveRow
	{
		return $this->context->getByType(CronModel::class)->getByType(ExportJob::class);
	}
}
