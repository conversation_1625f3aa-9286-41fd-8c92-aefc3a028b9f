<?php
namespace Webtec\Models;

use BackendModule\MailingPresenter;
use Contributte\Mailing\IMailBuilderFactory;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\FileManager\Adapters\S3Adapter;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Mailing\Template\BirthdayWishTemplate;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Mailing\Template\ContactMailingForm;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Mailing\Template\GdprTemplate;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Mailing\Template\InvoiceMailingTemplate;
use <PERSON><PERSON><PERSON><PERSON>ta\Mailing\Template\LostPasswordMailingTemplate;
use <PERSON><PERSON><PERSON>kita\Mailing\Template\ProvisionListTemplate;
use <PERSON><PERSON>mi<PERSON><PERSON>\Mailing\Template\RegistrationMailingTemplate;
use <PERSON><PERSON>miki<PERSON>\Mailing\Template\SurveyMailingTemplate;
use <PERSON><PERSON><PERSON><PERSON>ta\Mailing\Template\VoucherMailingTemplate;
use Ku<PERSON><PERSON>kita\Services\CompanyProvider;
use Kubomikita\Utils\DateTime;
use Latte\Loaders\StringLoader;
use Latte\Runtime\Template;
use Latte\Sandbox\SecurityPolicy;
use Nette\Application\IPresenter;
use Nette\Database\Table\ActiveRow;
use Nette\InvalidArgumentException;
use Nette\Utils\ArrayHash;
use Nette\Utils\Random;
use Nette\Utils\Strings;

/** @property-read $presenter */
class MailingTemplateModel extends BaseModelCompany {

	const TYPE_REGISTRATION = 1;
	const TYPE_INVOICE = 2;
	const TYPE_TRADE_MARKET = 3;
	const TYPE_TRADE_ACTIVITY = 4;
	const TYPE_SURVEY = 5;
	const TYPE_LOST_PASSWORD = 6;
	const TYPE_CUSTOM = 999;
	const TYPE_CUSTOM_TRADE_STATUS = 998;
	const TYPE_CUSTOM_VOUCHER = 997;
	const TYPE_PROVISION_LIST = 7;
	const TYPE_GDPR = 8;
	const TYPE_BIRTHDAY_WISH = 9;
	const TYPE_INVOICE_INVOICE = 10;
	const TYPE_CONTACT = 11;
	//const TYPE_VOUCHER = 12;


	const TYPES = [
		self::TYPE_REGISTRATION => "Aktivačný e-mail po registrácii",
		self::TYPE_INVOICE => "Faktúra",
		self::TYPE_TRADE_ACTIVITY => "Záznam o aktivite",
		self::TYPE_TRADE_MARKET => "Nový obchod v el. trhovisku",
		self::TYPE_SURVEY => "Online dotazník",
		self::TYPE_LOST_PASSWORD => "Zabudnuté heslo",
		self::TYPE_CUSTOM => "Vlastná šablóna",
		self::TYPE_CUSTOM_TRADE_STATUS => "K stavom obchodu",
		self::TYPE_PROVISION_LIST => "Provízny list",
		self::TYPE_GDPR =>  'Súhlas s marketingom',
		self::TYPE_BIRTHDAY_WISH => 'Narodeninové prianie',
		self::TYPE_INVOICE_INVOICE => "Faktúra",
		self::TYPE_CONTACT => "Kontaktný formulár",
		self::TYPE_CUSTOM_VOUCHER => "K elektronickým poukážkam",
	//	self::TYPE_VOUCHER => "Elektronická poukážka",
	];

	const DEFAULTS = [
		self::TYPE_REGISTRATION => [RegistrationMailingTemplate::class, 'getDefaults'],
		self::TYPE_INVOICE => [InvoiceMailingTemplate::class, 'getDefaults'],
		self::TYPE_SURVEY => [SurveyMailingTemplate::class, "getDefaults"],
		self::TYPE_LOST_PASSWORD => [LostPasswordMailingTemplate::class, 'getDefaults'],
		self::TYPE_PROVISION_LIST => [ProvisionListTemplate::class, 'getDefaults'],
		self::TYPE_GDPR => [GdprTemplate::class, 'getDefaults'],
		self::TYPE_BIRTHDAY_WISH => [BirthdayWishTemplate::class, 'getDefaults'],
		self::TYPE_CONTACT => [ContactMailingForm::class, "getDefaults"],
		//self::TYPE_VOUCHER => [VoucherMailingTemplate::class, "getDefaults"],
	];

	const CUSTOM_TYPES = [
		self::TYPE_CUSTOM => self::DEFAULT_CUSTOM_DESC,
		self::TYPE_CUSTOM_TRADE_STATUS => self::DEFAULT_CUSTOM_TRADE_STATUS_DESC,
		self::TYPE_CUSTOM_VOUCHER => self::DEFAULT_CUSTOM_DESC
	];

	const DEFAULT_CUSTOM_DESC = <<<EOT
{\$name} - meno
{\$surname} - priezvisko
{\$username} - e-mail na užívateľa
EOT;
	const DEFAULT_CUSTOM_TRADE_STATUS_DESC = <<<EOT
{\$name} - meno klienta
{\$surname} - priezvisko klienta
{\$username} - e-mail klienta
{\$section} - oblasť obchodu
{\$tradeName} - názov obchodu
{\$createdBy} - cele meno kto vytvoril
{\$trade} - druh obchodu
{\$client} - celé meno klienta
{\$link} - odkaz na obchod
EOT;



	/** @var IPresenter */
	private $presenter;

	public function onCreate() {
		parent::onCreate();
		$this->presenter = $this->context->getService($this->context->findByType(MailingPresenter::class)[0]);
	}

	public function getById(int $id, array $params = []) : ArrayHash
	{
		$template = $this->find($id)->fetch();
		if(!$template){
			throw new InvalidArgumentException("Mailing template with id '".$id."' not exists.");
		}

		return $this->get($template, $params);
	}

	public function getByType(int $type, array $params = []) : ArrayHash
	{
		$template = $this->findBy(["type" => $type])->fetch();
		if(!$template){
			throw new InvalidArgumentException("Mailing template with type '".$type."' not exists.");
		}

		return $this->get($template, $params);
	}

	public function get(ActiveRow $template, array $params = []) : ArrayHash
	{
		$toRender = $this->getRender($template, $params);
		$toRender["row"] = $template;
		$toRender["from"] = $template->from;

		return ArrayHash::from($toRender);
	}



	protected function getRender(ActiveRow $template, array $params = []) : array
	{
		$toRender = [
			"message" => $template->message,
			"subject" => $template->subject
		];

		/** @var \Nette\Bridges\ApplicationLatte\Template $t */
		$t = $this->presenter->createTemplate();
		$latte = $t->getLatte();

		$policy = SecurityPolicy::createSafePolicy();
		$policy->allowFilters(["humantime","timestring","currency","inflect","points","filesize","noescape"]);
		//bdump($policy->isFilterAllowed("humantime"));
		$latte->setPolicy($policy);
		$latte->setSandboxMode();
		$latte->setLoader(new StringLoader($toRender));

		foreach ($toRender as $key => $value){
			$toRender[$key] = $latte->renderToString($key, $params);
		}

		return $toRender;
	}

	public function getTradeStatusMessage(ActiveRow $trade, ActiveRow $tradeStatus = null, string  $message = null, bool $link = false) : string
	{
		/** @var Template $t */
		$t = $this->presenter->createTemplate();
		$t->setFile(APP_DIR."/BackendModule/templates/Trade/@mail_message.latte");
		$t->trade = $trade;
		$t->tradeStatus = $tradeStatus;
		$t->message = $message;
		if($link) {
			$t->link      = $this->context->parameters["url"].$this->presenter->link( ":Backend:Trade:edit", [ "id" => $trade->id ] );
			$t->link_text = "Zobraziť obchod";
		}
		return $t->renderToString();
	}

	/**
	 * @param ActiveRow $user
	 *
	 * @return string
	 */
	public function getUserSignature(ActiveRow $user) : string
	{
		/** @var Template $t */
		$t = $this->presenter->createTemplate();
		$t->setFile(APP_DIR."/BackendModule/templates/User/@signature.latte");
		$t->user = $user;
		return $t->renderToString();
	}

	public function link(string $dest, array $params = []) :string {
		return rtrim($this->presenter->getHttpRequest()->getUrl()->getBaseUrl(),"\/").$this->presenter->link($dest, $params);
	}

	public static function formatDesc(string $desc) : string {
		return nl2br(Strings::replace($desc, '/\{(.*)\}/m', '<strong class="tx-bold">{$1}</strong>'));
	}

	public static function parseDesc(string $desc) : array {
		$matches = Strings::matchAll($desc, '/\{\$(.+?)\}/m');
		$result = [];

		foreach ($matches as $match){
			if(isset($match[1])){
				$result[$match[1]] = $match[1];
			}
		}

		return $result;
	}

	public function createDefaults(?int $company_id = null) : void {
		if($company_id !== null) {
			/** @var CompanyProvider $companyProvider */
			$companyProvider = $this->context->getByType( CompanyProvider::class );
			$this->setCompany($companyProvider->getCompanyById($company_id));
			$this->setAdminMode(false);
		}
		$isset = $this->findAll()->fetchPairs("type","id");
		//bdump("MAILING TEMPLATE");
		$c = 0;
		foreach(self::DEFAULTS as $type => $callable){
		/*	bdump($callable);
			bdump($isset);
			bdump(is_callable($callable, true) && !isset($isset[$type]));*/
			if(is_callable($callable, true) && !isset($isset[$type])) {
				$callable[0] = new $callable[0];
				$value = $callable($this) + ["type" => $type];
				bdump($this->insert($value));
				$c++;
			}
		}

		//throw new InvalidArgumentException();
		//return $c;
	}

	public function getFakeData(ActiveRow $mailingTemplate) : array {
		if(isset(self::DEFAULTS[$mailingTemplate->type])) {
			$callable = self::DEFAULTS[ $mailingTemplate->type ];
			if ( is_callable( $callable, true ) ) {
				$callable[0] = new $callable[0];
				$callable[1] = 'getFakeData';
				$data        = $callable();
				if ( ! empty( $data ) ) {
					return $data;
				}
			}
		}
		$data = [];
		foreach (self::parseDesc($mailingTemplate->desc) as $k => $v){
			$data[$k] = Random::generate(5);
		}

		return $data;
	}

	//public function sendByType()
	/**
	 * @param ActiveRow|int $mailingTemplateType
	 * @param array $parameters
	 * @param string $to
	 * @param array $attachments
	 *
	 * @return ArrayHash
	 */
	public function send(ActiveRow|int $mailingTemplateType, array $parameters, string $to, array $attachments = []) : ArrayHash {
		if($mailingTemplateType instanceof ActiveRow){
			$body = $this->get($mailingTemplateType, $parameters);
		} elseif (is_int($mailingTemplateType)) {
			$body = $this->getByType( $mailingTemplateType, $parameters );
		}

		$mail = $this->context->getByType( IMailBuilderFactory::class )->create();

		if($body->from !== null){
			$mail->setFrom($body->from, $this->getCompany()->company_name);
		}

		$mail->setSubject( $body["subject"] );
		$mail->addTo( $to );
		$mail->setTemplateFile( MAIL_MESSAGE );
		$mail->setParameters( [ "message" => $body["message"], "signature" => "" ] );
		foreach ($attachments as $file){
			if(is_array($file)){
				$mail->getMessage()->addAttachment($file["name"], isset($file["content"]) ? $file["content"] : null, isset($file["mimeType"]) ? $file["mimeType"] : null);
			} else {
				$mail->getMessage()->addAttachment($file);
			}

		}

		if($this->hasAttachments($body->row)){
			foreach ($this->fileManager->listFiles(UPLOAD_DIR . "/" . $this->getAttachmentsDir( $body->row ), false, true) as $file){
				$mail->getMessage()->addAttachment($file["name"], $file["content"], $file["mimeType"]);
			}
		}
		bdump($mail);
		$mail->send();

		return $body;
	}

	/**
	 * @param int|ActiveRow $mailingTemplateType
	 * @param array $parameters
	 * @param string $to
	 * @param ActiveRow|null $sender
	 * @param string|null $attachmentDir
	 * @param string $marketing
	 *
	 * @return array
	 */
	public function queue(ActiveRow|int $mailingTemplateType, array $parameters, string $to, ?ActiveRow $sender = null/*, ?string $attachmentDir = null*/,string $marketing = UserModel::MARKETING_SYSTEM) : ArrayHash {
		//$body = $this->getByType($mailingTemplateType, $parameters);
		if($mailingTemplateType instanceof ActiveRow){
			$body = $this->get($mailingTemplateType, $parameters);
		} elseif (is_int($mailingTemplateType)) {
			$body = $this->getByType( $mailingTemplateType, $parameters );
		}
		$data = [
			"datetime" => new DateTime(),
			"subject" => $body["subject"],
			"text" => $body["message"],
			"to" => MailingModel::TYPE_USERS,
			"from" => ($body->from !== null) ? $body->from : $this->context->parameters["app"]["defaultSmtp"]["username"], // "<EMAIL>",
			"attachments_dir" => time(),
			"visibility" => 1,
			"type" => $marketing
		];
		if($sender !== null){
			$data["user"] = $sender->id;
		}
		if($this->hasAttachments($body->row)){
			$data["attachments_dir"] = $this->getAttachmentsDir($body->row);

			if($this->fileManager->getAdapter() instanceof S3Adapter){
				$data["useS3"] = 1;
			}
		}

		$mailing = $this->context->getByType(ModelFactory::class)->create("mailing")->insert($data);

		$mails = [
			[
				"mailing_id" => $mailing->id,
				"email" => $to
			]
		];
		$this->context->getByType(ModelFactory::class)->create("mailingSend")->insert($mails);

		return $body;
	}

	public function hasAttachments(ActiveRow $mailingTemplate) : int
	{
		return count($this->fileManager->listFiles(UPLOAD_DIR . "/" . $this->getAttachmentsDir( $mailingTemplate ), false, true));
	}

	public function isLinked(ActiveRow $mailingTemplate) : int {
		$res[] = $mailingTemplate->related("trade_status_mailing_template")->count("id");
		$res[] = $mailingTemplate->related("survey_type")->count("id");
		return (int) array_sum($res);
	}
	/**
	 * @param ActiveRow $mailingTemplate
	 *
	 * @return string
	 */
	public function getAttachmentsDir(ActiveRow $mailingTemplate) : string{
		return "mail_template_".$mailingTemplate->id;
	}
}