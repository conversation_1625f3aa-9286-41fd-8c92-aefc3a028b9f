<?php

namespace Webtec\Models;

use Nette;
use Nette\Database\Table\ActiveRow;

class lSectionItemModel extends BaseModelCompany
{
	public $defaults = [
		[
			"name" => "Konkrétny obchod",
			"active" => 1,
			"ordr" => 1,
		]
	];
	const TYPES = [
		2 => "Sumu obchodu",
		1 => "Uskutočnenie obchodu",
	];

	public function createDefaults( ?int $company_id = null ): void {
		$lSectionModel = $this->context->getByType(ModelFactory::class)->create("lSectionType");
		$this->defaults[0]["l_section_type"] = $lSectionModel->findBy(["company_id" => $company_id])->fetch()->id;
		$this->defaults[0]["partner"] = $this->context->getByType(ModelFactory::class)->create("user")->findBy(["type" => UserModel::TYPE_PARTNER,"company_id" => $company_id])->fetch()->id;
		parent::createDefaults( $company_id );
	}

	/**
	 * @param ActiveRow $l_section_item
	 * @param int $career_id
	 *
	 * @return ActiveRow
	 */
	public function getCommisions( ActiveRow $l_section_item, int $career_id = 1) : ActiveRow{
		return $l_section_item->related( "commissions" )->where(["career"=>$career_id, "date <=" => new Nette\Utils\DateTime()])->order( "date DESC" )->fetch();
	}
}

