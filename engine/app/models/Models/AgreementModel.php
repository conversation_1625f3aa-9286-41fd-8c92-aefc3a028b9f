<?php

namespace Webtec\Models;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Loaders\IAgreementGeneratorLoader;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Utils\Strings;
use Latte\Engine;
use Nette;
use Webtec\UI\Forms\Form;

class AgreementModel extends BaseModelCompany
{
	/** @var IAgreementGeneratorLoader */
	protected $loader;

	const TYPE_KUPNA_ZMLUVA = 1;
	const TYPE_DAROVACIA_ZMLUVA =2;
	const TYPE_ZAMENNA_ZMLUVA = 3;
	const TYPE_PREVOD_BYTU = 4;
	const TYPE_SRO_SPOLOCENSKA = 5;
	const TYPE_PRACOVNA_ZMLUVA = 6;
	const TYPE_KUPNA_ZMLUVA_HNUTELNE = 7;
	const TYPE_NAJOMNA_ZMLUVA_HNUTELNE = 8;
	const TYPE_UCTOVNICTVO = 9;

	public static $INPUTS = [];
	public static $FIELDS = [];
	public static $TYPES = [];
	public static $FIELDS_COUNT = [];

	public function onCreate() {
		$component_dir = APP_DIR."/components/AgreementGenerator";
		/** @var IAgreementGeneratorLoader $loader */
		$this->loader = $this->context->getByType(IAgreementGeneratorLoader::class);
		$this->loader->setDir($component_dir);
		if(empty(self::$TYPES)) {
			self::$TYPES = $this->loader->getTypesConfig();
		}
		if(empty(self::$FIELDS)) {
			self::$FIELDS = $this->loader->getFieldsConfig();
		}
		if(empty(self::$INPUTS)) {
			self::$INPUTS = $this->loader->getInputsConfig();
		}
		if(empty(self::$FIELDS_COUNT)) {
			self::$FIELDS_COUNT = $this->loader->getFieldsCountConfig();
		}
	}

	/**
	 * @param int $type
	 *
	 * @return array
	 */
	public function getPlaceholders(int $type) : array
	{
		return isset(AgreementModel::$FIELDS[$type]) ? AgreementModel::$FIELDS[$type] : [];
	}

	/**
	 * @param array $defaults
	 * @param Nette\Database\Table\ActiveRow $agreement
	 * @param bool $silent
	 *
	 * @return string
	 */
	public function generate(array $defaults, Nette\Database\Table\ActiveRow $agreement, $silent = true):string {
		//bdump($defaults);
		$defaults = $this->applyCounter($defaults);

		$placeholders = isset(self::$FIELDS[$agreement->type]) ? self::$FIELDS[$agreement->type] : [];

		/* Nešpecifikované placeholdre */
		$keys = array_diff_key($placeholders, $defaults);
		foreach ($keys as $key => $item){
			if($silent){
				$defaults[ $key ] = "";
			} else {
				$defaults[ $key ] = '<span class="unspecified">Nešpecifikované - ' . $key . '</span>';
			}
		}
		/* Automaticke pridanie keys filled */
		foreach($defaults as $key => $value){
			if(is_string($value)){
				$defaults[$key."_filled"] = (strlen(trim(strip_tags($value))) > 0) ? 1 : 0;
			} elseif(is_array($value)){
				foreach ($value as $k => $v){
					foreach($v as $kk => $vv){
						if(is_string($vv)) {
							$defaults[ $key ][ $k ][ $kk . "_filled" ] = ( strlen( trim( strip_tags( $vv ) ) ) > 0 ) ? 1 : 0;
						}
					}
				}
			}
		}

		$text = $agreement->text;
		$text = str_replace(['<p><br></p>','<br>', '&nbsp;',"\n","\r"],['<br/>','<br/>', ' ','',''],$text);
		$text = Strings::replace($text,'/<span[^>]+\>(.*?)<\/span>/m','$1');
		$text = Strings::replace($text,'/<div[^>]+\>(.*?)<\/div>/m','$1');
		$list_pattern = '/(\{\{\s*list\s+\"([a-zA-Z_]+)\"\s*\}\})(.*?)(\{\{\s*\/list\s*\}\})/mus';
		foreach(Strings::matchAll($text,$list_pattern) as $match){
			$key = $match[2];
			if(isset($defaults[$key]) && is_array($defaults[$key])){
				$texts = [];
				foreach($defaults[$key] as $array){
					$TEXT = $match[3];
					$TEXT = $this->applyIfElseCondition($TEXT,$array);
					$TEXT = $this->applyIfCondition($TEXT,$array);
					foreach ($array as $k => $v) {
						if(is_array($v) || is_string($v)) {
							$TEXT = Strings::replace($TEXT, '/(\{\{(\s*)(' . $k . ')(\s*)\}\})/m', $v);
						}
					}
					$texts[] = $TEXT;
				}

				$text = str_replace($match[0],implode("",$texts), $text);
			} else {
				if(!$silent) {
					$text = str_replace( $match[0], "<span class=\"unspecified\">Nešpecifikované - " . $key . "</span>",
						$text );
				}
			}
		}
		$text = $this->applyIfElseCondition($text,$defaults);
		//exit;

		foreach($defaults as $key => $value){
			if(!is_array($value)) {
				if(isset(self::$INPUTS[$key]["type"]) && self::$INPUTS[$key]["type"] == "addTextArea"){
					$text = Strings::replace( $text, '/(\{\{(\s*)(' . $key . ')(\s*)\}\})/m', nl2br(htmlentities( $value )) );
				} else {
					$text = Strings::replace( $text, '/(\{\{(\s*)(' . $key . ')(\s*)\}\})/m', htmlentities( $value ) );
				}
			}
		}
		//exit;
		foreach (Strings::matchAll($text,'/(\{\{(\s*)(.*?)(\s*)\}\})/m') as $other){
			if(Strings::contains($other[3],".") === true){
				$e = explode(".",$other[3]);
				if(!$silent) {
					$text = Strings::replace( $text, '/(\{\{(\s*)(' . $other[3] . ')(\s*)\}\})/m', Nette\Utils\Arrays::get($defaults,$e,"<span class=\"unspecified\">Nešpecifikované - " . $other[3] . "</span>") );
				} else {
					$text = Strings::replace( $text, '/(\{\{(\s*)(' . $other[3] . ')(\s*)\}\})/m', Nette\Utils\Arrays::get($defaults,$e,"") );
				}
			}
		}


		$headings = [
			"h1" => "font-size:22px;font-weight:bold;",
			"h2" => "font-size:18px;font-weight:bold;",
			"h3" => "font-size:16px;font-weight:bold;"
		];
		foreach ($headings as $h => $style){
			$text = Strings::replace($text,'/<'.$h.'([^>]style=\"(.*?)\")\>(.*?)<\/'.$h.'>/m','<p style="$2;'.$style.'">$3</p>');
			$text = Strings::replace($text,'/<'.$h.'\>(.*?)<\/'.$h.'>/m','<p style="'.$style.'">$1</p>');
		}
		if($silent) {
			$text = str_replace( [ '<p><br></p>', '<br>', '&nbsp;', "\n", "\r" ], [ '<br/>', '<br/>', ' ', '', '' ],
				$text );
			$text = Strings::replace( $text, '/<span[^>]+\>(.*?)<\/span>/m', '$1' );
			$text = Strings::replace( $text, '/<div[^>]+\>(.*?)<\/div>/m', '$1' );
		}
		return $text;
	}

	/**
	 * @param string $TEXT
	 * @param array $values
	 *
	 * @return string
	 */
	public function applyIfElseCondition(string $TEXT, array $values) : string {
		$if_else_pattern = '/(\{\{\s*if\s+\"([a-zA-Z_0-9.]+)\"\s*\}\})(.*)(\{\{\s*else\s*\}\})(.*)(\{\{\s*\/if\s*\}\})/mus';
		$array = $values;
		foreach(Strings::matchAll($TEXT, $if_else_pattern) as $if) {
			if(Strings::contains($if[2],".") === true){
				$if_value = Nette\Utils\Arrays::get($values,explode(".",$if[2]), 0);
			} else {
				$if_value = isset( $array[ $if[2] ] ) ? (int) $array[ $if[2] ] : 0;
			}
			$if_text1 = $if[3];
			$if_text2 = $if[5];
			if($if_value){
				$TEXT = str_replace($if[0],$this->applyIfBlockCondition($if_text1,$values),$TEXT);
			} else {
				$TEXT = str_replace($if[0],$this->applyIfBlockCondition($if_text2,$values),$TEXT);
			}
		}
		return $TEXT;
	}

	/**
	 * @param string $TEXT
	 * @param array $values
	 *
	 * @return string
	 */
	public function applyIfCondition(string $TEXT, array $values) : string {

		$if_pattern = '/(\{\{\s*if\s+\"([a-zA-Z_]+)\"\s*\}\})(.*?)(\{\{\s*\/if\s*\}\})/mus';

		$array = $values;
		//dump($TEXT);
		//echo $TEXT;
		foreach(Strings::matchAll($TEXT, $if_pattern) as $if) {
			$if_value = isset($array[$if[2]]) ? (int) $array[$if[2]] : 0;
			$if_text1 = $if[3];
			if($if_value){
				$TEXT = str_replace($if[0],$this->applyIfCondition($if_text1,$values),$TEXT);
			} else {
				$TEXT = str_replace($if[0],"",$TEXT);
			}
		}
		return $TEXT;
	}

	/**
	 * @param string $TEXT
	 * @param array $values
	 *
	 * @return string
	 */
	public function applyIfBlockCondition(string $TEXT, array $values) : string {

		$if_pattern = '/(\{\{\s*ifblock\s+\"([a-zA-Z_]+)\"\s*\}\})(.*?)(\{\{\s*\/ifblock\s*\}\})/mus';

		$array = $values;
		//dump($TEXT);
		//echo $TEXT;
		foreach(Strings::matchAll($TEXT, $if_pattern) as $if) {
			$if_value = isset($array[$if[2]]) ? (int) $array[$if[2]] : 0;
			$if_text1 = $if[3];
			if($if_value){
				$TEXT = str_replace($if[0],$this->applyIfCondition($if_text1,$values),$TEXT);
			} else {
				$TEXT = str_replace($if[0],"",$TEXT);
			}
		}
		return $TEXT;
	}

	/**
	 * @param string $key
	 * @param string $name
	 *
	 * @return string
	 */
	public function placeholderHTML(string $key, string $name) : string {
		return $this->loader->getPlaceholder($key, $name, $this->context);
	}

	/**
	 * @param array $defaults
	 *
	 * @return array
	 */
	public function applyCounter(array $defaults):array{
		$list = [
			"seller" => [$this,"counterRoman"],
			"buyer" => [$this, "counterRoman"],
			"changer" => [$this,"counterClassic"],
		];
		foreach ($list as $key => $callback){
			if(isset($defaults[$key])){
				if(count($defaults[$key]) > 1){
					$counter = 1;
					foreach($defaults[$key] as $k => $item){
						$defaults[$key][$k]["counter"] = $callback($counter);
						$counter++;
					}
				} else {
					foreach($defaults[$key] as $k => $item) {
						$defaults[ $key ][ $k ]["counter"] = "";
					}
				}
			}
		}
		return $defaults;
	}

	/**
	 * @param int $type
	 * @param array $values
	 *
	 * @return array
	 */
	public static function formValuesToStack(int $type,array $values):array {
		//$values = $v["type_".$type];

		$fields = AgreementModel::$FIELDS[$type];
		$counter = AgreementModel::$FIELDS_COUNT;
		bdump($values);
		$stack = [];
		foreach($fields as $key => $name){
			$c = isset($counter[$key]) ? $counter[$key] : 0;
			if($c > 0) {
				for ( $i = 0; $i < $c; $i ++ ) {
					if($key == "prices" || $key == "company_subject") {
						if ( strlen( trim( $values[ $key . "_" . $i ]["text"] ) ) > 0 ) {
							$stack[ $key ][] = (array) $values[ $key . "_" . $i ];
						}
					}elseif ($key == "buyer" || $key == "seller" || $key == "changer" || $key== "partner" || $key=="manager" || $key=="owner" /*|| $key == "person"*/) {
						if ( $values[ $key . "_" . $i ]["company"] ) {
							if ( strlen( trim( $values[ $key . "_" . $i ]["company_name"] ) ) > 0 ) {
								$stack[ $key ][] = (array) $values[ $key . "_" . $i ];
							}
						} else {
							if ( strlen( trim( $values[ $key . "_" . $i ]["fullname"] ) ) > 0 ) {
								$stack[ $key ][] = (array) $values[ $key . "_" . $i ];
							}
						}
					} elseif(Strings::startsWith($key,"subject") /*$key == "subject"*/) {
						if ( strlen( trim( $values[ $key . "_" . $i ]["nazov"] ) ) > 0 ) {
							$stack[ $key ][] = (array) $values[ $key . "_" . $i ];
						}

					} else {
						$stack[ $key ][] = (array) $values[ $key . "_" . $i ];
					}

				}
			} else {
				$stack[$key] = $values[$key];
			}
		}
		return $stack;
	}

	public static function counterFromStack(array $stack) : array{
		$counter = [];
		foreach(AgreementModel::$INPUTS as $key => $input){
			if(isset($stack[$key]) && isset($input["items"])){
				$counter[$key] = count($stack[$key]);
			}
		}
		return $counter;
	}

	/**
	 * @param Form $f
	 * @param Nette\Forms\Controls\BaseControl $TYPE
	 *
	 * @return Form
	 */
	public static function addInputs(Form $f, Nette\Forms\Controls\BaseControl $TYPE, ?int $onlyType = null){
		bdump(AgreementModel::$TYPES,"ADDINPUTS TYPES");
		bdump($onlyType, "ONLY TYPE");

		$TYPES = AgreementModel::$TYPES;
		if($onlyType !== null){
			$TYPES = [];
			$TYPES[$onlyType] = AgreementModel::$TYPES[$onlyType];
		}
		bdump($TYPES, "ADD INPUT TYPES");
		foreach($TYPES as $type_id => $type_name){
			$container_id = "type_".$type_id;
			$TYPE->addCondition($f::EQUAL,$type_id)->toggle($container_id."_container")->endCondition();

			$c = $f->addContainer("type_".$type_id);
			foreach(AgreementModel::$FIELDS[$type_id] as $field_key => $field_name) {
				if(isset(AgreementModel::$INPUTS[$field_key])) {
					$input = AgreementModel::$INPUTS[$field_key];
					$input_type = isset($input["type"]) ? $input["type"] : "addText";
					//bdump($input,$field_key);

					if(!isset($input["items"])) {
						/** @var Nette\Forms\Controls\TextInput $added_input */
						$added_input = $c->{$input_type}( $field_key, $field_name );//->setHtmlAttribute( "id", $container_id . "_container" );
						if(isset($input["class"])){
							$added_input->setHtmlAttribute("class",$input["class"]);
						}
						if(isset($input["required"]) && $input["required"] === true){
							$added_input->addConditionOn($f["type"],$f::EQUAL,$type_id)->setRequired();
						}
						if(isset($input["defaultValue"])){
							$added_input->setDefaultValue($input["defaultValue"]);
						}
					} else {
						$TOGGLE = null;
						$TOGGLE_ITM = null;
						$counter = (isset(AgreementModel::$FIELDS_COUNT[$field_key]) ? AgreementModel::$FIELDS_COUNT[$field_key] : 0);
						for($i=0; $i<$counter; $i++){
							$subc = $c->addContainer($field_key."_".$i);
							//bdump($input);
							foreach ($input["items"] as $itm_key => $itm){
								$itm_type = isset($itm["type"]) ? $itm["type"] : "addText";
								$itm_name = isset($itm["name"]) ? $itm["name"] : $itm_key;
								$added_itm = $subc->{$itm_type}($itm_key,$itm_name);
								if(isset($itm["required"]) && $itm["required"] === true && $i == 0 && !isset($itm["container"])){
									$added_itm->addConditionOn($f["type"],$f::EQUAL,$type_id)->setRequired()->endCondition();
								}
								if(isset($itm["toggle"]) && !empty($itm["toggle"])){
									//bdump("TOGGLE");

									foreach($itm["toggle"] as $toggle_container => $toggle_value) {
										$added_itm->addCondition( $f::EQUAL ,$toggle_value )->toggle($toggle_container."-".$type_id."-".$field_key."_".$i."-container")->endCondition();
									}
									$TOGGLE = $added_itm;
									$TOGGLE_ITM = $itm;
								}
								if(isset($itm["required"]) && $itm["required"] === true && $i == 0 && isset($itm["container"])){
									if($TOGGLE!==null && $TOGGLE_ITM !== null) {
										$added_itm->addConditionOn($f["type"],$f::EQUAL,$type_id)
										          ->addConditionOn($TOGGLE,$f::EQUAL,$TOGGLE_ITM["toggle"][$itm["container"]])
										          ->setRequired()
										          ->endCondition()->endCondition();

									}
								}
							}
						}
					}
				}
			}
		}
		return $f;
	}

	private function counterRoman(int $counter) {
		return " v ".Strings::toRomanInt($counter).". rade";
	}

	private function counterClassic(int $counter){
		return " č.".$counter;
	}

}
