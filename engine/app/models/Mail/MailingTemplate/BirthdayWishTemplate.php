<?php
namespace Ku<PERSON>mi<PERSON>ta\Mailing\Template;

final class BirthdayWishTemplate extends MailingTemplate {
	protected function getSubject(): string {
		return 'Vš<PERSON><PERSON> najlepšie k narodeninám';
	}
	protected function getMessage(): string {
		return <<<EOT
Milý/á {\$fullname}<br><br>
Dovoľte nám v mene celého adv Vám zagratulovať k Vašim narodeninám. Prajeme Vám veľa šťastia, zdravia a lásky a veľa úspešných obchodov.<br><br>
{\$signature|noescape}
EOT;

	}
	protected function getDesc(): array {
		return [
			'{$fullname} - Meno a priezvisko',
			'{$signature} - podpis odosielateľa'
		];
	}

	public function getFakeData(): array {
		return [
			'fullname' => "Meno Priezvisko",
			'signature' => '-----------------------------------------------<div style="font-size:13px;"> S pozdravom <div style="margin-top: 10px"> <strong> Odosielateľ Priezvisko </strong> <br> +4219xx yyy zzz<br> <EMAIL><br> </div> </div>'
		];
	}
}