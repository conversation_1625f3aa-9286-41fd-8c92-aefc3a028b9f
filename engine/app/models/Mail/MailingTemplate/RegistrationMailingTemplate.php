<?php
namespace Kubomikita\Mailing\Template;

final class RegistrationMailingTemplate extends MailingTemplate {

	protected function getSubject(): string {
		return 'Vytvorenie účtu {$name} {$surname} - potvrdenie registrácie';
	}

	protected function getMessage(): string {
		return <<<EOT
<h2 style="margin-bottom:5px">Potvrdenie registrácie</h2>
<h3 style="margin-bottom:5px;color:#666;"><PERSON>b<PERSON><PERSON> {\$name} {\$surname}</h3>
<p>
	Týmto Vám potvrdzujeme, že ste boli zaregistrovaný do nášho obchodného systému.
</p>
<p>
	Pre úspešne dokončenie registrácie si dôkladne prečítajte zmluvnú dokumentáciu priloženú v prílohe tohto e-mailu. Potvrdením Vašej registrácie potvrdzujete, že ste boli oboznámeny s obsahom priloženej zmluvnej dokumentácie, ako aj že súhlasite s jej obsahom a s poskytnutím Vaších osobných údajov.
</p>
<p>
	<a href="{\$link}">Pre potvrdenie registrácie kliknite tu.</a>
	<div style="font-size:12px">Nezabudnite že odkaz platí iba {\$interval|humantime}</div>
</p>
<p>
	S pozdravom,<br>
	<br>
	tím {\$company}
</p>
EOT;

	}

	protected function getDesc(): array {
		return [
			'{$name} - meno',
			'{$surname} - priezvisko',
			'{$link} - odkaz na aktiváciu',
			'{$interval} - platnosť odkazu',
			'{$company} - názov firmy',
			'{$ident} - identifkátor',
		];
	}
	public function getFakeData(): array {
		return [
			'name' => 'Meno',
			'surname' => 'Priezvisko',
			'link' => 'https://www.google.sk',
			'interval' => 60 * 60 * 3,
			'company' => 'Firma s.r.o.',
			'ident' => "SK000000000",
		];
	}
}