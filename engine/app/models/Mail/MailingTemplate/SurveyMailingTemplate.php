<?php

namespace <PERSON>bomi<PERSON>ta\Mailing\Template;


final class SurveyMailingTemplate extends MailingTemplate {

	protected function getSubject(): string {
		return 'Online dotazník od {$username}';
	}

	protected function getMessage(): string {
		return <<<EOT
<h2>Online dotazník</h2>
<p><PERSON><PERSON><PERSON><PERSON>, {\$username} Vám zasiela odkaz na vyplnenie online dotazníka ({\$type}). </p>
<p>{\$text}</p>
<div style="text-align: center; padding: 25px 0">
	<a style="padding:15px 30px;
		color:#FFF;
		background: #23BF08;
		text-decoration: none;
		border-radius: 3px;" href="{\$link}">Vyplniť online dotazník</a>
</div>

EOT;

	}


	protected function getDesc():array{
		return [
			'{$username} - užívateľ kt. vytvoril dotazník',
			'{$link} - odkaz na dotazník',
			'{$type} - typ dotazníka',
			'{$text} - text dotazníka',
		];
	}

	public function getFakeData(): array {
		return [
			'username' => '<PERSON><PERSON>rie<PERSON>',
			'link' => 'https://www.google.sk',
			'type' => 'Typ dotazníka',
			'text' => 'Tu bude text dotazníka.'
		];
	}
}