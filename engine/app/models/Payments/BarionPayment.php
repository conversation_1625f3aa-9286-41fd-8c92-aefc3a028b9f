<?php

namespace App\Payments;

use Kubomikita\Services\CompanyProvider;
use Nette\DI\Container;
use Nette\Http\Request;
use Nette\Security\User;

if(file_exists(LIBS_DIR . '/barion/barion-web-php/library/helpers/loader.php')) {
	require_once LIBS_DIR . '/barion/barion-web-php/library/BarionClient.php';
	require_once LIBS_DIR . '/barion/barion-web-php/library/helpers/loader.php';
}
class Barion implements Payment
{
	private readonly string $environment;
	private readonly array $config;
	private string $userEmail;
	private \BarionClient $barionClient;

	public function __construct(Container $container, private readonly Request $httpRequest)
	{
		$this->config = $container->parameters["payments"]["barion"];
	}

	public function createClient(string $posKey,string $userEmail, ?bool $sandBoxMode = null) : void
	{
		$this->userEmail = $userEmail;
		if($sandBoxMode === null){
			$this->environment = ($this->config["sandbox"] ?? false) ? \BarionEnvironment::Test : \BarionEnvironment::Prod;
		} else {
			$this->environment = ($sandBoxMode ?? false) ? \BarionEnvironment::Test : \BarionEnvironment::Prod;
		}
		$this->barionClient = new \BarionClient($posKey, 2, $this->environment, true);
	}
	public function isValidResponse(mixed $response) : bool
	{
		if($response instanceof \PaymentStateResponseModel) {
			return $response->RequestSuccessful && empty($response->Errors) && ($response->Status === 'Reserved' || $response->Status === "Succeeded");
		}
		return false;
	}
	public function preparePayment(string $transactionId, int $variableSymbol, string $payerEmail, float $amount, array $itms, ?string $comment = null) : ?string
	{
		$bc = $this->barionClient;
		$items = [];
		foreach ($itms as $itm){
			$item = new \ItemModel();
			$item->Name = $itm["name"];
			$item->Description = $itm["desc"] ?? $itm["name"];
			$item->Quantity = $itm["quantity"];
			$item->Unit = (string) $itm["unit"];
			$item->UnitPrice = (string) $itm["unitPrice"];
			$item->ItemTotal = (string) $itm["totalPrice"];
			$item->SKU = $itm["sku"] ?? '';

			$items[] = $item;
		}

		$trans = new \PaymentTransactionModel();
		$trans->POSTransactionId = $transactionId; //"TRANS-01";
		$trans->Payee = $this->userEmail;
		$trans->Total = (string) $amount;


		$trans->Comment = "Objednávka ".$variableSymbol;
		$trans->AddItems($items);

		$ppr = new \PreparePaymentRequestModel();
		$ppr->GuestCheckout = true;
		$ppr->PaymentType = \PaymentType::Immediate;
		$ppr->ReservationPeriod = "9:00:00:00";
		$ppr->PaymentWindow = "00:20:00";
		$ppr->FundingSources = [\FundingSourceType::All];
		$ppr->PaymentRequestId = "PAYMENT-".date("Y-m-d-H-i-s");
		$ppr->PayerHint = $payerEmail; //"<EMAIL>";
		$ppr->Locale = \UILocale::SK;
		$ppr->OrderNumber = $variableSymbol;
		$ppr->Currency = 'EUR';
		$ppr->RedirectUrl = $this->httpRequest->getUrl()->getBaseUrl().ltrim($this->config["redirectUrl"],"/");//"/api/v2/payment-gateway/barion-company";//$barionProvider->getConfig("redirectUrl");
		//$ppr->CallbackUrl = //$barionProvider->getConfig("callbackUrl");
		$ppr->AddTransaction($trans);


		$myPayment = $bc->PreparePayment($ppr);


		if ($myPayment->RequestSuccessful === true) {
			return $myPayment->PaymentRedirectUrl;
		}
		if(!empty($myPayment->Errors)){
			bdump($myPayment->Errors);
		}
		return null;
	}

	public function getBarionClient(): \BarionClient
	{
		return $this->barionClient;
	}

	public static function isInvoicingAllowed(CompanyProvider $companyProvider) : bool
	{
		return $companyProvider->company->invoicing_barion && $companyProvider->getDefaultPresenter()->isSuperadmin(); //($companyProvider->getDefaultPresenter()->isSuperSuperadmin() || $companyProvider->getContainer()->getByType(User::class)->getIdentity()->getData()["username"] === "<EMAIL>");
	}
}