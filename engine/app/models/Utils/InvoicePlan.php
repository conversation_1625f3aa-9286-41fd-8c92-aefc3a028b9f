<?php
declare(strict_types=1);


namespace <PERSON><PERSON><PERSON><PERSON>ta\Utils;


use BackendModule\BasePresenter;
use BackendModule\InvoiceModule\CashRegisterPresenter;
use BackendModule\InvoiceModule\ExpensePresenter;
use Nette\Application\InvalidPresenterException;
use Nette\Application\PresenterFactory;
use Nette\Application\UI\Presenter;
use Nette\Database\Table\ActiveRow;
use Nette\InvalidArgumentException;
use Nette\Utils\ArrayHash;
use Webtec\Models\InvoicePlanModel;

final class InvoicePlan {

	public ?ActiveRow $plan = null;
	public int $id;
	public string $ident;
	public string $name;
	public bool $pay_by_square = false;

	public \DateTimeInterface $planEnd;
	public int $planEndDays;
	public bool $planExpired = false;
	public bool $planTrial = false;

	protected array $allowedModules = [];
	protected array $allowedInvoiceTypes = [1=>"regular"];
	protected ?ActiveRow $userPlan = null;


	public function __construct(?ActiveRow $row = null){
		if($row !== null) {
			$this->userPlan = $row;
			$this->plan  = $row->ref("invoice_plan");
			$this->id    = $this->plan->id;
			$this->ident = $this->plan->ident;
			$this->name  = $this->plan->name;
			$this->pay_by_square = !!$this->plan->pay_by_square;

			$this->planEnd     = $row->date_end;
			$dateDiff          = ((new DateTime())->diff($row->date_end->modifyClone("+1 day")));
			$this->planEndDays = $dateDiff->days;
			if ($dateDiff->invert) {
				$this->planEndDays *= -1;
			}

			$this->planTrial   = ! ! $row->trial;
			$this->planExpired = $this->planEndDays < 1;

			foreach (InvoicePlanModel::PermanentModules as $module) {
				$this->allowedModules[$module] = $module;
			}
			$this->allowedModules += $this->plan->related("invoice_plan_module")->fetchPairs("module", "module");
			$this->allowedInvoiceTypes += $this->plan->related("invoice_plan_type")->select("invoice_plan_type.*, invoice_type.key AS type_key")->fetchPairs("invoice_type", "type_key");
		}
		//bdump($this);

	}

	public function allowed(string|Presenter|null $presenter = null, bool $linkString = false) : bool
	{
		if($this->userPlan !== null) {
			if ($presenter === null) {
				$db = debug_backtrace();

				if (isset($db[1]["object"]) && in_array($db[1]["object"]::class, $this->allowedModules)) {
					return true;
				}

			} else {
				if ($presenter instanceof \BackendModule\InvoiceModule\BasePresenter) {
					$presenter = $presenter::class;
				}

				if($linkString){
					$presenter = $this->linkToPresenter($presenter);
				}

				return isset($this->allowedModules[$presenter]);
			}
			return false;
		}
		return true;
	}

	public function allowedInvoiceType(?string $key= null) : bool {
		if($this->userPlan !== null){
			if($key === null){
				$db = debug_backtrace();
				if (isset($db[1]["object"]) && $db[1]["object"] instanceof \BackendModule\InvoiceModule\BasePresenter){
					$presenter = $db[1]["object"];
				}
				return isset($this->allowedInvoiceTypes[$presenter->selectedInvoiceType->id]);
			} else {
				return in_array($key, $this->allowedInvoiceTypes);
			}

		}
		return true;
	}

	private function linkToPresenter(string $link) : string
	{
		$link = str_starts_with($link,":") ? substr($link,1) : $link;
		$e = explode(":", $link);
		array_pop($e);
		$link = implode(":", $e);
		return (new PresenterFactory())->getPresenterClass($link);
	}

}