<?php

namespace Webtec\Security;

use Kubomikita\Factory\CacheFactory;
use Kubomikita\Services\CompanyProvider;
use Nette;
use Nette\Security\Permission;

/**
 * @property-write \Nette\Database\Connection $database
 */
class DatabaseAuthorizator extends Nette\Security\Permission
{
	/** @var \Nette\Database\Connection */
	protected $database;
	/** @var Nette\Security\IIdentity */
	protected $identity;
	/** @var Nette\Caching\Cache */
	protected $cache;
	/** @var Nette\Http\Session */
	protected $session;
	/** @var CompanyProvider */
	protected $companyService;
	/** @var int */
	protected $company_id;


	/**
	 * DatabaseAuthorizator constructor.
	 *
	 * @param Nette\Database\Connection $database
	 * @param CacheFactory $cacheFactory
	 * @param Nette\Http\Session $session
	 */
	public function __construct(Nette\Database\Connection $database, CacheFactory $cacheFactory, Nette\Http\Session $session, CompanyProvider $companyService)
	{
		$this->cache = $cacheFactory->create("authorizator");
		$this->session = $session;
		$this->companyService = $companyService;
		$this->company_id = $this->companyService->getCompany() !== null ? $this->companyService->getCompany()->id : 0;
		$this->database = $database;
		$this->loadAcl();
		$section = $this->session->getSection("admin");
		if(!isset($section->superadmin)){
			$section->superadmin = true;
		}
	}

	/**
	 * @param Nette\Security\IIdentity $identity
	 */
	public function setIdentity( Nette\Security\IIdentity $identity ): void {
		$this->identity = $identity;
	}

	public function isAllowed( $role = self::ALL, $resource = self::ALL, $privilege = self::ALL ):bool {
		try {
			return $this->isSuperadmin() || parent::isAllowed( $role, $resource, $privilege );
		} catch (Nette\InvalidStateException $e){
			if(PHP_SAPI !== "cli") {
				trigger_error($e->getMessage(), E_USER_NOTICE);
			}
		}
		return false;
	}

	public function isSuperadmin(){
		if($this->identity instanceof Nette\Security\IIdentity && $this->session->getSection("admin")->superadmin) {
			return $this->identity->superadmin;
		}
		return false;
	}


	/**
	 * @return void
	 */
	public function loadAcl()
	{
		$roles = [];
		foreach ($this->loadRoles() as $role) {
			$this->addRole($role->name, $role->parent_name);
			$roles[$role->name] = $role->name;
		}

		foreach ($this->loadResources() as $resource) {
			$this->addResource($resource->name, $resource->parent_name);
		}
		//bdump($this->loadRules());
		foreach ($this->loadRules() as $rule) {
			if(isset($roles[$rule->role])) {
				$this->{$rule->allowed ? 'allow' : 'deny'}( $rule->role, $rule->resource, $rule->privilege );
			}
		}
	}



	/**
	 * @return mixed
	 */
	public function loadRoles()
	{
		//dumpe($this->company_id);
		$roles = $this->cache->load("roles".$this->company_id,function (){
			return $this->database->query(
				"SELECT " .
				"DISTINCT(r1.name) AS name," .
				"r2.name AS parent_name " .
				"FROM acl_role AS r1 " .
				"LEFT JOIN acl_role AS r2 ON r1.acl_role_id=r2.id ".
				//"GROUP BY r1.name"
				"WHERE r1.company_id = ?", $this->company_id
			)->fetchAll();
		});
		//bdump($roles);
		return $roles;

	}



	/**
	 * @return mixed
	 */
	public function loadResources()
	{
		$resources = $this->cache->load("resources".$this->company_id,function () {
			return $this->database->query(
				"SELECT " .
				"r1.name AS name," .
				"r2.name AS parent_name " .
				"FROM acl_resource AS r1 " .
				"LEFT JOIN acl_resource AS r2 ON r1.acl_resource_id=r2.id"
			)->fetchAll();
		});
		return $resources;
	}



	/**
	 * @return mixed
	 */
	public function loadRules()
	{
		$rules = $this->cache->load("rules".$this->company_id,function (&$dependencies){
			$dependencies = [Nette\Caching\Cache::EXPIRE => "1 day"];
			return $this->database->query(
				"SELECT " .
				"a.allowed AS allowed," .
				"ro.name AS role," .
				"re.name AS resource," .
				"p.name AS privilege " .
				"FROM acl AS a " .
				"JOIN acl_role AS ro ON a.acl_role_id=ro.id " .
				"LEFT JOIN acl_resource AS re ON a.acl_resource_id = re.id " .
				"LEFT JOIN acl_privilege AS p ON a.acl_privilege_id = p.id"
			)->fetchAll();
		});
		return $rules;

	}
}
