<?php
namespace Kubomikita\Services\Imap;


use Ddeboer\Imap\Message\Attachment;

final class DdeboerAttachmentProvider implements AttachmentProvider {

	private Attachment $attachment;
	public function __construct($attachment)
	{
		$this->attachment = $attachment;
	}

	public function getFilename(): string
	{
		return pathinfo($this->attachment->getFilename(), PATHINFO_FILENAME);
	}

	public function getExtension(): string
	{
		return pathinfo($this->attachment->getFilename(), PATHINFO_EXTENSION);
	}

	public function getBody(): string
	{
		return $this->attachment->getDecodedContent();
	}
}