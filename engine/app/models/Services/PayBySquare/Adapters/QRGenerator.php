<?php
declare(strict_types=1);

namespace App\Services\PayBySquare\Adapter;


use GuzzleHttp\Client;
use Nette\Utils\Json;

class QRGenerator implements \App\Services\PayBySquare\PayBySquareAdapter {

	const API_ENDPOINT = 'https://api.qrgenerator.sk/by-square/pay/base64';
	public function getQrCode(array $data) : ?string
	{

		$qry = [
			"format" => "jpg",
			"iban" => $data["iban"],
			"amount" => $data["amount"],
			"currency" => $data["currency"],
			"vs" => $data["variable"],
			"due_date" => isset($data["date_due"]) ? $data["date_due"]->format("Y-m-d") : null,
			"size" => 256,
			"ss" => $data["specific"] ?? '',
			"cs" => $data["constant"] ?? '',
			"transparent"=> "false",
			"payment_note" => $data["note"] ?? ''
		];

		if(isset($data["mode"])){
			$qry["mode"] = $data["mode"];
		}
		//bdump($qry);
		$client = new Client();
		$result = $client->get(static::API_ENDPOINT, [
			'query' => $qry
		]);

		try {
			$response = Json::decode($result->getBody()->getContents());

			return $response->data;
		} catch (\Throwable){}


		return null;
	}
}