<?php

namespace Kubomikita;

use Contributte\Deployer\Config\Config;
use Contributte\Deployer\Config\Section;
use Contributte\Deployer\Listeners\AfterListener;
use Contributte\Deployer\Listeners\BeforeListener;
use Contributte\Deployer\Utils\System;
use Deployment\Deployer;
use Deployment\Logger;
use Deployment\Server;
use Nette\Utils\ArrayHash;

class DeployListener implements AfterListener, BeforeListener {

	private array $searchFor;
	private array $replaceFor;
	private array $files;
	public function testAfter(Config $config, Section $section, Server $server, Logger $logger, Deployer $deployer) :void {
		$logger->log("test after","aqua");
	}

	private function loadConfig($webname): void
	{
		$this->searchFor = [
			"#RewriteBase /".$webname."/"
			//"ErrorDocument 404 /".$webname."/",
			//"RewriteBase /".$webname."/",
			//"RewriteRule ^imgcache/(.*)$ /".$webname."/imgcache/$1",
			//"RewriteRule ^imgs/(.*)$ /".$webname."/imgs/$1",
			//"RewriteRule ^imgi/(.*)$ /".$webname."/imgi/$1",
			//"RewriteRule ^imgc/(.*)$ /".$webname."/imgc/$1",
			//"RewriteRule ^img.php$ /".$webname."/img.php"
		];
		$this->replaceFor = [
			//"ErrorDocument 404 /",
			"RewriteBase /",
			//"RewriteRule ^imgcache/(.*)$ /imgcache/$1",
			//"RewriteRule ^imgs/(.*)$ /imgs/$1",
			//"RewriteRule ^imgi/(.*)$ /imgi/$1",
			//"RewriteRule ^imgc/(.*)$ /imgc/$1",
			//"RewriteRule ^img.php$ /img.php"
		];

		$this->files = [".htaccess"/*,"imgcache/.htaccess","commerce/manager/.htaccess"*/];
	}

	public function onAfter(Config $config, Section $section, Server $server, Logger $logger, Deployer $deployer) :void
	{
		$webname = $section->getName();
		$this->loadConfig($webname);
		foreach($this->files as $file){
			$p = file_get_contents($section->getLocal()."/".$file);
			$new = str_replace($this->searchFor,$this->replaceFor,$p);
			file_put_contents($section->getLocal()."/".$file.".production",$new);
			$server->writeFile($section->getLocal()."/".$file.".production",$server->getDir()."/".$file);
			//unlink($section->getLocal()."/".$file.".production");
			$logger->log("File: ".$file." changed to production and uploaded.");
		}

		$this->opcache($webname, $logger);
	}
	public function onBefore(Config $config, Section $section, Server $server, Logger $logger, Deployer $deployer) :void {
		$logger->log("OnBefore listener","navy");
		//System::run(sprintf('composer update --no-dev --prefer-dist --optimize-autoloader -d %s', ""), $return);
		//$logger->log($return);
	}
	public function startTests(Config $config, Section $section, Server $server, Logger $logger, Deployer $deployer) :void {
		$logger->log("Starting UNIT tests", "navy");
		$logger->log("---------------------------------","navy");
		System::run("php engine/libs/nette/tester/src/tester.php engine/tests -C --colors 1",$return);
		if($return > 0){
			$logger->log("============================================","red");
			$logger->log("=========== UNIT TEST FAILED ===============","red");
			$logger->log("============================================","red");
			exit;
		} else {
			$logger->log("UNIT TEST PASSED","lime");
			$logger->log("----------------","lime");
		}
	}

	public function afterDeploySSH(Config $config, Section $section, Server $server, Logger $logger, Deployer $deployer) : void {
		$userdata = ArrayHash::from($config->getUserdata());

		if(!empty($userdata->ssh2->commands)){
			$conn = ssh2_connect($userdata->ssh2->host);
			//ssh2_auth_pubkey_file()
			ssh2_auth_password($conn, $userdata->ssh2->user,$userdata->ssh2->pass);

			if(!$conn){
				$logger->log("Failed to connect ".$userdata->ssh2->user."@".$userdata->ssh2->host, "red");
			} else {

				foreach ($userdata->ssh2->commands as $command) {
					$stream = ssh2_exec( $conn, $command );
					stream_set_blocking( $stream, true );
					$stream_out = ssh2_fetch_stream( $stream, SSH2_STREAM_STDIO );
					$logger->log("cmd: ".$command,"aqua");
					$result = stream_get_contents( $stream_out );
					if(strlen($result) > 0) {
						$logger->log($result , "lime" );
					} else {
						$logger->log("executed","fuchsia");
					}
				}

				ssh2_disconnect( $conn );
			}
		}

		//$logger->log($userdata->ssh2->commands[0], "red");
	}

	protected function opcache(string $webname, Logger $logger) {
		try {
			$flushUrl = 'https://www.' . $webname . "/api/cache/opcache?flush=1";

			$json    = \Nette\Utils\Json::decode(file_get_contents($flushUrl), \Nette\Utils\Json::FORCE_ARRAY);
			$restart = (int) $json["status"]["restartPending"];
			$logger->log("OPCache restart ($webname): " . ($restart ? 'OK' : "FAIL"), ($restart ? "lime" : "red"));
		} catch (Throwable $e){
			$logger->log("ERROR in opcache restart. ($webname)", "red");
		}
	}
}