<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Logging\Processor;



use Nette\Http\Session;
use Sentry\Event;
use Sentry\EventHint;

final class SentrySessionContextProcessor {
	/** @var Session  */
	private $session;

	public function __construct(Session $session) {
		$this->session = $session;
	}
	public function __invoke(Event $record, EventHint $payload) : Event
	{
		if ($this->session) {
			/*bdump($this->session);
			bdump($_SESSION['__NF']['DATA']);
			bdump($_SESSION);
			$data = [];
			foreach ($this->session->getIterator() as $section) {
				foreach ($this->session->getSection($section)->getIterator() as $key => $val) {
					$data[$section][$key] = $val;
				}
			}*/
			$record->setExtra($record->getExtra() + ['session' =>  $_SESSION['__NF']['DATA']]);
		}
		return $record;
	}
}