<?php

namespace Kubomikita\Events;


use Nette\Database\Table\ActiveRow;
use Webtec\Models\AclRoleModel;
use Webtec\Models\CareerModel;
use Webtec\Models\CareerPositionModel;
use Webtec\Models\CronModel;
use Webtec\Models\lSectionItemModel;
use Webtec\Models\lSectionModel;
use Webtec\Models\lSectionTypeModel;
use Webtec\Models\MailingTemplateModel;
use Webtec\Models\MeetingMeetingTypeModel;
use Webtec\Models\OfficeModel;
use Webtec\Models\TradeStatusModel;
use Webtec\Models\UserModel;

class CompanyEvents extends BaseEvent {
	public function onCreate(ActiveRow $company){
		/** @var AclRoleModel $aclRoleModel */
		$aclRoleModel = $this->modelFactory->create("aclRole");
		$aclRoleModel->createDefaults($company->id);

		/** @var CareerModel $careerModel */
		$careerModel = $this->modelFactory->create("career");
		$careerModel->insert(["company_id" => $company->id, "name" => $company->name]);

		/** @var CareerPositionModel $careerPositionModel */
		$careerPositionModel = $this->modelFactory->create("careerPosition");
		$careerPositionModel->createDefaults($company->id);

		/** @var TradeStatusModel $tradeStatusModel */
		$tradeStatusModel = $this->modelFactory->create("tradeStatus");
		$tradeStatusModel->createDefaults($company->id);

		/** @var MailingTemplateModel $mailingTemplateModel */
		$mailingTemplateModel = $this->modelFactory->create("mailingTemplate");
		$mailingTemplateModel->createDefaults($company->id);

		/** @var CronModel $cronModel */
		$cronModel = $this->modelFactory->create("cron");
		$cronModel->createDefaults($company->id);

		/** @var OfficeModel $officeModel */
		$officeModel = $this->modelFactory->create("office");
		$officeModel->createDefaults($company->id);



		/** @var lSectionModel $lSectionModel */
		$lSectionModel = $this->modelFactory->create("lSection");
		$lSectionModel->createDefaults($company->id);


		/** @var lSectionTypeModel $lSectionTypeModel */
		$lSectionTypeModel = $this->modelFactory->create("lSectionType");
		$lSectionTypeModel->createDefaults($company->id);

		/** @var UserModel $userModel */
		$userModel = $this->modelFactory->create("user");
		$userModel->createDefaults($company->id);

		/** @var lSectionItemModel $lSectionItemModel */
		$lSectionItemModel = $this->modelFactory->create("lSectionItem");
		$lSectionItemModel->createDefaults($company->id);

		/** @var MeetingMeetingTypeModel $meetingMeetingTypeModel */
		$meetingMeetingTypeModel = $this->modelFactory->create("meetingMeetingType");
		$meetingMeetingTypeModel->createDefaults($company->id);


		//dumpe($company,$careerModel,$this->modelFactory);
		//throw new InvalidArgumentException("nevytvorene");
	}
}