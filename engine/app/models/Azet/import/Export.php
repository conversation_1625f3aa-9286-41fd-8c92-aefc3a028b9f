<?php

namespace azet\import;

/**
 * Import Export
 *
 * <AUTHOR>
 * @copyright  2015 Azet.sk
 */
abstract class Export {

    /**
     *
     * @var Scheme
     */
    protected $scheme;
    protected $type;
    protected $apiKey = '';
    protected $items = [];

    public function __construct(Scheme $scheme, $type) {
        $this->scheme = $scheme;
        $this->type = $type;
    }

    protected function element($name, $start = true) {
        $s = $start ? '' : '/';
        $split = explode("/", $name);
        if (!$start) {
            $split = array_reverse($split);
        }
        return "<$s" . implode("><$s", $split) . '>';
    }

    public function generateXml() {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= $this->element($this->scheme->getXmlRoot($this->type), true) . "\n";
        foreach ($this->items as $row) {
            /* @var $row Item */
            $xml .= $row->generateXml($this->scheme) . "\n";
        }
        return $xml . $this->element($this->scheme->getXmlRoot($this->type), false);
    }

    public function addItem(Item $item) {
        if ($this->type == Scheme::ADVERT && !$item instanceof Advert) {
            throw new Exception("Export is defined for Advert only");
        }
        $this->items[] = $item;
    }

    /**
     * Test generated XML against schema
     * 
     * @return array
     */
    public function validate($schema = '') {
        $errors = [];

        libxml_use_internal_errors(true);
        $xml = new \DOMDocument();
        //$xml->validateOnParse = true;
        $valid = $xml->loadXML($this->generateXml(), LIBXML_COMPACT | LIBXML_NOERROR);
        if ($valid === false) {
            $errors[] = 'XML: Stiahnute data nemaju XML strukturu';
        }

        if (!$schema) {
            $schema = $this->scheme->getXmlSchema($this->type);
        }
        if ($valid && $schema) {
            $valid = $xml->schemaValidate($schema);
            if ($valid === false) {
                $errors[] = 'XMLSchema: Stiahnute data nie su validne';
            }
        }

        if (!$valid) {
            foreach (libxml_get_errors() AS $error) {
                $level = '';
                switch ($error->level) {
                    case LIBXML_ERR_WARNING:
                        $level = '[W]';
                        break;
                    case LIBXML_ERR_ERROR:
                        $level = '[E]';
                        break;
                    case LIBXML_ERR_FATAL:
                        $level = '[F]';
                        break;
                }
                $errors[] = $level . ' ' . $error->message . '(column: ' . $error->column . ', line: ' . $error->line . ')';
            }
        }
        unset($xml);
        return $errors;
    }

}
