<?php

namespace azet\import;

/**
 * Import Item
 *
 * <AUTHOR>
 * @copyright  2015 Azet.sk
 */
abstract class Item {

    protected $content = [];
    private $delete = false;
    private $idOnly = false;
    protected $id;
    protected $idVal = 0;
    protected $type;

    /**
     * Set content value
     * 
     * @param string $title
     * @param mixed $value
     */
    public function set($title, $value) {
        if (is_numeric($value) || is_string($value) || is_array($value)) {
            $this->content[$title] = $value;
            if ($title == $this->id) {
                $this->idVal = $value;
            }
        } else {
            throw new Exception("Wrong datatype for <$title>");
        }
    }

    public function getId() {
        return $this->idVal;
    }

    public function setId($value) {
        $this->set($this->id, $value);
    }

    /**
     * Set content value of list
     * <pre>
     * 'photos','photo',[url1,url2,url3]
     * </pre>
     * 
     * @param string $title
     * @param string $subTitle
     * @param array $value
     */
    public function setList($title, $subTitle, array $value) {
        $this->content[$title] = [];
        foreach ($value as $row) {
            $this->content[$title][] = [$subTitle => $row];
        }
    }

    public function markIdOnly() {
        $this->idOnly = true;
    }

    public function markDelete() {
        $this->delete = true;
        $this->markIdOnly();
    }

    protected function element($name, $start = true) {
        $s = $start ? '' : '/';
        $split = explode("/", $name);
        if (!$start) {
            $split = array_reverse($split);
        }
        return "<$s" . implode("><$s", $split) . '>';
    }

    protected function generateStep(array $content) {
        $xml = '';
        foreach ($content as $key => $value) {
            if (is_numeric($key)) {
                if (!is_array($value)) {
                    throw new Exception("Numeric index must have array value");
                }
                $xml .= $this->generateStep($value);
                continue;
            }
            if (is_array($value)) {
                $xml .= "<$key>" . $this->generateStep($value) . "</$key>";
            } elseif (is_numeric($value)) {
                $xml .= "<$key>$value</$key>";
            } elseif (is_string($value)) {
                $xml .= "<$key><![CDATA[$value]]></$key>";
            } else {
                throw new Exception("Wrong datatype for item: <$key>");
            }
        }
        return $xml;
    }

    protected function generateContentXml(Scheme $scheme, $content) {
        $xml = $this->element($scheme->getXmlItem($this->type), true);
        if ($this->idOnly) {
            $content = [$this->id => $content[$this->id]];
        }
        if ($this->delete) {
            $content['delete'] = 1;
        }
        $xml .= $this->generateStep($content);
        return $xml . $this->element($scheme->getXmlItem($this->type), false);
    }

    /**
     * Generate XML element
     * 
     * @param \azet\import\Scheme $scheme
     * @return string
     */
    public function generateXml(Scheme $scheme) {
        return $this->generateContentXml($scheme, $this->content);
    }

}
