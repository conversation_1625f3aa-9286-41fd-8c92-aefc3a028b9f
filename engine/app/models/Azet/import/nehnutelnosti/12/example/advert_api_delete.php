<?php

$dir = 'C:\workspace\sarmir\www\importlib';
$source = 12;

/* Load library */
foreach (['Exception', 'Scheme', 'Item', 'Advert', 'Export', 'ExportBig', 'ExportApi',
 "nehnutelnosti/Scheme",
 "nehnutelnosti/$source/Advert_$source", "nehnutelnosti/$source/Broker_$source"] as $name) {
    include_once $dir . "/azet/import/$name.php";
}

use azet\import\Scheme as Scheme0;
use azet\import\nehnutelnosti\Scheme;
use azet\import\nehnutelnosti\Advert_12;
use azet\import\nehnutelnosti\Broker_12;

/* Create global instances */
$apiKey = '***';
$scheme = new Scheme($source);
$export = new \azet\import\ExportApi($scheme, Scheme0::ADVERT, $apiKey, '12345677', 'Dalten-TEST');

/* Prepare data */
foreach ([117403,204052,208816] as $id) {
    $adv = new Advert_12();
    $adv->setId($id);
    $adv->markDelete();
    $export->addItem($adv);  //add to export
}

/* Export data */
try {
    $result = $export->sendExport();
    foreach ($result as $id => $row) {
        if ($row['status'] != 200) {
            //TODO: Problem, need to resolve
            echo '!!! Error !!!' . $row['msg'];
        }
    }
    print_r($result);
} catch (Exception $ex) {
    //TODO: Problem, need to resolve
    echo '!!! Error !!!' . $ex;
}