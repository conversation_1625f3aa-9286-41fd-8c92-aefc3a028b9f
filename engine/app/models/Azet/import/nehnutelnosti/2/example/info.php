<?php

$dir = 'C:\workspace\sarmir\www\importlib';
$source = 2;

/* Load library */
foreach (['Exception', 'Scheme', 'Item', 'Advert', 'Export', 'ExportBig', 'ExportApi',
 "nehnutelnosti/Scheme",
 "nehnutelnosti/$source/Advert_$source", "nehnutelnosti/$source/Broker_$source", "nehnutelnosti/$source/Helper_$source"] as $name) {
    include_once $dir . "/azet/import/$name.php";
}

use azet\import\Scheme as Scheme0;
use azet\import\nehnutelnosti\Scheme;
use azet\import\nehnutelnosti\Advert_2;
use azet\import\nehnutelnosti\Broker_2;
use azet\import\nehnutelnosti\Helper_2;

/* Create global instances */
$apiKey = '***';
$scheme = new Scheme($source);

echo 'Import report: ' . $scheme->getImportLogsUrl($apiKey);
