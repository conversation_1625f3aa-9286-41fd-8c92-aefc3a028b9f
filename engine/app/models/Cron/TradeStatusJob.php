<?php

namespace <PERSON><PERSON>mi<PERSON>ta\Cron;

use JetBrains\PhpStorm\Deprecated;
use Webtec\Models\TradeModel;

#[Deprecated]
class TradeStatusJob extends BaseJob implements Job {
	public function run( array $args = [] ) {
		$this->log("=============================================================");
		/** @var TradeModel $tradeModel */
		$tradeModel = $this->modelFactory->create("trade");

		//SELECT t.id FROM trade as t WHERE t.trade_status != (SELECT trade_status FROM trade_statuses WHERE trade = t.id ORDER BY id DESC LIMIT 1);
		//SELECT t.id FROM trade as t WHERE t.trade_status > 1 AND (SELECT COUNT(id) FROM trade_statuses WHERE trade = t.id) = 0;

		foreach($tradeModel->findBy(["trade_status <> (SELECT trade_status FROM trade_statuses WHERE trade = trade.id ORDER BY id DESC LIMIT 1)"]) as $trade) {
			$this->log($trade->id);
			//$this->log($tradeModel->getLastStatus($trade)->trade_status);
			$trade->update(["trade_status" => $tradeModel->getLastStatus($trade)->trade_status]);
		}

		foreach($tradeModel->findBy(["trade_status >" => 1,"(SELECT COUNT(id) FROM trade_statuses WHERE trade = trade.id)"=>0]) as $trade) {
			$this->log($trade->id);
			$trade->update(["trade_status" => 1]);
		}


		$this->log("============================ end ============================");
	}
}