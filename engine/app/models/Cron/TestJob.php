<?php

namespace <PERSON><PERSON>mi<PERSON><PERSON>\Cron;

use JetBrains\PhpStorm\Deprecated;
use Kubomikita\Services\CompanyProvider;

#[Deprecated]
class TestJob extends BaseJob implements Job {
	public function run( array $args = [] ) {

		//dump($this->context->getByType(CompanyProvider::class)->getCompanyId());

		$mail = $this->context->getByType(\Contributte\Mailing\IMailBuilderFactory::class)->create();
		$mail->addTo("<EMAIL>");
		$mail->setSubject("test");
		$mail->setTemplateFile(MAIL_DIR . "/message.latte");
		$mail->setParameters(["message" => "test", "signature" => "podpis"]);
		$mail->send();

		/*$this->log("=============================================================");
		$this->log("hello cli world");
		foreach($args as $k => $v){
			$this->log("argument[$k]: ".$v);
		}

		foreach ($this->modelFactory->create("apiToken")->findAll() as $token){
			$this->log("token: ".$token->token." - company: ". $token->company_id);
		}

		$this->log($this->context->getByType(CompanyProvider::class)->getCompany()->id);


		$this->log("============================ end ============================");*/
	}
}