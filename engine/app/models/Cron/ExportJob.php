<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Cron;



use BackendModule\TradePresenter;
use Kubomikita\Attributes\Cron;
use Kubomikita\Services\CompanyProvider;
use Kubomikita\Utils\Strings;
use Nette\Application\PresenterFactory;
use Nette\Utils\FileSystem;
use Ublaboo\DataGrid\CsvDataModel;

#[Cron("Export do CSV", "* * * * *", 1, FALSE, TRUE, false)]
class ExportJob extends BaseJob implements Job {

	protected string $exportDir = TEMP_DIR."/export";


	public function run( array $args = [] ) {
		if(!empty($args)) {


			$this->log( "============= EXPORT =============" );

			//dump($args);
			/*$args = [
				"user" => 1,
				"superadmin" => 1,
				"email" => "<EMAIL>",
				"subject" => "Export obchodov do CSV",
				"presenterName" => TradePresenter::class,
				"selection" => "getTradesSelection",
				"propertyDataSource" => "tradesSource",
				"gridName" => "tradeGrid",
				"fileName" => "obchody",
				"order" => ["id" => "DESC"]
			];*/
			//dump($args);
			//echo Json::encode($args);


			//echo "<hr>";

			if(!Strings::contains( $args["presenterName"], ":")) {
				$this->log("Pri vytvárani exportu nastala chyba.");
			} else {

				/** @var PresenterFactory $presenterFactory */
				$presenterFactory = $this->context->getService( "application.presenterFactory" );

				/** @var TradePresenter $presenter */
				$presenter = $presenterFactory->createPresenter(  $args["presenterName"] );
				$presenter->companyService = $this->context->getByType(CompanyProvider::class);
				$presenter->company = $presenter->companyService->getCompany();
				$presenter->cache = $presenter->cacheFactory->create(get_called_class());

				try {
					$dataSource = $presenter->{$args["selection"]}( $args["superadmin"], $args["user"] );
					if ( isset( $args["propertyDataSource"] ) && $args["propertyDataSource"] !== null ) {
						$presenter->{$args["propertyDataSource"]} = $dataSource;
					}

					if(isset($args["where"]) && !empty($args["where"])){
						$dataSource->where($args["where"]);
					}

					if($args["gridName"] == "tradeGrid"){
						$presenter->tradesDatagridFactory->setUserId($args["user"]);
					}
					//dump($dataSource->fetchAll());
					//exit;

					$filtered = isset($args["filtered"]) ? $args["filtered"] : false;
					$grid = $presenter[ $args["gridName"] ];

					if($args["gridName"] === "tradeGrid"){
						$grid->removeColumn("id1");
					}

					//$grid->setUserId($args["user"]);
					$grid->setDataSource( $dataSource );
					$grid->filter = !empty($args["filter"]) ? $args["filter"] : [];
					$grid->onFiltersAssembled = [];
					$data         = $grid->prepareDataForExport( $filtered, isset( $args["order"] ) ? $args["order"] : [] );



					$columns      =  $grid->getColumns( false, isset($args["columns"]) && !empty($args["columns"]) ? $args["columns"] : null );

					$columnsTitle = (isset($args["columnsTitle"]) and !empty($args["columnsTitle"])) ? $args["columnsTitle"] : [];

					//dump($columnsTitle);

					if(isset($args["columnsRenderer"]) and !empty($args["columnsRenderer"])){
						foreach($args["columnsRenderer"] as $key => $renderer){
							if(is_callable($renderer)) {
								if (isset($columns[$key])) {
									$columns[$key]->setRenderer($renderer);
								} else {
									$columns[$key] = $grid->addColumnText($key, $columnsTitle[$key] ?? $key)->setRenderer($renderer);
								}
							}
						}
					}

					if(isset($args["columnsOrder"]) && !empty($args["columnsOrder"])) {
						$order = $args["columnsOrder"];
						//$sorted = array_intersect($args["columnsOrder"], $columns);
						uksort($columns, function ($key1, $key2) use ($order) {
							return ((array_search($key1, $order) > array_search($key2, $order)) ? 1 : -1);
						});
					}

					$csvDataModel = new CsvDataModel( $data, $columns, $grid->getTranslator() );
					//dump($columns, $csvDataModel,  $args);
					$filename = date( "Ymd-His" ) . "_" . $args["user"] . "-" . $args["fileName"] . ".csv";
					$this->log( "Export prebehol úspešne." );
					$this->log( ".csv súbor si stiahnete na tejto adrese:" );

					$encoding = isset($args["encoding"]) ? $args["encoding"] : "windows-1250";
					$delimiter = isset($args["delimiter"]) ? $args["delimiter"] : ";";

					$this->createCsvFile( $csvDataModel->getSimpleData(), $filename, $encoding, $delimiter, true );
					$url = rtrim($this->httpRequest->getUrl(),"/") . $this->presenter->link( ":Backend:Download:default",
							[ "type" => "export", "file" => $filename ] );
					$this->log( $url );
					$this->log("-----------------------------------");
					$this->log("alebo si možte pozrieť <a href='".rtrim($this->httpRequest->getUrl(),"/") . $this->presenter->link( ":Backend:User:export")."'>zoznam všetkých exportov tu.</a>");
					if(isset($args["exportId"])){
						$this->modelFactory->create("export")->find($args["exportId"])->update(["link" => $url]);
					}

				} catch ( \Throwable $e ) {
					$this->log( "Export sa nepodaril." );
					$this->log( "Chyba: " . $e->getMessage() );
					//dump($e);
				}
			}

			$this->log( "==================================" );
			//$this->cron->update(["started" => null]);

		}
		//dumpe($this->context,$presenterFactory,$presenterFactory->createPresenter("Backend:Trade"));
	}

	protected function createCsvFile(array $data, string $name = 'export.csv', 	string $outputEncoding = 'utf-8', string $delimiter = ';', bool $includeBom = false): string
	{
		ob_start();
		// Output data
		if ($includeBom && strtolower($outputEncoding) === 'utf-8') {
			echo b"\xEF\xBB\xBF";
		}
		$delimiter = '"' . $delimiter . '"';
		foreach ($data as $row) {
			if (strtolower($outputEncoding) === 'utf-8') {
				echo '"' . implode($delimiter, (array) $row) . '"';
			} else {
				echo iconv('UTF-8', $outputEncoding, '"' . implode($delimiter, (array) $row) . '"');
			}
			echo "\r\n";
		}

		if(!file_exists($this->exportDir)){
			FileSystem::createDir($this->exportDir);
		}

		$filename = $name;
		FileSystem::write($this->exportDir."/".$filename, ob_get_clean());
		return rtrim($this->httpRequest->getUrl(),"/")."/engine/temp/export/".$filename;
	}

}