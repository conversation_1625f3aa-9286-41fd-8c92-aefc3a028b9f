parameters:
    purifier:
        encoding: 'UTF-8'
        finalize: true
        ignoreNonStrings: false
                #'cachePath'          => storage_path('app/purifier'),
                #'cacheFileMode'      => 0755,
        settings:
            default:
                HTML.Doctype: 'HTML 4.01 Transitional'
                HTML.Allowed: 'div[style],b,strong,i,em,u,a[href|title|class],ul,ol,li,p[style|align],br,span[style|class],img[width|height|alt|src|style],iframe[style|src|width|height|frameborder|title|allowfullscreen],table[border|cellspacing|cellpadding|style],tr,td[width|valign|style],th,tbody,thead,font[color|style],h1,h2,h3,figure[class]'
                CSS.AllowedProperties: 'font,font-size,font-weight,font-style,font-family,text-decoration,padding-left,color,background-color,text-align,width,width,border-top,border-left,border-right,border-bottom,padding,margin,background,margin-bottom'
                Cache.DefinitionImpl: null
                AutoFormat.AutoParagraph: true
                AutoFormat.RemoveEmpty: true
                CSS.MaxImgLength: null
                HTML.SafeIframe: true
                URI.SafeIframeRegexp: '%^(https?:)?//(www\.youtube(?:-nocookie)?\.com/embed/|player\.vimeo\.com/video/)%' #'%(.*)%'
                HTML.SafeEmbed: true
                Filter.YouTube: true
            test:
                Attr.EnableID: true
            youtube:
                HTML.SafeIframe: true
                URI.SafeIframeRegexp: "%^(http://|https://|//)(www.youtube.com/embed/|player.vimeo.com/video/)%"
        custom_definition:
            id: 'html5-definitions'
            rev: 1
            debug: false
            elements:
                # http://developers.whatwg.org/sections.html
                - ['section', 'Block', 'Flow', 'Common']
                - ['nav',     'Block', 'Flow', 'Common']
                - ['article', 'Block', 'Flow', 'Common']
                - ['aside',   'Block', 'Flow', 'Common']
                - ['header',  'Block', 'Flow', 'Common']
                - ['footer',  'Block', 'Flow', 'Common']
                # Content model actually excludes several tags, not modelled here
                - ['address', 'Block', 'Flow', 'Common']
                - ['hgroup', 'Block', 'Required: h1 | h2 | h3 | h4 | h5 | h6', 'Common']
                # http://developers.whatwg.org/grouping-content.html
                - ['figure', 'Block', 'Optional: (figcaption, Flow) | (Flow, figcaption) | Flow', 'Common']
                - ['figcaption', 'Inline', 'Flow', 'Common']
                # http://developers.whatwg.org/the-video-element.html#the-video-element
                - [ source, Block, Flow, Common, [ src: URI, type: Text ] ]
                - ['video', 'Block', 'Optional: (source, Flow) | (Flow, source) | Flow', 'Common', [ src: 'URI', type: 'Text',  width: 'Length', height: 'Length', poster: 'URI', preload: 'Enum#auto,metadata,none', controls: 'Bool']]
                # http://developers.whatwg.org/text-level-semantics.html
                - ['s',    'Inline', 'Inline', 'Common']
                - ['var',  'Inline', 'Inline', 'Common']
                - ['sub',  'Inline', 'Inline', 'Common']
                - ['sup',  'Inline', 'Inline', 'Common']
                - ['mark', 'Inline', 'Inline', 'Common']
                - ['wbr',  'Inline', 'Empty', 'Core']
                # http://developers.whatwg.org/edits.html
                - ['ins', 'Block', 'Flow', 'Common', [cite: 'URI', datetime: 'CDATA']]
                - ['del', 'Block', 'Flow', 'Common', [cite: 'URI', datetime: 'CDATA']]
            attributes:
                - ['iframe', 'allowfullscreen', 'Bool']
                - ['table', 'height', 'Text']
                - ['td', 'border', 'Text']
                - ['th', 'border', 'Text']
                - ['tr', 'width', 'Text']
                - ['tr', 'height', 'Text']
                - ['tr', 'border', 'Text']
                - ['a', 'class','Text']
                - ['div', "data-oembeded-url", "Text"]
                #- ['a','href','Text']
        custom_attributes:
            - ['a', 'target', 'Enum#_blank,_self,_target,_top']
        custom_elements:
            - ['u', 'Inline', 'Inline', 'Common']
            #- ['font', 'color', 'Inline', 'Common']