{var $title = "Obchodné výsledky spolupracovníci"}

{block content}

<div class="card widget-2 mb-3">

	<div class="card-body  bd-color-gray-lighter">
		{form reportForm, class=>"ajax"} {*, class=>"ajax"*}
            <div class="d-none">{input humanDate}</div>
			<div class="row align-items-end no-gutters">
				<div class="col-12">
					{label user /}
					{input user}
					<div id="{$form["user"]->getErrorId()}" n:class="$form['user']->hasErrors() ? invalid-feedback" >{inputError user}</div>
				</div>
                <div class="col-12 my-3 p-2 bg-light">
                    <div class="row">
                        <div class="col-md-2">
                            <label>Týždeň</label>
                            <select class="form-control reportDate select2">
                                <option value="" selected></option>
                                <option n:foreach="$weeks as $week" value="{$week["value"]}">{$week["name"]}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label>Mesiac</label>
                            <select class="form-control reportDate select2">
                                <option value="" selected></option>
                                <option n:foreach="$months as $month" value="{$month["value"]}">{$month["name"]}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label>Kvartál</label>
                            <select class="form-control reportDate select2">
                                <option value="" selected></option>
                                <option n:foreach="$quarters as $quarter" value="{$quarter["value"]}">{$quarter["name"]}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label>Rok</label>
                            <select class="form-control reportDate select2">
                                <option value="" selected></option>
                                <option n:foreach="$years as $year" value="{$year["value"]}">{$year["name"]}</option>
                            </select>
                        </div>
                    </div>
                </div>
				<div class="col-md pr-2">
					{label start /}
					{input start}
					<div id="{$form["start"]->getErrorId()}" n:class="$form['start']->hasErrors() ? invalid-feedback" >{inputError start}</div>
				</div>
				<div class="col-md pr-2">
					{label end /}
					{input end}
					<div id="{$form["end"]->getErrorId()}" n:class="$form['end']->hasErrors() ? invalid-feedback" >{inputError end}</div>
				</div>
				<div class="col-md">
					{input save, class=>"btn btn-success btn-p"}
				</div>
			</div>
			{*<div class="row">
				<div class="col-md">
					{input infomeeting_on}
				</div>
			</div>
			<div class="row no-gutters align-items-center" id="meeting-container">
				<div class="col-md-3 pr-2" >
					{input $form["infomeeting"]["type"], class => "form-control select2 select2-tag"}
				</div>
				<div class="col-md-auto pr-2">
					{input $form["infomeeting"]["points"]}
				</div>
			</div>*}
		{/form}
	</div>
</div>

{snippet report}
    {var $ses = $presenter->ses->values}
    {if (isset($ses["humanDate"]) && strlen(trim($ses["humanDate"])) > 0)}
        <h2 class="tx-dark tx-bold mt-5 mb-2"><span class="tx-light">Sledované obdobie:</span> {$ses["humanDate"]}</h2>
    {else}
        <h2 class="tx-dark tx-bold mt-5 mb-2"><span class="tx-light">Sledované obdobie:</span> {$ses["start"]|date:"d.m.Y"} - {$ses["end"]|date:"d.m.Y"}</h2>
    {/if}

<div class="row">
    <div class="col-lg-12">

        <div class="card mb-3">
{*            <div class="card-header bg-gray-400">

                <h6 class="card-title mb-0"></h6>
            </div>*}
            <div class="card-body  bd-color-gray-lighter">

            {*var $report = $presenter->getReport()}
                <div class="alert alert-danger tx-bold" n:if="$report === null || empty($report['data'])">
                    <i class="fas fa-exclamation-triangle"></i>
                    Vyberte dátum od - do a vyberte aky typ reportu chcete zobraziť kliknite na filtrovať!
                </div>
            {*php dump($report)*}




                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Spolupracovník</th>
                            <th class="text-center">Infomeeting</th>
                            <th class="text-center">Infomeeting body (rozpracované)</th>
                            <th class="text-center">Rozpracované body</th>
                            <th class="text-center">Zrealizované body</th>
                            <th class="text-center">Sprovizované body</th>

                        </tr>
                    </thead>
                    <tbody>
                        <tr n:foreach="$ses['user'] as $user_id">
                            {var $userRow = $presenter->userModel->find($user_id)->fetch()}
                            <td>{Webtec\Models\UserModel::formatName($userRow)}</td>
                            <td class="text-center">{$presenter->getInfomeetingCount($userRow, $ses["start"], $ses["end"])}</td>
                            <td class="text-center">{$presenter->userModel->getPointsInfomeeting($user_id, $ses["start"], $ses["end"])|points}</td>
                            <td class="text-center">{$presenter->userModel->getPointsInProgress($user_id, $ses["start"], $ses["end"])|points}</td>
                            <td class="text-center">{$presenter->userModel->getPointsWaiting($user_id, $ses["start"], $ses["end"])|points}</td>
                            <td class="text-center">{$presenter->userModel->getPointsToPaid($user_id, $ses["start"], $ses["end"])|points}</td>
                        </tr>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
</div>
{/snippet}

{block scripts}

    <script>
        var startSelect = $("#" + {$presenter["reportForm"]["start"]->htmlId});
        var endSelect = $("#" + {$presenter["reportForm"]["end"]->htmlId});
        var humanDate = $("#" + {$presenter["reportForm"]["humanDate"]->htmlId});
        $(document).on("change",".reportDate",function () {
        	if($(this).val() != "") {
		        var val = JSON.parse($(this).val());
		        console.log(val);
		        console.log($(this).find("option:selected").text());
		        if (val !== null) {
			        startSelect.val(val.start);
			        endSelect.val(val.end);
			        humanDate.val($(this).find("option:selected").text());
		        }
		        $(this).val("").change();
		        spinnerOn();
		        $("#" + {$presenter["reportForm"]->getElementPrototype()->getAttribute("id")}).trigger("submit");
	        }
        	//$(this).find("option[value='']").prop("selected",true);

            //

        })

    </script>

{/block}