{var $title = "Osobné nastavenia"}
{block content}
	<div class="row">
		<div class="col-lg-6">
			<div class="card widget-2 mb-3">
				<div class="card-header">
					<h6 class="card-title"><PERSON><PERSON> so mnou</h6>
				</div>
				<div class="card-body">
					{control sharerGrid}
				</div>
			</div>
		</div>
		<div class="col-lg-6">
			<div class="card widget-2 mb-3">
				<div class="card-header">
					<h6 class="card-title"><PERSON><PERSON><PERSON>am s inými</h6>
				</div>
				<div class="card-body">
					{control sharedGrid}
				</div>
			</div>
		</div>
		<div class="col-lg-6">
			{form marketingForm, class => "ajax"}
				<div class="card widget-2 mb-3">
					<div class="card-header">
						<h6 class="card-title">Marketingové nastavenia</h6>
					</div>
					<div class="card-body">
						<div class="form-group">
							{label marketing /}
							{foreach $form["marketing"]->items as $key => $label}
								<div>
									<label class="ckbox ckbox-success" n:name="marketing:$key">
										<input n:name="marketing:$key">
										<span>{$label}</span>
									</label>
								</div>
							{/foreach}
							{*input marketing*}
						</div>
						<div class="d-none">
							{input save}
						</div>
					</div>
				</div>

			{/form }
		</div>
		{*<div class="col-lg-6">
			<div class="card widget-2 mb-3">
				<div class="card-header">
					<h6 class="card-title">Asistenti</h6>
				</div>
				<div class="card-body">
					{control assistantGrid}
				</div>
			</div>
		</div>*}
	</div>

	{*<div id="modalAddAssistant" class="modal fade" aria-hidden="true" style="display: none;">
		{form modalAddAssistantForm, class=>"ajax"}
			<div class="modal-dialog modal-dialog-centered" role="document">
				<div class="modal-content bd-0 tx-14">
					<div class="modal-header pd-y-20 pd-x-25">
						<h6 class="tx-14 mg-b-0 tx-uppercase tx-inverse tx-bold">Pridať asistenta</h6>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">×</span>
						</button>
					</div>
					<div class="modal-body pd-25">
						<div class="form-group">
							{label assistant /}
							{input assistant}
						</div>
						<div class="form-group">
							<label class="ckbox ckbox-primary">
								{$form["coworker"]->getControlPart()}
								<span>{$form["coworker"]->getLabelPart()}</span>
							</label>
						</div>
						<div class="form-group">
							<label class="ckbox ckbox-primary">
								{$form["specialist"]->getControlPart()}
								<span>{$form["specialist"]->getLabelPart()}</span>
							</label>
						</div>
					</div>
					<div class="modal-footer">
						{input save, class=>"btn btn-success btn-p"}
						<button type="button" class="btn btn-danger" data-dismiss="modal">{_"Zavrieť"}</button>
					</div>
				</div>
			</div><!-- modal-dialog -->
		{/form}
	</div>*}


{/block}
{block scripts}

<script>
	$(document).ready(function () {
		$("#frm-marketingForm input[type='checkbox']").on("change",function () {
			//console.log("teraz");
			$("#frm-marketingForm input[type='submit']").click();
		})
	})
</script>