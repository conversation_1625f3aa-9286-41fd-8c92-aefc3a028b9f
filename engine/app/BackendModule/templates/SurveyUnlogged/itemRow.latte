{varType Nette\Database\Table\ActiveRow $surveyItem}
{varType Nette\Forms\Container $container}
{varType string $namePrepend}
{varType int $multiplier}

{default $colClass = "col-lg pb-lg-0 pb-2"}
{var $defaultColClass = $colClass}
{default $multiplier = 0}
{*php dump($container)*}
<div class="row no-gutters align-items-center border-bottom">
    <div class="col-lg-4 text-center text-lg-left py-2">{$surveyItem->name}</div>
    <div class="col-lg-8">
        <div class="row  no-gutters align-items-center ">
            {foreach $surveyItem->related('survey_item_input')->order("ordr") as $surveyInput}
                {var $colClass = $defaultColClass}
                {var $inputName = $namePrepend.$surveyInput->name}
                {var $inputDomId = 'input-'.$surveyInput->id.'-'.$multiplier}
                {if $surveyInput->type == "addTextArea"}
                    {if strlen(trim($container[$inputName]->getValue())) > 0}
                        {var $colClass = "col-lg-12 pb-2 order-12"}
                    {else}
                        {var $colClass = "col-lg-12 pb-2 order-12 d-none"}
                    {/if}

                    <div class="col-lg-auto px-2 order-11">
                        <a href="javascript:;" onclick="javascript:showInfoInput({$inputDomId});"><i class="fas fa-info-circle" title="Pridať doplňujúce informácie" data-toggle="tooltip"></i></a>
                    </div>
                {/if}
                <div id="{$inputDomId}" class="{$colClass} px-1 py-lg-1">

                    {if $surveyInput->type == "addCheckbox"}
                        {var \Nette\Forms\Controls\Checkbox $checkbox = $container[$inputName]}
                        <label class="ckbox ckbox-primary mb-0">
                            {$checkbox->getControlPart()}
                            <span>{$checkbox->getCaption()}</span>
                        </label>
                        {*php dump($container[$inputName]->getLabelPart(),$checkbox->getControl(), $checkbox->getCaption())*}
                    {else}
                        {*php dump($inputName,$container[$inputName])*}
                        {*label $container[$inputName]*}
                        <div class="input-group input-group-sm" n:tag-if="strlen($surveyInput->desc) > 0">
                            {input $container[$inputName]}
                            <div class="input-group-prepend" n:tag-if="strlen($surveyInput->desc) > 0">
                                <span class="input-group-text" n:tag-if="strlen($surveyInput->desc) > 0">{$surveyInput->desc}</span>
                            </div>
                        </div>
                    {/if}
                </div>
            {/foreach}
        </div>
    </div>
</div>