{extends "../@layout.survey.latte"}
{var $title = "Dotazník"}
{block content}
    <style>
        iframe {
            max-width: 100%;
            /*height: auto;*/
        }
    </style>
    <div class="container">
    <div class="px-3 my-5 py-5 bg-white rounded">

        <h1 class="text-center tx-28 mb-3 d-none">Dotazník ({$survey->survey_type->name}) pre <span class="tx-black">{$survey->email}</span></h1>

        {$survey->survey_type->text|noescape}
    {snippet surveySnippet}
        {form surveyForm, class => "ajax"}
            <div class="row">
                <div class="col-lg-3">
                    <div class="form-group">
                        {label name /}
                        {input name}
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group">
                        {label surname /}
                        {input surname}
                    </div>
                </div>


                <div class="col-lg-3">
                    <div class="form-group">
                        {label phone /}
                        {input phone}
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group">
                        {label email /}
                        {input email}
                    </div>
                </div>
            </div>

            {foreach $surveySection as $section}
                {if $section->related("survey_item")->where(["deleted" => 0])->count("id") > 0}
                    <h2 style="font-size: 21px;" class="mt-4">{$section->name}</h2>
                    {foreach $section->related("survey_item")->order("ordr ASC")->where(["survey_item IS NULL", "deleted"=>0]) as $surveyItem}
                        {var $inputCount = $surveyItem->related('survey_item_input')->count('id')}
                        {if $inputCount === 1}
                            {var $colClass = "col-lg pb-2"}
                        {elseif $inputCount > 4}
                            {var $colClass = "col-lg-6 pb-2"}
                        {else}
                            {var $colClass = "col-lg-3  pb-2"}
                        {/if}

                        {if $surveyItem->multiplier}
                            {var $multiplierName = "survey_multiplier_".$surveyItem->id}
                            <div n:multiplier="$form['questions'][$multiplierName]" class="border p-3 mb-3">
                                <div class="d-flex justify-content-between">
                                    <h3>{(int)$_multiplier->getName() +1}. {$surveyItem->name}</h3>
                                    <div n:if="!$survey->sended">
                                        {multiplier:remove}
                                    </div>
                                </div>
                                {foreach $presenter->surveyItemModel->findBy(["survey_item" => $surveyItem->id, "deleted"=>0])->order("ordr ASC") as $multiplierItem}
                                    {var $multiplierPrepend = Webtec\Models\SurveyItemModel::formatPrepend($multiplierItem->name)."_"}
                                    {include "itemRow.latte", container => $_multiplier, namePrepend => $multiplierPrepend, surveyItem => $multiplierItem, multiplier => (int)$_multiplier->getName()}
                                {/foreach}
                            </div>
                            <div class="text-center mb-5" n:if="!$survey->sended">{multiplier:add $form['questions'][$multiplierName]}</div>
                        {/if}


                        {if $inputCount > 0 && $surveyItem->multiplier == 0}
                            {*php dump($form["questions"])*}
                            {include "itemRow.latte", container => $form["questions"]["item_".$surveyItem->id], namePrepend => '', surveyItem => $surveyItem, colClass => $colClass}
                        {/if}
                    {/foreach}
                {/if}

            {/foreach}


            <div class="my-3 justify-content-center {if $survey->client !== null || !$survey->survey_type->registration}d-none{else}d-flex{/if}">

                <label class="ckbox ckbox-danger mb-0">
                    {$form["create"]->getControlPart()}
                    <span>
                        {strip_tags($form["create"]->getLabelPart())} (registrácia do systému)
                    </span>
                </label>

            </div>
            <div class="my-3 text-center"  n:if="!$survey->sended">
                {input save, class=>"btn btn-success btn-p"}
                <a href="javascript:;" data-toggle="modal" data-form="surveyForm" data-target="#modalConfirm" class="btn btn-primary modal-open btn-p" >Odoslať</a>
            </div>
            <div class="my-3 text-center" n:if="$survey->sended && $survey->survey_type->mailing_template_id !== null">
                <a href="javascript:;" data-toggle="modal" data-target="#modalSuccess" class="btn btn-dark modal-open" >Viac informácii</a>
            </div>
            <div id="modalConfirm" class="modal fade" aria-hidden="true" style="display: none;">

                    <div class="modal-dialog modal-dialog-centered modal-dialog-wide " {*style="min-width: 600px;"*} role="document">
                    <div class="modal-content bd-0 tx-14">
                    <div class="modal-header pd-y-10 pd-x-10 align-items-center">
                        <h6 class=" mg-b-0 tx-inverse tx-bold">Naozaj si prajete odoslať online dotazník?</h6>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body pd-10">
                        <strong>Upozornenie:</strong> po odoslaní dotazníka sa už nebude dať meniť.
                    </div>
                    <div class="modal-footer pd-10 d-block">
                        <div class="row no-gutters">
                            <div class="col-lg-6  text-lg-left">
                                <div class="my-3">
                                    <div>
                                        <label class="ckbox ckbox-danger mb-0">
                                            {$form["gdpr"]->getControlPart()}
                                            <span>
                                                Súhlasím s <a href="javascript:;">poskytnutím osobných údajov</a>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 text-center text-lg-right ">
                                {input send, class=>"btn btn-primary btn-p"}
                                <button type="button" class="btn btn-danger" data-dismiss="modal">{_"Zavrieť"}</button>
                            </div>
                        </div>
                    </div>
                </div>
                </div><!-- modal-dialog -->

            </div>
        {/form}
        {/snippet}

        <div id="modalSuccess" class="modal fade" aria-hidden="true" style="display: none;">

            <div class="modal-dialog modal-dialog-centered modal-dialog-wide" role="document">
                <div class="modal-content bd-0 tx-14">
                    <div class="modal-header pd-y-20 pd-x-25 bg-gray-400">
                        <h6 class="tx-14 mg-b-0 tx-inverse tx-bold">Ďakujeme, dotazník bol úspešne odoslaný...</h6>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body pd-25" n:snippet="modalSuccess">
                        {ifset $modalSuccess}
                        {$modalSuccess|noescape}
                            {ifset $modalSuccessFiles}
                                <h3>Prílohy:</h3>
                                {control documentViewer}
                            {/ifset}
                        {/ifset}
                    </div>
                    {*<div class="modal-footer">

                        <button type="button" class="btn btn-danger" data-dismiss="modal">{_"Zavrieť"}</button>
                    </div>*}
                </div>
            </div><!-- modal-dialog -->

        </div>

        {*php dump($survey)*}
    </div>
    </div>


{/block}

{block scripts}
<script src="{$basePath}/assets/app/jquery.autoSaveForm.js?v=1.0002"></script>
<script>

	$(document).ready(function () {
		$("#" + {$presenter["surveyForm"]->getElementPrototype()->id}).autoSaveForm();
	});
	$.nette.ext({
		success: function (payload) {
			//console.log(payload);
			$("#" + {$presenter["surveyForm"]->getElementPrototype()->id}).autoSaveForm();
		}
    });
	</script>
<script>


//})
function showInfoInput(id){
	if($("#" + id).is(":visible")){
		$("#" + id).addClass("d-none");
	} else {
	    $("#" + id).removeClass("d-none");
	}
}
</script>