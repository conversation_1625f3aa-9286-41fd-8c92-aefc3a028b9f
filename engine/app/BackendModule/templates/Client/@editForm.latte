{form clientForm, class=>"ajax"}
<div class="row justify-content-center">
	<div class="col-md-12">
		<div class="card">
			<div class="card-header bg-gray-400">
				<h6 class="card-title mb-0">Ú<PERSON><PERSON> o klientovi</h6>
			</div>
			<div class="card-body  bd-color-gray-lighter">
				<div class="row">
					<div class="col-md-6">
						<div class="form-label-group">
							{var $form = $control["clientForm"]}
                            {input users_id}
                            {label users_id /}
						</div>
						<div class="form-label-group">

                            {input username}
                            {label username /}

						</div>
						<div class="form-label-group">

							{input $form["nameList"]["phone"], class => "form-control phone"}
                            {label $form["nameList"]["phone"] /}
						</div>
					</div>
					<div class="col-md-6">
                        <div class="row no-gutters">
                            <div class="col pr-2">
                                <div class="form-label-group">
                                    {input $form["nameList"]["degree_before"]}
                                    {label $form["nameList"]["degree_before"] /}
                                </div>
                            </div>
                            <div class="col-4 pr-2">
                                <div class="form-label-group">
                                    {input $form["nameList"]["name"]}
                                    {label $form["nameList"]["name"] /}
                                </div>
                            </div>
                            <div class="col-4 pr-2">
                                <div class="form-label-group">
                                    {input $form["nameList"]["surname"]}
                            {label $form["nameList"]["surname"] /}
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-label-group">
                                    {input $form["nameList"]["degree_after"]}
                                    {label $form["nameList"]["degree_after"] /}
                                </div>
                            </div>
                        </div>

						<div class="form-label-group">
                            {input $form["nameList"]["company_name"]}
                            {label $form["nameList"]["company_name"] /}

						</div>
						<div class="form-label-group">
                            {input $form["nameList"]["company_ico"]}
                            {label $form["nameList"]["company_ico"] /}

						</div>
                        <div class="form-label-group">
                            {input $form["nameList"]["company_icdph"]}
                            {label $form["nameList"]["company_icdph"] /}

                        </div>
					</div>
				</div>
			</div>
		</div>
	</div>
    <div class="col-md-12 mt-3" >
        <div class="card">
            <div class="card-header bg-gray-400">
                <h6 class="card-title mb-0">Doplňujúce informácie</h6>
            </div>
            <div class="card-body  bd-color-gray-lighter">
                <div class="row">
                    <div class="col-md-2_5">
                        <div class="form-label-group">
                            {input $form["nameList"]["birthdate"]}
                            {label $form["nameList"]["birthdate"] /}

                        </div>
                        <div class="form-label-group">
                            {input $form["nameList"]["identification_number"]}
                            {label $form["nameList"]["identification_number"] /}

                        </div>
                    </div>

                    <div class="col-md-2_5">
                        <div class="form-label-group">
                            {input $form["nameList"]["identity_number"]}
                            {label $form["nameList"]["identity_number"] /}

                        </div>
                        <div class="form-label-group">
                            {input $form["nameList"]["identity_number_valid"]}
                            {label $form["nameList"]["identity_number_valid"] /}

                        </div>
                    </div>
                    <div class="col-md-2_5">
                        <div class="form-label-group">
                            {input $form["nameList"]["iban"]}
                            {label $form["nameList"]["iban"] /}


                        </div>
                    </div>
                    <div class="col-md-2_5">
                        <div class="form-label-group">
                            {input $form["nameList"]["health_insurance"]}
                            {label $form["nameList"]["health_insurance"] /}


                        </div>
                    </div>
                    <div class="col-md-2_5">
                        <div class="form-label-group">
                            {input $form["nameList"]["employer"]}
                            {label $form["nameList"]["employer"] /}

                        </div>
                        <div class="form-label-group">
                            {input $form["nameList"]["employer_info"]}
                            {label $form["nameList"]["employer_info"] /}

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mt-3" >
        <div class="card">
            <div class="card-header bg-gray-400">
                <h6 class="card-title mb-0">Trvalý pobyt</h6>
            </div>
            <div class="card-body  bd-color-gray-lighter">
                <div class="row">
                    <div class="col-8">
                        <div class="form-label-group">
                            {input $form["nameList"]["street"]}
                            {label $form["nameList"]["street"] /}

                        </div>
                    </div>
                    <div class="col-4">
                        <div class="form-label-group">
                            {input $form["nameList"]["street_number"]}
                            {label $form["nameList"]["street_number"] /}

                        </div>
                    </div>
                </div>
                <div class="form-label-group">
                    {input $form["nameList"]["zip"]}
                    {label $form["nameList"]["zip"] /}

                </div>
                <div class="form-label-group">
                    {input $form["nameList"]["city"]}
                    {label $form["nameList"]["city"] /}

                </div>
                <div class="form-label-group">
                    {input $form["nameList"]["state"]}
                    {label $form["nameList"]["state"] /}

                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mt-3" >
        <div class="card">
            <div class="card-header bg-gray-400">
                <h6 class="card-title mb-0">Korešpondenčná adresa</h6>
            </div>
            <div class="card-body  bd-color-gray-lighter">
                <div class="row">
                    <div class="col-8">
                        <div class="form-label-group">
                            {input $form["nameList"]["p_street"]}
                            {label $form["nameList"]["p_street"] /}

                        </div>
                    </div>
                    <div class="col-4">
                        <div class="form-label-group">
                            {input $form["nameList"]["p_street_number"]}
                            {label $form["nameList"]["p_street_number"] /}

                        </div>
                    </div>
                </div>
                <div class="form-label-group">
                    {input $form["nameList"]["p_zip"]}
                    {label $form["nameList"]["p_zip"] /}

                </div>
                <div class="form-label-group">
                    {input $form["nameList"]["p_city"]}
                    {label $form["nameList"]["p_city"] /}

                </div>
                <div class="form-label-group">
                    {input $form["nameList"]["p_state"]}
                    {label $form["nameList"]["p_state"] /}

                </div>
            </div>
        </div>
    </div>
	<div class="col-12 text-center mt-3">
		{*php dump($presenter->getUser()->isAllowed("client","edit"))*}
		{if !isset($button_save) || (isset($button_save) && $button_save) }
			{input save, class=>"btn btn-success btn-p"}
		{/if}
		{if !($companyService->hasOption("client_noedit") && $presenter->getParameter("id") !== $user->getId() && !$presenter->isSuperadmin()) && $presenter->getUser()->isAllowed("Client","edit") && !(!isset($button_save) || (isset($button_save) && $button_save))}
			<a n:href="Client:edit, id=>$presenter->getParameter('id')" class="btn btn-primary">Upraviť klienta</a>
		{/if}
		<a n:href="Client:default" class="btn btn-danger btn-p">Späť</a>
	</div>
</div>
{/form}