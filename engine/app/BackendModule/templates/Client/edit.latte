{var $title="Úprava klienta"}
{var $provisionCount = $presenter["provisionListGrid"]->getDataSource()->getCount()}
{block content}

    <div class="card bd-0">
        <div class="card-header bg-dark">
            <ul class="nav nav-tabs nav-tabs-for-dark card-header-tabs card-header-tabs-gray"  id="myTab" role="tablist">
                <li class="nav-item">
                    <a class="nav-link bd-0 pd-y-8" data-toggle="tab"  role="tab" href="#default"
                       aria-expanded="true" aria-controls="default">Údaje o klientovi</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link bd-0 pd-y-8" data-toggle="tab"  role="tab" href="#companies"
                       aria-expanded="true" aria-controls="default">Firemné profily</a>
                </li>
                <li class="nav-item" n:if="$user->isAllowed('Client','document_view')">
                    <a class="nav-link bd-0 tx-gray-light"  data-toggle="tab" role="tab" href="#documents"
                       aria-expanded="true" aria-controls="documents">Dokumenty
                        <span class="square-15 bg-primary rounded-circle tx-12 text-center tx-bold tx-white" n:snippet="documentCount">
                            {$presenter["documentViewer"]->getCount()}
                        </span>
                    </a>
                </li>
                <li class="nav-item"  n:if="$companyService->hasModule('Trade')">
                    <a class="nav-link bd-0 tx-gray-light" data-toggle="tab" role="tab" href="#trades"
                       aria-expanded="true" aria-controls="trades">Obchody</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link bd-0 tx-gray-light" data-toggle="tab" role="tab" href="#history"
                       aria-expanded="true" aria-controls="history">História zmien</a>
                </li>
                <li class="nav-item" n:if="$provisionCount > 0 && false">
                    <a class="nav-link bd-0 tx-gray-light" data-toggle="tab" role="tab" href="#provision"
                       aria-expanded="true" aria-controls="history">Provízne listy
                        <span class="square-15 bg-primary rounded-circle tx-12 text-center tx-bold tx-white">
                            {$provisionCount}
                        </span>
                    </a>
                </li>
                {var $aclRole = new Kubomikita\ActiveRecord\AclRole($USER->acl_role)}
                <li class="nav-item" n:if="$aclRole->isCoworker()">
                    <a class="nav-link bd-0 tx-gray-light" data-toggle="tab" role="tab" href="#career"
                       aria-expanded="true" aria-controls="history">Kariéra</a>
                </li>
                {var $surveyCount = $presenter["userSurveyGrid"]->getDataSource()->getCount()}
                <li class="nav-item" n:if="$surveyCount > 0">
                    <a class="nav-link bd-0 tx-gray-light" data-toggle="tab" role="tab" href="#survey"
                       aria-expanded="true" aria-controls="history">Online dotazník
                        <span class="square-15 bg-primary rounded-circle tx-12 text-center tx-bold tx-white">
                            {$surveyCount}
                        </span>
                    </a>
                </li>
                <li class="nav-item" n:if="$companyService->hasModule('Code')">
                    <a class="nav-link bd-0 tx-gray-light" data-toggle="tab" role="tab" href="#qrcode"
                       aria-expanded="true" aria-controls="qrcode">Môj QR a čiarový kód</a>
                </li>
            </ul>
        </div><!-- card-header -->
        <div class="card-body color-gray-lighter bg-gray-200 bd-0 py-3 px-0">
            <div class="tab-content">
                <div class="tab-pane fade" id="default" role="tabpanel" aria-labelledby="home-tab">
                    {snippetArea clientForm}
                        {include "@editForm.latte"}
                    {/snippetArea}
                </div>
                <div class="tab-pane fade" id="documents" role="tabpanel" aria-labelledby="profile-tab" >
                    {control documentViewer}
                    {include "@documents.latte"}
                </div>
                <div class="tab-pane fade" id="qrcode" role="tabpanel" aria-labelledby="qrcode-tab"  n:if="$companyService->hasModule('Code')">
                    <div class="card mb-3">
                        <div class="card-header bg-gray-400">
                            <h6 class="card-title mb-0">Môj QR kód</h6>
                        </div>
                        <div class="card-body  bd-color-gray-lighter">
                            {include "../Code/@qrcode.latte"}
                            {*php dump($userModel->getQrCode($selfUser))*}
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="trades" role="tabpanel" aria-labelledby="contact-tab"  n:if="$companyService->hasModule('Trade')">
                    <div class="card mb-3">
                        <div class="card-header bg-gray-400">
                            <h6 class="card-title mb-0">Zoznam obchodov</h6>
                        </div>
                        <div class="card-body pd-0 bd-color-gray-lighter">
                            {control clientTradesGrid}
                        </div>
                    </div>
                    <div class="card mb-3">
                        <div class="card-header bg-danger">
                            <h6 class="card-title mb-0 tx-white">Zoznam neevidovaných obchodov ({$watchedTrades->count("*")})</h6>
                        </div>
                        <div class="card-body  bd-color-gray-lighter">
                            {*control flashMessage*}
                            {*php dump($watchedTrades->fetchAll())*}
                            <table class="table table-striped table-bordered table-sm">
                                <thead>
                                <tr>
                                    <th>Názov</th>
                                    <th>Oblasť obchodu</th>
                                    <th></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr n:foreach="$watchedTrades as $nt">
                                    {var $fill = ["trade_0" => ["add" => 1,"l_section"=>$nt->l_section,"l_section_type"=>$nt->l_section_type]]}
                                    {var $disable = ["trade_0" => ["l_section" => 1, "l_section_type" => 1]]}
                                    <td>
                                        {$nt->name}
                                        <div class="tx-11">{$nt->desc}</div>
                                    </td>
                                    <td>{$nt->ref("l_section")->name}</td>
                                    <td width="50" align="right">
                                        <a n:href="Trade:add, user=> $USER->id, fill => $fill, disable => $disable" class="btn btn-success btn-xs"><i class="fas fa-plus-square"></i></a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="contact-tab">
                    <div class="card">
                        <div class="card-header bg-gray-400">
                            <h6 class="card-title mb-0">História zmien pri spoluprac. / klientovi</h6>
                        </div>
                        <div class="card-body p-0 bd-color-gray-lighter">
                            {control historyGrid}
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="companies" role="tabpanel" aria-labelledby="contact-tab">
                    <div class="card">
                        <div class="card-header bg-gray-400">
                            <h6 class="card-title mb-0">Firemné profily</h6>
                        </div>
                        <div class="card-body p-0 bd-color-gray-lighter">
                            {control invoiceAddressGrid}
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="provision" role="tabpanel" aria-labelledby="contact-tab" n:if="$provisionCount > 0 && false">
                    <div class="card">
                        <div class="card-header bg-gray-400">
                            <h6 class="card-title mb-0">Provízne listy</h6>
                        </div>
                        <div class="card-body p-0 bd-color-gray-lighter">
                            {control provisionListGrid}
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="career" role="tabpanel" aria-labelledby="contact-tab" n:if="$aclRole->isCoworker()">
                    <div class="row">
                        <div class="col-xl-3 col-lg-6">
                            <div class="card">
                                <div class="card-header bg-gray-400">
                                    <h6 class="card-title mb-0">Kariéra</h6>
                                </div>
                                <div class="card-body bd-color-gray-lighter">
                                    {control career}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="survey" role="tabpanel" aria-labelledby="contact-tab">
                    <div class="card">
                        <div class="card-header bg-gray-400">
                            <h6 class="card-title mb-0">Vytvorené online dotazníky</h6>
                        </div>
                        <div class="card-body p-0 bd-color-gray-lighter">
                            {control userSurveyGrid}
                        </div>
                    </div>
                </div>
            </div>

        </div><!-- card-body -->
    </div>


    <div id="modalInvoiceAddress" class="modal fade" aria-hidden="true" style="display: none;">
        {form modalInvoiceAddressForm, class=>"ajax"}
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content bd-0 tx-14">
                    <div class="modal-header pd-y-20 pd-x-25 bg-gray-400">
                        <h6 class="tx-14 mg-b-0 tx-inverse tx-bold">Firemný profil</h6>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body pd-25">
                        <div class="form-group">
                            <label class="ckbox ckbox-primary">
                                {$form["po"]->getControlPart()}
                                <span>{$form["po"]->getLabelPart()}</span>
                            </label>
                        </div>
                        <div id="company-container">
                            <div class="form-label-group">
                                <div class="input-group">
                                    {input registration_id}
                                    {label registration_id /}
                                    <div class="input-group-append">
                                        <a n:href="loadByIco!" id="company-ico-search" class="btn btn-primary">Vyhľadať</a>
                                    </div>
                                </div>
                            </div>
                            <div class="form-label-group">
                                {input tax_id}
                                {label tax_id /}
                            </div><div class="form-label-group">
                                {input vat_id}
                                {label vat_id /}
                            </div>
                            <div class="form-label-group">
                                {input register}
                                {label register /}
                            </div>

                        </div>
                        <div class="form-label-group">
                            {input name}
                            {label name /}
                        </div>
                        <div class="form-label-group">
                            {input street}
                            {label street /}
                        </div>
                        <div class="row">
                            <div class="col-lg-4">
                        <div class="form-label-group">
                            {input post_code}
                            {label post_code /}
                        </div>
                            </div>
                            <div class="col-lg-8">
                            <div class="form-label-group">
                            {input town}
                            {label town /}
                        </div>
                        </div>
                        </div>
                        <div class="form-label-group">
                            {input state}
                            {label state /}
                        </div>
                        <div class="form-label-group">
                            {input email}
                            {label email /}
                        </div>
                        <div class="form-label-group">
                            {input phone}
                            {label phone /}
                        </div>
                        <div class="form-label-group">
                            {input iban, class=>"form-control iban"}
                            {label iban /}
                        </div>
                    </div>
                    <div class="modal-footer">
                        {input save, class=>"btn btn-success btn-p"}
                        <button type="button" class="btn btn-danger" data-dismiss="modal">{_"Zavrieť"}</button>
                    </div>
                </div>
            </div><!-- modal-dialog -->
        {/form}
    </div>



{/block}

{block scripts}
<script>
    $(document).ready(function (){
	    $(document).on("click","#company-ico-search",function(e){
		    e.preventDefault();
		    $.nette.ajax({
			    method: "get",
			    url: $(this).attr("href"),
			    data: {
				    ico: $("#" + {$presenter["modalInvoiceAddressForm"]["registration_id"]->htmlId}).val(),
                    form: 'modalInvoiceAddressForm',
                    keys: {
				    	company_dic : "tax_id",
                        company_name: "name",
                        city: "town",
                        zip: "post_code",
                        street:"street",

                    }
			    },
			    success: function (resp) {
				    if(typeof resp.data !== "undefined") {
					    resp.data.forEach(function (item) {
						    $("#" + item.id).val(item.value);
					    });
				    }
			    }
		    });
	    });
    })
</script>
{control documentViewer:js}
{control dropzoneForm:js}

