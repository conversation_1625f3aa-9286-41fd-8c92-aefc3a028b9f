{varType App\Record\Closure $closure}
{default $tableClass = 'table-sm mb-0'}
<table class="table {$tableClass}">
    <tr>
        <th class=" border-top-0"></th>
        <th class="tx-right border-top-0">Suma bez DPH</th>
        <th class="tx-right border-top-0">Sadzba DPH</th>
        <th class="tx-right border-top-0">DPH</th>
        <th class="tx-right border-top-0">Spolu vrátane DPH</th>
    </tr>
    {php $sumProvision = $sumProvisionVat = $sumVat = []}
    <tr n:foreach="$closure->sumVat() as $k => $v">
        <td n:if="$k == 0">Oslobodené od DPH</td>
        <td n:if="$k > 0">Podlieha DPH</td>
        <td class="tx-right">{$v["provision"]|currency}</td>
        <td class="tx-right">{$k}%</td>
        <td class="tx-right">{$v["vat"]|currency}</td>
        <td class="tx-right">{$v["provisionVat"]|currency}</td>

        {php $sumProvision[] = $v["provision"]}
                    {php $sumProvisionVat[] = $v["provisionVat"]}
                    {php $sumVat[] = $v["vat"]}

    </tr>
    <tr>
        <td class="tx-bold">
            Spolu
        </td>
        <td class="tx-right tx-bold">{array_sum($sumProvision)|currency}</td>
        <td></td>
        <td class="tx-right tx-bold">{array_sum($sumVat)|currency}</td>
        <td class="tx-right tx-bold">{array_sum($sumProvisionVat)|currency}</td>
    </tr>
</table>