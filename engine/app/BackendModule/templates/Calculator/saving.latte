{var $title = "Kalkulačka sporenia"}

{block content}
{*control savingForm*}

{snippet savings}


{form savingForm, class=>"ajax"}

            <div class="card">
                <div class="card-header bg-gray-400">
                    <h6 class="card-title mb-0">Kalkulačka na výpočet sporenia</h6>
                </div>
                <div class="card-body bd-color-gray-lighter">
                    <table class="table table-bordered table-hover table-striped table-condensed" n:foreach="$savingList as $key => $savings">
                        <thead>
                        <tr>
                            <th width="30%"><span  class="tx-primary">{$key}</span></th>
                            <th width="150" class="text-center">úrok v % p.a.</th>
                            <th width="150" >mesačne</th>
                            <th width="220" >doba sporenia</th>
                            <th class="text-right" n:ifset="$inserted">vložené spolu</th>
                            <th class="text-right" n:ifset="$inserted">úroky</th>
                            <th class="text-right" n:ifset="$inserted">našetrené spolu</th>

                        </tr>
                        </thead>
                        <tbody>
                            <tr n:foreach="$savings as $s" n:class="($form['item_'.$s->id]['amount']->getValue() == 0 || $form['item_'.$s->id]['numberOfYears']->getValue() == 0) ? 'table-light' : '' "> {*(!$s->classic ? 'table-primary' : '')*}

                                {var $container = $form["item_".$s->id]}
                                <td>
                                    {*<span n:if="!$s->classic" class="badge badge-primary">Navrhované sporenie</span>*} {$s->name}
                                </td>
                                <td width="150" class="text-center tx-black">
                                    <div class="input-group input-group-sm">
                                        {input $container["rate"], class => "form-control form-control-sm"}
                                        <div class="input-group-append">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                    {*$s->rate*100*}</td>
                                <td width="150">
                                    {*<div class="form-label-group mb-0">*}
                                    <div class="input-group input-group-sm">
                                        {input $container["amount"], class => "form-control form-control-sm"}
                                        <div class="input-group-append">
                                            <span class="input-group-text">€</span>
                                        </div>
                                    </div>
                                </td>
                                <td width="220">
                                    {*<div class="form-label-group mb-0">*}
                                    <div class="input-group input-group-sm">
                                        {input $container["numberOfYears"], class => "form-control form-control-sm"}
                                    <div class="input-group-append">
                                        <span class="input-group-text">rokov</span>
                                    </div>
                </div>
                        {*label $container["numberOfYears"]}
                                    </div>*}
                                </td>
                                <td n:ifset="$inserted" class="text-right">
                                    {ifset $inserted[$key][$s->id]}
                                    {$inserted[$key][$s->id]|currency}
                                    {/ifset}
                                </td>
                                <td n:ifset="$inserted" class="text-right">
                                    {ifset $rate[$key][$s->id]}
                                        {$rate[$key][$s->id]|currency}
                                    {/ifset}
                                </td>
                                <td n:ifset="$inserted" class="text-right tx-black">
                                    {ifset $saved[$key][$s->id]}
                                    {$saved[$key][$s->id]|currency}
                                        {/ifset}
                                </td>
                            </tr>
                        </tbody>
                        <tfoot n:ifset="$totalPerMonth">
                        <tr class="">
                            <td></td>
                            <td></td>
                            <td class="text-right tx-bold tx-danger">{$totalPerMonth[$key]|currency}</td>
                            <td></td>
                            <td class="text-right tx-bold tx-danger">{$totalInserted[$key]|currency}</td>
                            <td class="text-right tx-bold tx-primary">{$totalRate[$key]|currency}</td>
                            <td class="text-right tx-bold tx-success">{$totalSaved[$key]|currency}</td>
                        </tr>
                        </tfoot>
                    </table>
                    <div class="text-center my-3">
                        {input save, class=>"btn btn-success btn-p"}
                        <a n:ifset="$inserted" n:href="savingDefault!" class="btn btn-danger ajax">Vymazať formulár</a>
                    </div>

                    <div id="ch5" class="ht-250 ht-sm-300" n:if="isset($chartSaved)"></div>

                </div>
            </div>
{/form}
    {ifset $chartSaved}
    <script src="{$adminAssetsPath}/lib/jquery.flot/jquery.flot.js"></script>
    <script src="{$adminAssetsPath}/lib/jquery.flot/jquery.flot.resize.js"></script>
    <script src="{$adminAssetsPath}/lib/flot-spline/js/jquery.flot.spline.min.js"></script>
    <script>
		var newCust = {Nette\Utils\Json::encode($chartSaved)|noescape}; //[[1,1], [2,20], [3,30], [4, 40], [5, 500]];
		var retCust = {Nette\Utils\Json::encode($chartRecommend)|noescape};
		var plot = $.plot($('#ch5'),[
			{
				data: newCust,
				label: 'Spolu nasporené',
				color: '#0866C6'
			},
			{
				data: retCust,
				label: 'Navrhovaný stav',
				color: '#23BF08'
			},
			{
				data: {Nette\Utils\Json::encode($chartClassic)|noescape},
				label: 'Aktuálny stav',
				color: "#DC3545"//'#666'
			}
		],{
			series: {
				lines: {
					show: false
				},
				splines: {
					show: true,
					tension: 0.4,
					lineWidth: 0,
					fill: 0.7
				},
				shadowSize: 0
			},
			points: {
				show: true,
			},
			grid: {
				hoverable: true,
				clickable: true,
				borderColor: '#ddd',
				borderWidth: 0,
				labelMargin: 5,
				backgroundColor: '#fff'
			},
            zoom: {
				interactive:true
            },
			yaxis: {
				min: 0,
				//max: 500,
				color: '#eee',
				font: {
					size: 15,
					color: '#999'
				}
			},
			xaxis: {
				min: 0,
				color: '#eee',
				font: {
					size: 15,
					color: '#999'
				}
			}
		});

		$("<div id='tooltip'></div>").css({
			position: "absolute",
			display: "none",
			border: "1px solid #666",
			padding: "5px",
			color: "#000",
			"font-size": "12px",
			"background-color": "rgba(255,255,255,0.8)",

		}).appendTo("body");

		$("#ch5").bind("plothover", function (event, pos, item) {

			if (!pos.x || !pos.y) {
				return;
			}


			//if ($("#enableTooltip:checked").length > 0) {
			if (item) {
				var x = item.datapoint[0].toFixed(2),
					y = item.datapoint[1].toFixed(2);

				$("#tooltip").html(item.series.label + ": <strong>" + /*x + " = " +*/ y + '</strong>')
					.css({ top: item.pageY+5, left: item.pageX+5 })
					.fadeIn(200);
			} else {
				$("#tooltip").hide();
			}
			//}
		});

		$("#ch5").bind("plothovercleanup", function (event, pos, item) {
			$("#tooltip").hide();
		});
    </script>
    {/ifset}
{/snippet}
    {*php dump($chart, Nette\Utils\Json::encode($chart))*}
{/block}
{block scripts}
<script>

</script>

{/block}