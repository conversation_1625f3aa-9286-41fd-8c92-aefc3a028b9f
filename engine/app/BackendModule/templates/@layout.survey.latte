{default $lang = 'sk'}
<!DOCTYPE html>
<html lang="{$lang}">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Meta -->
    <meta name="description" content="">
    <meta name="author" content="adv j.s.a.">

    <title>{$title} | {$presenter->companyService->getCompany()->name}</title>

    <!-- vendor css -->
    <link href="{$adminAssetsPath}/lib/@fortawesome/fontawesome-free/css/all.min.css" rel="stylesheet">
    <link href="{$adminAssetsPath}/lib/ionicons/css/ionicons.min.css" rel="stylesheet">

    <!-- Bracket CSS -->
    <link rel="stylesheet" href="{$adminAssetsPath}/css/bracket.css">
    {if $companyService->getCompany()->stylesheet !== null}
        <link rel="stylesheet" href="{$adminAssetsPath.$companyService->getCompany()->stylesheet}">
    {/if}
    <link rel="stylesheet" type="text/css" href="{$adminAssetsPath}/css/custom.css?v={$version}">
    <link rel="stylesheet" type="text/css" href="{$adminAssetsPath}/css/material-inputs.css?v={$version}">
	{*<link rel="stylesheet" href="{$adminAssetsPath}/css/bracket.oreo.css">*}
</head>

<body class="bg-primary">
<div class="alert alert-danger tx-18 text-center mb-0" n:if="$presenter->isDev()"><i class="fas fa-info-circle"></i> Pozor, využívate <strong>{$presenter->httpRequest->getUrl()->getHost()}</strong> (@dev) verziu aplikácie.</div>

{control flashMessages}

{block content}{/block}

<script>
    let adminAssetsPath = {$adminAssetsPath}
</script>
<script src="{$adminAssetsPath}/lib/jquery/jquery.min.js"></script>
<script src="{$adminAssetsPath}/lib/jquery-ui/ui/widgets/datepicker.js"></script>
<script src="{$adminAssetsPath}/lib/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="{$basePath}/assets/app/netteAjax.js"></script>
<script src="{$basePath}/assets/app/live-form-validation.js"></script>
<script src="{$basePath}/assets/app/netteInit.js"></script>
<script>
	$.nette.ext({
		success: function (payload) {
			if (typeof payload.closeModal !== "undefined") {
				$(".modal button.close").trigger("click");
                //console.log($(".modal"));
                //$(".modal").modal("hide");
			}
			if(typeof payload.closeModalForce !== "undefined"){
				$(".modal").modal("hide");
				$("body").removeClass("modal-open");
				$(".modal-backdrop").remove();
            }
			if(typeof payload.openModal !== "undefined"){
				$(payload.openModal).modal("show");
            }
		}
	});
</script>
{block scripts}{/block}

</body>
</html>
