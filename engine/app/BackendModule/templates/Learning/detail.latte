{var $title = "Webinár"}

{block content}
    {php dump($learning)}
    <div class="row">
        <div class="col-lg-6">
    <iframe src="{$link}" class="youtube-embed" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
        </div>
    </div>
    <style>
		.youtube-embed {
			--container-width: 1145px;
			--ratio: calc(16 / 9); /* 1.77 */
			width: 100%;
			height: calc(var(--container-width) / var(--ratio));
		}

		/*iframe{
			width: 100%;
			height: calc(100%/1.77);
		}*/
    </style>
{/block}

{block scripts}

<script>
	let startDate = new Date();
	let elapsedTime = 0;

	const focus = function() {
		startDate = new Date();
		console.log(startDate);
	};

	const blur = function() {
		const endDate = new Date();
		const spentTime = endDate.getTime() - startDate.getTime();
		elapsedTime += spentTime;
		console.log(elapsedTime);
	};

	const beforeunload = function() {
		const endDate = new Date();
		const spentTime = endDate.getTime() - startDate.getTime();
		elapsedTime += spentTime;
		console.log(elapsedTime);
		$.nette.ajax({
			type  : "GET",
			url   : {link spentTime!},
			data  : {
				learning: {$presenter->getParameter("id")},
                time: elapsedTime
			}
        })
		// elapsedTime contains the time spent on page in milliseconds
	};

	const activity = function (){
		$.nette.ajax({
			type  : "GET",
			url   : {link activity!},
			data  : {
				learning: {$presenter->getParameter("id")},
				time: elapsedTime
			}
		})
    }

	//window.addEventListener('focus', focus);
	//window.addEventListener('blur', blur);
	window.addEventListener('beforeunload', beforeunload);

	setInterval(activity, 60000);
</script>