{default $reg = true}
{default $ident = true}
{default $l_person_type_id = null}
{default $button_save = true}
{form coworkerEditForm, class=>"ajax"}
	<div class="row justify-content-center">
		<div class="col-md-4">
			<div class="card mb-3">
				<div class="card-header bg-gray-400 d-flex align-items-center">
					<h6 class="card-title mb-0">Prihlásenie</h6>
                    <div class="ml-auto" n:if="$presenter->getUser()->isAllowed('Settings.Users','view') && isset($USER)">
                        <a n:href=":Backend:Setting:Users:default, do => 'specialist', id => $USER->id" class="btn btn-xs btn-danger" onclick="javascript: return confirm('Naozaj?')" n:if="$USER->type === Webtec\Models\UserModel::TYPE_COWORKER"><PERSON><PERSON><PERSON><PERSON> na špecialistu</a>
                    </div>
				</div>
				<div class="card-body  bd-color-gray-lighter">
					<div class="form-label-group">
						{var $form = $control["coworkerEditForm"]}
                        {input username}
                        {label username /}

						<div id="{$form["username"]->getErrorId()}" n:class="$form['username']->hasErrors() ? invalid-feedback" >{inputError username}</div>
					</div>
					<div n:class="$type != Webtec\Models\UserModel::TYPE_PARTNER ? form-label-group : d-none">
                        {if !$reg || (isset($reg_reload_hide) && $reg_reload_hide)}
                            {input reg_id}
                            {label reg_id /}
	                    {else}
                            <div class="input-group" n:snippet="inputRegId">
                                {var $form = $control["coworkerEditForm"]}
							    {input reg_id}
                                {label reg_id /}
                                <div class="input-group-append">
                                    <a class="btn btn-primary" {*id="company-get-ico"*} href="javascript:;" id="check-reg-id"><i class="fas fa-redo tx-16"></i></a>
                                </div>
                            </div>
                        {/if}

                        <div id="{$form["reg_id"]->getErrorId()}" n:class="$form['reg_id']->hasErrors() ? invalid-feedback" >{inputError reg_id}</div>
					</div>
                    {*<div n:class="$type != Webtec\Models\UserModel::TYPE_PARTNER ? form-label-group : d-none">
                        {if !$ident}
                            {input identifier}
                            {label identifier /}
	                    {else}
                            <div class="input-group" n:snippet="inputIdentifier">
                            {var $form = $control["coworkerEditForm"]}
							    {input identifier}
                                {label identifier /}
                            <div class="input-group-append">
                                <a class="btn btn-primary ajax"  n:href="checkIdentifier!"><i class="fas fa-redo tx-16"></i></a>
                            </div>
                        </div>
                        {/if}

                    </div>*}
					<div class="form-label-group">
                        {input $form["nameList"]["phone"], class => "form-control phone"}
                        {label $form["nameList"]["phone"] /}

					</div>
					<div class="form-label-group">
                        {input acl_role_id}
                        {label acl_role_id /}

					</div>
					<div class="form-label-group">
                        {input users_id}
                        {label users_id /}

					</div>
					<div n:class="$type != Webtec\Models\UserModel::TYPE_PARTNER ? form-label-group : d-none">
                        {input user_groups_id}
                        {label user_groups_id /}

					</div>
					<div class="form-label-group">
                        {input l_person_type_id}
                        {label l_person_type_id /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["iban"], class=>"form-control iban"}
                        {label $form["nameList"]["iban"] /}

					</div>
				</div>
			</div>
		</div>

		<div class="col-md-4">
			<div class="card  mb-3" id="person">
				<div class="card-header bg-gray-400">
					<h6 class="card-title mb-0">Osobné informácie</h6>
				</div>
				<div class="card-body  bd-color-gray-lighter">
{*
                    <label class="ckbox ckbox-danger">
                        {$form["newsletters"]->getControlPart()}
                        <span>{$form["newsletters"]->getLabelPart()}</span>
                    </label>

                    <div class="form-label-group">
                        {input emails, class => "form-control"}
                        {label emails /}
                    </div>*}


                    <div class="form-label-group">
                        {input $form["nameList"]["degree_before"], class => "form-control"}
                        {label $form["nameList"]["degree_before"] /}
                    </div>
					<div class="form-label-group">
                        {input $form["nameList"]["name"], class => "form-control"}
                        {label $form["nameList"]["name"] /}
					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["surname"], class => "form-control"}
                        {label $form["nameList"]["surname"] /}
					</div>
                    <div class="form-label-group">
                        {input $form["nameList"]["degree_after"], class => "form-control"}
                        {label $form["nameList"]["degree_after"] /}
                    </div>
					<div class="form-label-group">
                        {input $form["nameList"]["birthdate"]}
                        {label $form["nameList"]["birthdate"] /}
					</div>
                    <div class="form-label-group">
                        {input $form["nameList"]["identification_number"]}
                        {label $form["nameList"]["identification_number"] /}
                    </div>
				</div>
			</div>

			<div class="card  mb-3" id="company" style="display: none;">
				<div class="card-header bg-gray-400">
					<h6 class="card-title mb-0">Firemné údaje</h6>
				</div>
				<div class="card-body  bd-color-gray-lighter">
					<div class="form-label-group">

						<div class="input-group">

							{input $form["nameList"]["company_ico"], class => "form-control"}
                             {label $form["nameList"]["company_ico"] /}
							<div class="input-group-append">
								<a class="btn btn-primary" n:href="loadByIco!" id="company-ico-search">Vyhľadať</a>
							</div>

						</div>
                        {*php dump($form["nameList"]["company_ico"])*}
                        <div id="{$form["nameList"]["company_ico"]->getErrorId()}" n:class="$form['nameList']['company_ico']->hasErrors() ? invalid-feedback" >{inputError $form["nameList"]["company_ico"]}</div>
					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["company_dic"], class => "form-control"}
                        {label $form["nameList"]["company_dic"] /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["company_icdph"], class => "form-control"}
                        {label $form["nameList"]["company_icdph"] /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["company_name"], class => "form-control"}
                        {label $form["nameList"]["company_name"] /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["company_owner"], class => "form-control"}
                        {label $form["nameList"]["company_owner"] /}

					</div>
                    <div class="form-label-group">
                        {input $form["nameList"]["company_register"], class => "form-control"}
                        {label $form["nameList"]["company_register"] /}

                    </div>
				</div>
			</div>
            <div class="card mb-3">
                <div class="card-header bg-gray-400">
                    <h6 class="card-title mb-0">Poznámka</h6>
                </div>
                <div class="card-body  bd-color-gray-lighter">
                    <div class="form-label-group mb-0">
                        {input note}
                        {label note /}

                    </div>
                </div>
            </div>
		</div>
		<div class="col-md-4">
			<div class="card  mb-3">
				<div class="card-header bg-gray-400">
					<h6 class="card-title mb-0">Adresa</h6>
				</div>
				<div class="card-body  bd-color-gray-lighter">
                    <div class="row">
                        <div class="col-8">
                            <div class="form-label-group">
                                {input $form["nameList"]["street"]}
                                {label $form["nameList"]["street"] /}

                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-label-group">
                                {input $form["nameList"]["street_number"]}
                                {label $form["nameList"]["street_number"] /}

                            </div>
                        </div>
                    </div>
					<div class="form-label-group">
                        {input $form["nameList"]["zip"], class => "form-control"}
                        {label $form["nameList"]["zip"] /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["city"], class => "form-control"}
                        {label $form["nameList"]["city"] /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["state"], class => "form-control select2"}
						{label $form["nameList"]["state"] /}

					</div>
				</div>
			</div>
			<div class="card mb-3">
				<div class="card-header bg-gray-400">
					<h6 class="card-title mb-0">Korešpondenčná adresa</h6>
				</div>
				<div class="card-body  bd-color-gray-lighter">
                    <div class="row">
                        <div class="col-8">
                            <div class="form-label-group">
                                {input $form["nameList"]["p_street"]}
                                {label $form["nameList"]["p_street"] /}

                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-label-group">
                                {input $form["nameList"]["p_street_number"]}
                                {label $form["nameList"]["p_street_number"] /}

                            </div>
                        </div>
                    </div>
					<div class="form-label-group">
                        {input $form["nameList"]["p_zip"], class => "form-control"}
                        {label $form["nameList"]["p_zip"] /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["p_city"], class => "form-control"}
                        {label $form["nameList"]["p_city"] /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["p_state"], class => "form-control select2"}
                        {label $form["nameList"]["p_state"] /}

					</div>
				</div>
			</div>
		</div>
		{*<div n:class="(($type === Webtec\Models\UserModel::TYPE_COWORKER || $type === Webtec\Models\UserModel::TYPE_SPECIALIST) || $presenter->getUser()->isAllowed('Coworker','edit_details')) ?  'col-md-4' : 'd-none'">*}
		<div n:class="$presenter->isSuperadmin() ? 'col-md-4' : 'd-none'">
			<div class="card mb-3">
				<div class="card-header bg-gray-400">
					<h6 class="card-title mb-0">Osobitné údaje spolupracovníka</h6>
				</div>
				<div class="card-body  bd-color-gray-lighter">
					<div class="form-label-group">
                        {input $form["nameList"]["career_id"], class => "form-control select2"}
                        {label $form["nameList"]["career_id"] /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["start_position"], class => "form-control"}
                        {label $form["nameList"]["start_position"] /}

					</div>
					<div class="form-label-group">
                        {input $form["nameList"]["start_points"], class => "form-control"}
                        {label $form["nameList"]["start_points"] /}

					</div>
                    <div n:class="$presenter->isOwner() ? 'form-group' : 'd-none'">
                        <label class="ckbox ckbox-danger">
                            {$form["in_list"]->getControlPart()}
                            <span>{$form["in_list"]->getLabelPart()}</span>
                        </label>
                    </div>
                    <div n:class="$presenter->isOwner() ? 'form-group' : 'd-none'">
                        <label class="ckbox ckbox-danger">
                            {$form["pay_provisions"]->getControlPart()}
                            <span>{$form["pay_provisions"]->getLabelPart()}</span>
                        </label>
                    </div>
                    <div n:class="$presenter->isOwner() && $companyService->getCompanyId() === 1 ? 'form-group' : 'd-none'">
                        <label class="ckbox ckbox-danger">
                            {$form["advis"]->getControlPart()}
                            <span>{$form["advis"]->getLabelPart()}</span>
                        </label>
                    </div>
                    <div n:class="$presenter->isOwner() ? 'form-group' : 'd-none'">
                        <label class="ckbox ckbox-danger">
                            {$form["cms"]->getControlPart()}
                            <span>{$form["cms"]->getLabelPart()}</span>
                        </label>
                    </div>
                    <div n:class="$presenter->isSuperadmin() ? 'form-group' : 'd-none'">
                        <label class="ckbox ckbox-danger">
                            {$form["extern_partner_bool"]->getControlPart()}
                            <span>{$form["extern_partner_bool"]->getLabelPart()} <span class="help-block tx-danger tx-13">(Generuje sa prov. list pre každú dcérsku spoločnosť zvlášť)</span></span>
                        </label>

                    </div>
                    <div class="form-label-group" id="extern-partner-container">
                        {input $form["extern_partner"]}
                        {label $form["extern_partner"] /}

                    </div>
                    <div class="form-label-group">
                        {input $form["divisions"]}
                        {label $form["divisions"] /}

                    </div>
                    <div n:class="$presenter->companyService->isProfiPlus() && $presenter->isSuperadmin() ? form-label-group : d-none">
                        {input $form["profi_contract"]}
                        {label $form["profi_contract"] /}

                    </div>
                    <div n:class="$presenter->companyService->isProfiPlus() && $presenter->isSuperadmin() ? form-label-group : d-none">
                        {input $form["profi_contract_date"]}
                        {label $form["profi_contract_date"] /}

                    </div>
                    <div class="form-label-group">
                        {input $form["user_label"]}
                        {label $form["user_label"] /}

                    </div>
				</div>
			</div>
		</div>
		<div class="col-md-4">
			<div class="card  mb-3">
				<div class="card-header bg-gray-400">
					<h6 class="card-title mb-0">Osobitné údaje spolupracovníka</h6>
				</div>
				<div class="card-body  bd-color-gray-lighter">
					<div class="form-label-group">
                        <input type="text" value="{$points["points"]|points}" class="form-control" disabled>
						<label>Body spolu historicky</label>
					</div>
                    <div class="form-label-group">
                        <input type="text" value="{$points["position"]}" class="form-control" disabled>
                        <label>Pozícia</label>
                    </div>
                    <div class="form-label-group">
                        <input type="text" value="{$points["pointValue"]|currency}" class="form-control" disabled>
                        <label>Hodnota bodu</label>
                    </div>
				</div>
			</div>
		</div>
		<div n:class="($type !== Webtec\Models\UserModel::TYPE_SPECIALIST || !$presenter->getUser()->isAllowed('Coworker','edit_details')) ?  'd-none' : 'col-md-4'">
			<div class="card mb-3">
				<div class="card-header bg-gray-400">
					<h6 class="card-title mb-0">Osobitné údaje špecialistu</h6>
				</div>
				<div class="card-body  bd-color-gray-lighter">
					{*<div class="form-group">
						{label $form["other"]["specialist_section"] /}
						{input $form["other"]["specialist_section"], class => "form-control select2 select2-tag"}
					</div>*}
					<div class="form-group">
						{label $form["other"]["specialist_section_type"] /}
						{input $form["other"]["specialist_section_type"], class => "form-control select2 select2-tag"}
					</div>
					<div class="form-group">
						{label $form["other"]["specialist_partner"] /}
						{input $form["other"]["specialist_partner"], class => "form-control select2 select2-tag"}
					</div>
					<div class="form-group">
						{label $form["other"]["specialist_position"] /}
						{input $form["other"]["specialist_position"], class => "form-control select2 select2-tag"}
					</div>
					<div class="form-group">
						{label $form["other"]["specialist_region"] /}
						{input $form["other"]["specialist_region"], class => "form-control select2 select2-tag"}
					</div>
                    <div n:class="$type != Webtec\Models\UserModel::TYPE_PARTNER ? form-group : d-none">
                        {label office /}
						{input office}
                    </div>
				</div>
			</div>
		</div>
		<div n:class="($type !== Webtec\Models\UserModel::TYPE_PARTNER || !$presenter->getUser()->isAllowed('Coworker','edit_details'))  ? 'd-none' : 'col-md-4'">
			<div class="card  mb-3">
				<div class="card-header bg-gray-400">
					<h6 class="card-title mb-0">Osobitné údaje obchodného partnera</h6>
				</div>
				<div class="card-body bd-color-gray-lighter">
					<div class="form-label-group">
                        {input $form["other_partner"]["l_sections"], class => "form-control select2 select2-tag"}
						{label $form["other_partner"]["l_sections"] /}
					</div>
                    <div class="form-group">
                        <label class="ckbox ckbox-primary">
                            {$form["other_partner"]["ikros_enable"]->getControlPart()}
                            <span>{$form["other_partner"]["ikros_enable"]->getLabelPart()} <strong class="tx-danger">(Ďalej nevývyjané)</strong></span>
                        </label>
                    </div>
                    <div id="ikros-container">
                        <div class="form-label-group">
                            {input $form["other_partner"]["ikros_api_key"]}
                            {label $form["other_partner"]["ikros_api_key"] /}

                        </div>
                        <div class="form-label-group">
                            {input $form["other_partner"]["ikros_invoice_sequence"]}
                            {label $form["other_partner"]["ikros_invoice_sequence"] /}

                        </div>
                        <div class="form-label-group">
                            {input $form["other_partner"]["invoice_last"]}
                            {label $form["other_partner"]["invoice_last"] /}

                        </div>
                        <div class="form-group">
                            <label class="ckbox ckbox-danger">
                                {$form["other_partner"]["invoice_blocked"]->getControlPart()}
                                <span>{$form["other_partner"]["invoice_blocked"]->getLabelPart()}</span>
                            </label>

                        </div>
                    </div>
                    <div class="form-group">
                        <label class="ckbox ckbox-primary">
                            {$form["other_partner"]["advis_invoice_enable"]->getControlPart()}
                            <span>{$form["other_partner"]["advis_invoice_enable"]->getLabelPart()}</span>
                        </label>
                    </div>
                    <div id="advis-invoice-container">
                        <div class="form-label-group">
                            {input $form["other_partner"]["advis_invoice_supplier"]}
                            {label $form["other_partner"]["advis_invoice_supplier"] /}

                        </div>
                    </div>
                    <div class="form-group">
                        <label class="ckbox ckbox-primary">
                            {$form["other_partner"]["superfaktura_enable"]->getControlPart()}
                            <span>{$form["other_partner"]["superfaktura_enable"]->getLabelPart()}</span>
                        </label>
                    </div>
                    <div id="superfaktura-container">
                        <div class="form-label-group">
                            {input $form["other_partner"]["superfaktura_email"]}
                            {label $form["other_partner"]["superfaktura_email"] /}

                        </div>
                        <div class="form-label-group">
                            {input $form["other_partner"]["superfaktura_key"]}
                            {label $form["other_partner"]["superfaktura_key"] /}

                        </div>
                        <div class="form-label-group">
                            {input $form["other_partner"]["superfaktura_company_id"]}
                            {label $form["other_partner"]["superfaktura_company_id"] /}

                        </div>
                        <div class="form-label-group">
                            {input $form["other_partner"]["superfaktura_title"]}
                            {label $form["other_partner"]["superfaktura_title"] /}

                        </div>
                        <div class="form-label-group">
                            {input $form["other_partner"]["superfaktura_module"]}
                            {label $form["other_partner"]["superfaktura_module"] /}

                        </div>

                    </div>
                    <div class="form-label-group">
                        {input $form["partner_id"]} {*select2 select-search*}
                        {label $form["partner_id"] /}

                    </div>
                    <div class="form-label-group">
                        {input $form["bank_account_id"], class => "form-control select2"} {*select2 select-search*}
                        {label $form["bank_account_id"] /}

                    </div>
                    <div class="form-group">
                        <label class="ckbox ckbox-danger">
                            {$form["provision_payer"]->getControlPart()}
                            <span class="tx-danger">{$form["provision_payer"]->getLabelPart()|stripHtml}</span>
                        </label>

                    </div>

				</div>
			</div>
		</div>

		{*<div class="col-md-4">
			<div class="card widget-2">
				<div class="card-header">
					<h6 class="card-title">Iné informácie</h6>
				</div>
				<div class="card-body  bd-color-gray-lighter">
					<div class="form-group">

						{input residence}
					</div>
					<div class="form-group">

						{input mortgage}
					</div>
					<div class="form-group">

						{input car}
					</div>
					<div class="form-group">
						{label l_person_status /}
						{input l_person_status}
					</div>
					<div class="form-group">
						{label note /}
						{input note, rows=>"3"}
					</div>
				</div>
			</div>
		</div>
		*}
		<div class="col-12 text-center mt-3" n:if="$button_save">
			{input save, class=>"btn btn-success btn-p"}
			<a n:href="Coworker:default" class="btn btn-danger btn-p">Späť</a>
		</div>

	</div>
{/form}

{block #scripts}

<script>
	var company = $("#company");
	var person = $("#person");
	function loadForm(val){
		var val = parseInt(val);
		if(val == 1){
			company.hide();
			person.show();
		} else if(val == 2){
			company.show();
			person.show();
		} else if(val == 3){
			company.show();
			person.show();
		}
	}

	$(document).ready(function () {
		{if (int) $l_person_type_id > 0}
		loadForm({$l_person_type_id})
		{/if}

		$("#frm-" + {$form->getName()} + " select[name='l_person_type_id']").change(function(){
			var val = parseInt($(this).val());
			loadForm(val);
		});
		$("#company-ico-search").click(function(e){
			e.preventDefault();
			$.ajax({
				method: "get",
				url: $(this).attr("href"),
				data: {
					ico: $("#" + {$form["nameList"]["company_ico"]->htmlId}).val()
				},
				success: function (resp) {
					console.log(resp.data);
					if(typeof resp.data !== "undefined") {
						resp.data.forEach(function (item) {
							$("#" + item.id).val(item.value);
						});
					}
					$.nette.ext("snippets").updateSnippets(resp.snippets);
				}
			});
		});
		$("#company-get-ico").click(function(e){
			e.preventDefault();
			$.ajax({
				method: "get",
				url: $(this).attr("href"),
				success: function (resp) {
					$("#"+resp.id).val(resp.value).trigger("change");
				}
			});
		});
		{*$("#" + {$form["reg_id"]->htmlId}).on("change",function(e){
			e.preventDefault();
			{dump $form->getElementPrototype()->getId()}
			$.ajax({
				method: "get",
				url: {link ValidateRegId!},
				data: {
					value: $(this).val()
				},
				success: function (payload) {
					console.log(payload);
					LiveForm.addError(document.getElementById("frm-coworkerEditForm-reg_id"),payload.error);
				}
			});
			//var form = document.getElementById({$form->getElementPrototype()->getId()});
			//Nette.validateForm(form);
			//console.log({$form->getElementPrototype()->getId()})
		});*}
	})
	$(document).on("change","#" + {$form["nameList"]["state"]->htmlId},function () {

		let regid = $("#" + {$form["reg_id"]->htmlId});
        if(!regid.is(":disabled")) {
	        $.nette.ajax({
		        url : {link checkRegId!},
		        data: {
			        state: $(this).val()
		        }
	        })
        }
	});
	$(document).on("click", "#check-reg-id", function (){
		let regid = $("#" + {$form["reg_id"]->htmlId});
		if(!regid.is(":disabled")) {
			let state = null;
			if($("#" + {$form["nameList"]["state"]->htmlId}).val().length > 0){
				state = $("#" + {$form["nameList"]["state"]->htmlId}).val();
            }
			$.nette.ajax({
				url : {link checkRegId!},
				data: {
					state: state
				}
			})
		}
    });
	$(document).on("change","#" + {$form["reg_id"]->htmlId},function () {
		$.ajax({
			method: "get",
			url: {link ValidateRegId!},
			data: {
				reg_id: $(this).val()
			},
			success: function(resp) {
				$.nette.ext("snippets").updateSnippets(resp.snippets);
			}
		});
	})
	/*$(document).on("change","#" + {$form["username"]->htmlId},function () {
		$.ajax({
			method: "get",
			url: {link ValidateUsername!},
			data: {
				username: $(this).val()
			},
			success: function(resp) {
				$.nette.ext("snippets").updateSnippets(resp.snippets);
			}
		});
	})*/

	function validateControl(domid) {

	}

</script>