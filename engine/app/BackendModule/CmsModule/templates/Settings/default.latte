{var $title="Nastavenia CMS"}

{block content}

    <div class="card bd-0">
        {include "../@submenu.latte"}

        <div class="card-body">

            {*include "@settingsMenu.latte"*}

            {*include "@supplierForm.latte", isEdit: true*}
            {form cmsLayoutForm, class=>"ajax"}
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="form-label-group">
                                {input name}
                                {label name /}
                            </div>
                            <div class="form-label-group">
                                {input file}
                                {label file /}
                            </div>
                            <div class="form-label-group">
                                {input domain}
                                {label domain /}
                            </div>
                            <div class="form-label-group">
                                {input langs}
                                {label langs /}
                            </div>
                            <div class="form-group d-flex align-items-center" n:if="$presenter->isSuperSuperadmin() || $presenter->isOwner()">
                                <div id="toggleOn" class="br-toggle br-toggle-success {if $layout->active}on{/if}" data-handler="{link toggleOn!}">
                                    <div class="br-toggle-switch"></div>
                                </div>
                                <div class="ml-2">Web je aktívny / vypnutý</div>
                            </div>
                            <div class="form-group d-flex align-items-center" n:if="$presenter->isSuperSuperadmin() || $presenter->isOwner()">
                                <div id="toggleDebug" class="br-toggle br-toggle-warning {if $layout->debug}on{/if}" data-handler="{link toggleDebug!}">
                                    <div class="br-toggle-switch"></div>
                                </div>
                                <div class="ml-2">Vývojarský mód - zobrazuje sa len superadminom a majiteľom licencie</div>
                            </div>

                        </div>
                        <div class="col-lg-4">
                            <div class="form-label-group">
                                {input title}
                            {label title /}

                            </div>
                            <div class="form-label-group">
                                {input company_name}
                            {label company_name /}

                            </div>

                            <div class="form-label-group">
                                {input cookieconsent_email, class:"form-control"}
                            {label cookieconsent_email /}

                            </div>
                            <div class="form-label-group">
                                {input google_analytics, class:"form-control"}
                            {label google_analytics /}
                            </div>
                            <div class="form-label-group">
                                {input recaptcha_site_key, class:"form-control"}
                                {label recaptcha_site_key /}
                            </div>
                            <div class="form-label-group">
                                {input recaptcha_secret_key, class:"form-control"}
                                {label recaptcha_secret_key /}
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="form-group">
                                {label logo /}
                                <div>{input logo}</div>
                            </div>
                            <div class="form-group">
                                {label favicon /}
                                <div>{input favicon}</div>
                            </div>
                            <div class="form-group">
                                {label apple_touch_icon /}
                                <div>{input apple_touch_icon}</div>
                            </div>

                            <div n:snippet="logo" class="d-flex">
                                <div n:if="isset($layout) && $layout->logo !== null" class="mb-3 p-3 border">
                                    <div><span class="badge badge-primary tx-14">Logo webu</span></div>
                                    <img src="{$layout->logo}" class="img-fluid" style="max-width: 350px">
                                    <div>
                                        <a n:href="deleteLogo!" class="ajax text-danger" data-datagrid-confirm="Naozaj?">Vymazať logo</a>
                                    </div>
                                </div>
                                <div n:if="isset($layout) && $layout->favicon !== null" class="mb-3 p-3 border">
                                    <div><span class="badge badge-primary tx-14">Favicon webu</span></div>
                                    <img src="{$layout->favicon}" class="img-fluid" style="max-width: 350px">
                                    <div>
                                        <a n:href="deleteFavicon!" class="ajax text-danger" data-datagrid-confirm="Naozaj?">Vymazať favicon</a>
                                    </div>
                                </div>
                                <div n:if="isset($layout) && $layout->apple_touch_icon !== null" class="mb-3 p-3 border">
                                    <div><span class="badge badge-primary tx-14">Apple touch</span></div>
                                    <img src="{$layout->apple_touch_icon}" class="img-fluid" style="max-width: 350px">
                                    <div>
                                        <a n:href="deleteAppleTouch!" class="ajax text-danger" data-datagrid-confirm="Naozaj?">Vymazať Apple touch</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                        <div class="form-group">
                            {label html_font /}
                            {input html_font}
                        </div>
                        <div class="form-group">
                            {label html_head /}
                            {input html_head}
                        </div>
                        <div class="form-group">
                            {label stylesheet /}
                            {input stylesheet}
                        </div>
                        <div class="form-group">
                            {label javascript /}
                            {input javascript}
                        </div>



                                {input save, class=>"btn btn-primary btn-p"}
                                <button type="button" class="btn btn-danger" data-dismiss="modal">{_"Zavrieť"}</button>



            {/form}

        </div>
    </div><!-- card -->



{/block}

{block scripts}
