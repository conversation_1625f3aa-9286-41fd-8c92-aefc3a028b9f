<?php

namespace BackendModule\InvoiceModule\SettingModule;


use BackendModule\InvoiceModule\BasePresenter;
use Kubomikita\Attributes\Description;
use Nette\Http\FileUpload;
use Nette\Utils\ArrayHash;
use Nette\Utils\Image;
use Webtec\UI\Forms\Form;

#[Description("Nastavenia | Logo a podpis")]
class LogoPresenter extends BasePresenter {


	public function handleDeleteLogo(){
		$this->supplier->update(["logo" => null]);
		$this->template->supplier = $this->supplier;
		$this->redrawControl("logo");
	}

	public function handleDeleteSignature(){
		$this->supplier->update(["signature" => null]);
		$this->template->supplier = $this->supplier;
		$this->redrawControl("signature");
	}

	public function createComponentImageForm($name) : Form
	{
		$f = $this->formFactory->create($this, $name);
		$f->addUpload('logo',"Logo")->addCondition($f::FILLED)->addRule($f::IMAGE, "Vyberte obrázok.");
		$f->addUpload('signature', "Podpis")->addCondition($f::FILLED)->addRule($f::IMAGE, "Vyberte obrázok.");

		$f->addSubmit("save", "Uložiť");

		$f->onSuccess[] = function (Form $form, ArrayHash $values){
			bdump($values);
			/** @var FileUpload $logo */
			$logo = $values->logo;
			if($logo->isOk() && $logo->isImage()){
				//bdump();
				$image = $logo->toImage();
				$image->resize(300, null, Image::SHRINK_ONLY | Image::FIT);

				$imageType = Image::extensionToType($logo->getImageFileExtension());
				if($imageType === Image::PNG){
					$background = Image::rgb( 255, 255, 255, 127 );
				} else {
					$background = Image::rgb( 255, 255, 255 );
				}

				$im = Image::fromBlank($image->getWidth(), $image->getHeight(), $background);
				$im->place($image);

				$this->supplier->update(["logo" => base64_encode($im->toString())]);
				$this->template->supplier = $this->supplier;
				$this->redrawControl("logo");
			}
			$signature = $values->signature;
			if($signature->isOk() && $signature->isImage()){
				//bdump();
				$image = $signature->toImage();
				$image->resize(300, null, Image::SHRINK_ONLY | Image::FIT);

				$imageType = Image::extensionToType($signature->getImageFileExtension());
				if($imageType === Image::PNG){
					$background = Image::rgb( 255, 255, 255, 127 );
				} else {
					$background = Image::rgb( 255, 255, 255 );
				}

				$im = Image::fromBlank($image->getWidth(), $image->getHeight(), $background);
				$im->place($image);

				$this->supplier->update(["signature" => base64_encode($im->toString())]);
				$this->template->supplier = $this->supplier;
				$this->redrawControl("signature");
			}


		};

		return $f;
	}
}