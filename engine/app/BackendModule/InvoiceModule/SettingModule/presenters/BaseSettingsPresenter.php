<?php

namespace BackendModule\InvoiceModule\SettingModule;


use BackendModule\InvoiceModule\BasePresenter;
use IPub\FlashMessages\Entities\Message;
use Kubomikita\Attributes\Description;
use <PERSON>bomi<PERSON>ta\Utils\Strings;
use Nette\DI\Attributes\Inject;
use Nette\Utils\ArrayHash;
use Nette\Utils\Validators;
use Webtec\Models\DocumentCompanyModel;
use Webtec\Models\DocumentModel;
use Webtec\Models\UserModel;
use Webtec\UI\Forms\Form;

#[Description("Nastavenia | Základné nastavenia")]
class BaseSettingsPresenter extends BasePresenter {
	#[Inject]
	public DocumentCompanyModel $documentCompanyModel;
	public function actionDefault(){
		$defaultUsers = [];
		foreach ($this->invoiceSupplierUserModel->findBy(["invoice_supplier" => $this->supplierId]) as $supplierUser){
			$defaultUsers[$supplierUser->user] = UserModel::email($supplierUser->ref("user"));
		}
		$this["baseForm"]["users"]->setItems($defaultUsers);
		$this["baseForm"]->setDefaults($this->supplier->toArray() + ["users" => array_keys($defaultUsers)]);

	}
	public function createComponentBaseForm($name) : Form
	{
		$f = $this->formFactory->create($this,$name);
		$f->addText("default_payment_days","Predvolená splatnosť faktúr (dni)")->setDefaultValue(14);
		$f->addText("default_constant","Predvolený KS")->setNullable();

		$f->addSearchSelect("default_accounting_account", "Predvolený syntetický účet (faktúry)", DocumentModel::ACCOUNTING_ACCOUNT["Výnosy"])->setPrompt("-- vyberte --");
		$f->addSearchSelect("default_accounting_analytic_account","Predvolený analytický účet (faktúry)", DocumentModel::ANALYTIC_ACCOUNT)->setPrompt("-- vyberte --");

		$f->addSearchSelect("default_accounting_account_expenses", "Predvolený syntetický účet (náklady)", DocumentModel::ACCOUNTING_ACCOUNT["Náklady"])->setPrompt("-- vyberte --");
		$f->addSearchSelect("default_accounting_analytic_account_expenses","Predvolený analytický účet (náklady)", DocumentModel::ANALYTIC_ACCOUNT)->setPrompt("-- vyberte --");

		$f->addSelect("default_payment_type","Predvolený spôsob úhrady", [null => '-- žiadny --'] + $this->invoicePaymentTypeModel->findAll()->whereOr(["invoice_supplier" => $this->supplierId, "invoice_supplier IS NULL"])->fetchPairs("id", "name"))->setHtmlAttribute("class","form-control");//->setPrompt('-- žiadny --');
		$f->addCheckbox("default_prices_on_delivery","Zobrazovať ceny na dodacích listoch");
		$f->addSelect("document_company", "Synchronizovať s firmou v elektronickom spise", $this->documentCompanyModel->findBy(["user" => $this->allowedUsers])->fetchPairs("id", "name"))->setHtmlAttribute("class","form-control")->setPrompt("-- žiadna --");

		$f->addText('default_rounding_precision_item', 'Zaokrúhľovanie - jednotkovej ceny na dokladoch (počet des. miest)')->setHtmlType("number")->setDefaultValue(4)->setRequired()->addCondition($f::Filled)->addRule($f::Range, 'Musí byť v rozsahu od 0 do 6', [0,6]);;
		$f->addText('default_rounding_precision_total', 'Zaokrúhľovanie - celkovej ceny na dokladoch (počet des. miest)')->setHtmlType("number")->setDefaultValue(2)->setRequired()->addCondition($f::Filled)->addRule($f::Range, 'Musí byť v rozsahu od 0 do 6', [0,6]);

		$f->addText('vat_default', 'Predvolená sadzba DPH')->setHtmlType("number")->setDefaultValue(20)->setRequired()->addCondition($f::Filled)->addRule($f::Range, 'Musí byť v rozsahu od 0 do 30', [0,30]);;

		$f->addCheckbox("proforma_to_regular", "Automaticky vytvoriť ostrú faktúru po úhrade zálohovej");
		$f->addCheckbox("proforma_to_regular_send","Po vytvorení automatickej faktúry zo zálohovej odoslať klientovi, ak má zadaný e-mail");//->addConditionOn($f["a"]);
		$f->addCheckbox("show_rates", "Zobraziť pri FA úrok z omeškania vypočítaný na základe úrok. sazdby ECB");

		$f->addCheckbox("invoice_to_expense", "Automaticky vytvoriť náklad odberateľovi po vystavení FA");
		/*$f->addCheckbox("menu_proforma", "Zobraziť zálohové faktúry");
		$f->addCheckbox("menu_delivery", "Zobraziť dodacie listy");
		$f->addCheckbox("menu_cancel", "Zobraziť dobropisy");*/

		foreach ($this->invoiceTypeModel->findBy(["id >" => 1])->order("ordr") as $invoiceType){
			//bdump($invoiceType);
			if( isset($this->supplier->toArray()['menu_'.str_replace("-","_",$invoiceType->key)])) {
				$f->addCheckbox("menu_" . str_replace("-","_",$invoiceType->key), "Zobraziť " . Strings::firstLower($invoiceType->group));
			}
		}

		/*$f->addAjaxSearchMultiSelect("users","Užívatelia s prístupom", ["model" => "user",
		                                                                "where" => [
			                                                                "user.type" => [UserModel::TYPE_COWORKER,UserModel::TYPE_SPECIALIST]
		                                                                ],
		                                                                "columns" => [
																			"user.username"
		                                                                ],

																		"render" => [UserModel::class, "email"]
		]);*/
		$f->addMultiSelect("users", "Užívatelia s prístupom");

		$f->addSubmit("save", "Uložiť");

		$f->onValidate[] = function (Form $form) {

			//if
		};

		$f->onSuccess[] = function (Form $form, ArrayHash $values){

			$errors = []; $addedUsers = [];
			$users = $form->getHttpData()["users"];
			//bdump($users);
			foreach ($users as $user){
				if(!is_numeric($user) && is_string($user)){
					bdump($user);
					if(!Validators::isEmail($user)){
						$errors[] = sprintf('"<strong>%s</strong>" nieje platný e-mail.', $user);
						continue;
					}
					$exists = $this->context->getByType(UserModel::class)->findBy(["username" => $user])->fetch();
					if($exists === null){
						$errors[] = sprintf('Užívateľ s e-mailom "<strong>%s</strong>" neexistuje.', $user);
						continue;
					}
					$addedUsers[] = $exists->id;
				}
			}

			if(!empty($errors)){
				foreach ($errors as $error) {
					$this->flashMessage($error, Message::LEVEL_ERROR, "Užívatelia s prístupom");
				}
				//$form->addError(implode(", ",$errors));

			}

			$users = $values->users;
			$users = array_merge($users, $addedUsers);
			//exit;

			$this->supplier->related("invoice_supplier_user")->delete();
			$userInsert = [];
			foreach ($users as $userId){
				$userInsert[] = ["invoice_supplier" => $this->supplierId, "user" => $userId];
			}
			if(!empty($userInsert)){
				$this->invoiceSupplierUserModel->insert($userInsert);
			}

			unset($values->users);

			$this->supplier->update((array) $values);
			$this->flashMessage("Nastavenia boli uložené.", Message::LEVEL_SUCCESS);
			$this->template->supplier = $this->supplier;
			$this->redrawControl("submenu");
		};

		return $f;
	}
}