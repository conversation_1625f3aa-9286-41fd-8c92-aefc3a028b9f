<?php

namespace BackendModule\InvoiceModule\SettingModule;


use App\Datagrid\DeliveryTypeGrid;
use App\Forms\ModalDeliveryTypeForm;
use BackendModule\InvoiceModule\BasePresenter;
use IPub\FlashMessages\Entities\Message;
use Kubomiki<PERSON>\Attributes\Description;
use Nette\DI\Attributes\Inject;
use Ublaboo\DataGrid\DataGrid;
use Webtec\UI\Forms\Form;

#[Description("Nastavenia | Spôsoby doručenia")]
class DeliveryTypePresenter extends BasePresenter {

	#[Inject]
	public DeliveryTypeGrid $deliveryTypeGrid;
	#[Inject]
	public ModalDeliveryTypeForm $modalDeliveryTypeForm;
	public function actionDefault(){
		$this["deliveryTypeGrid"]->setDataSource($this->invoiceDeliveryTypeModel->findAll()->whereOr(["invoice_supplier" => $this->supplierId, "invoice_supplier IS NULL"])->order("id ASC"));
	}
	public function handleDeleteDeliveryType(int $id){
		$this->invoiceDeliveryTypeModel->find($id)->delete();
		$this->flashMessage("Spôsob dodania bol vymazaný.", Message::LEVEL_ERROR);
		$this["deliveryTypeGrid"]->reload();
	}

	public function createComponentDeliveryTypeGrid($name) : DataGrid
	{
		$g = $this->deliveryTypeGrid->setSupplierId($this->supplierId);
		return $g->create($this, $name);
	}

	public function createComponentModalDeliveryTypeForm($name) : Form
	{
		$f = $this->modalDeliveryTypeForm->create($this, $name);
		$f->onSuccess[] = function (){
			$this->flashMessage("Spôsob dodania bol uložený.", Message::LEVEL_SUCCESS);
			$this["deliveryTypeGrid"]->reload();
			$this->payload->closeModal = 1;
		};
		return $f;
	}
}