{var $title="Ćíselníky - Nastavenia faktúr"}

{block content}

<div class="card bd-0 invoice-container">
    {include "../@submenu.latte"}

    <div class="card-body">

            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                Po<PERSON>, prida<PERSON> (zmena) číselníka sa odporúča iba na začiatku zúčtovacieho obdobia (začiatku roka), bez vytvorených dokladov.
        </div>
            {control sequenceGrid}
    </div>
</div><!-- card -->


<div id="modalSequence" class="modal fade" aria-hidden="true" style="display: none;">
    {form modalSequenceForm, class=>"ajax"}
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content bd-0 tx-14">
            <div class="modal-header bg-gray-400 pd-y-20 pd-x-25">
                <h6 class="tx-14 mg-b-0 tx-inverse tx-bold">
                    Pridať číselný rad
                </h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body pd-25">
                <div class="form-label-group">
                    {input name}
                    {label name /}
                </div>
                <div class="form-label-group">
                    {input document_name}
                    {label document_name /}
                </div>
                <div class="form-label-group">
                    {input invoice_type}
                    {label invoice_type /}
                </div>
                <div class="form-label-group mb-0">
                    {input mask}
                    {label mask /}

                </div>
                <div class="mb-3 tx-11 mt-1">
                    <p>Tieto znaky budú nahradené skutočnými hodnotami:<br>
                        <strong>RRRR</strong> - Rok 4 číslice<br>
                        <strong>RR</strong> - Rok 2 číslice<br>
                        <strong>MM</strong> - Mesiac<br>
                        <strong>DD</strong> - Deň<br>
                        <strong>C</strong> - Č. faktúry
                        <br>
                        Príklad: <strong>FA-RRRR/CCCC</strong>
                    </p>
                    <p>
                        Znaky ohraničené # nebudú nahradené hodnotami.<br>
                        Príklad: #MM# - RRRR/CCCC = MM - RRRR/CCCC
                    </p>
                </div>
                <div class="form-label-group">
                    {input renew}
                    {label renew /}
                    <div class="tx-danger tx-11 mt-1"><strong>Pozor:</strong><br> Vybraté obdobie musí byť zahrnuté vo formáte číselneho radu, inak nebude fungovať správne.</div>
                </div>
                {*<div class="form-label-group">
                    {input valid}
                    {label valid /}
                </div>*}
            </div>
            <div class="modal-footer d-block">
                <div class="row no-gutters">
                    <div class="col-12 text-center text-lg-right">
                        {input save, class=>"btn btn-primary btn-p"}
                        <button type="button" class="btn btn-danger" data-dismiss="modal">{_"Zavrieť"}</button>
                    </div>
                </div>

            </div>
        </div>
    </div>
    {/form}
</div>
{/block}