<?php

namespace App\Datagrid;

use App\Latte\CurrencyFilter;
use App\ORM\Entity\InvoiceSupplierBankStatement;
use App\ORM\Entity\InvoiceSupplierIban;
use Kubomikita\Factory\DataGridFactory;
use <PERSON><PERSON>miki<PERSON>\Factory\IDatagridItemFactory;
use <PERSON><PERSON>mi<PERSON>ta\FileManager\FileManager;
use Kubomikita\Utils\Iban;
use Nette\ComponentModel\IContainer;
use Nette\Database\Table\ActiveRow;
use Nette\Utils\Html;
use Nette\Utils\Json;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\InvoiceClientModel;
use Webtec\Models\InvoiceModel;
use Webtec\Models\ModelFactory;

class StatementGrid implements IDatagridItemFactory
{

	protected ?int $supplierId = null;

	public function __construct(
		protected DataGridFactory $dataGridFactory,
		protected FileManager $fileManager,
	) {

	}
	public function create(IContainer $presenter = null, string $name = null): DataGrid
	{
		$d = $this->dataGridFactory->create($presenter, $name);
		//$d->addColumnNumber("id", "ID");
		$d->addColumnDateTime("datetime", "Dátum a čas")->setFormat("d.m.Y H:i")->addCellAttributes(["width" => 150]);
		$d->addColumnText("invoice_supplier_iban","Bankový účet")->setRenderer(fn(InvoiceSupplierBankStatement $entity) => $entity->invoice_supplier_iban->name." [".Iban::maskIban($entity->invoice_supplier_iban->iban)."]");
		$d->addColumnText("name","Názov súboru")->setRenderer(function (InvoiceSupplierBankStatement $statement){
			$res = [];
			$res[] = '<a href="'.$statement->downloadLink($this->fileManager).'">';
			if($statement->ext === "pdf"){
				$res[] = '<i class="fas fa-file-pdf text-danger"></i> ';
			} else {
				$res[] = '<i class="fas fa-file"></i> ';
			}

			$res[] = $statement->name;
			$res[] = '</a>';

			return implode("", $res);
		})->setTemplateEscaping(false);

		$d->addModalToolbarButton("this","Nahrať","#modalBankStatement","modalBankStatementForm")->setIcon("plus")->setClass("btn-success btn-sm");
		$d->addAction("delete","","deleteStatement!")->setClass("btn btn-danger ajax btn-xs")->setIcon("times")->setConfirmation(new StringConfirmation("Naozaj?"));
		return $d;
	}
}