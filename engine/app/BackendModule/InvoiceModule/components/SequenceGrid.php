<?php

namespace App\Datagrid;

use Kubomikita\Factory\DataGridFactory;
use Kubomikita\Factory\IDatagridItemFactory;
use Nette\ComponentModel\IContainer;
use Nette\Database\Table\ActiveRow;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\InvoiceSupplierModel;
use Webtec\Models\ModelFactory;

class SequenceGrid implements IDatagridItemFactory
{


	public function __construct(protected DataGridFactory $dataGridFactory, protected ModelFactory $modelFactory, protected InvoiceSupplierModel $invoiceSupplierModel)
	{

	}

	public function create(IContainer $presenter = null, string $name = null, ?int $supplierId = null): DataGrid
	{

		$activeSequences = $this->invoiceSupplierModel->getSequences($supplierId);

		$d = $this->dataGridFactory->create($presenter,$name);
		$d->addColumnNumber("id","ID");
		$d->addColumnText("name", "Názov");
		$d->addColumnText("document_name", "Vlastný názov dokladu");
		$d->addColumnText("invoice_type.name","Typ dokladu")->setRenderer(function (ActiveRow $item){
			if($item->invoice_type !== null){
				return $item->ref("invoice_type")->name;
			} elseif ($item->expense){
				return 'Náklad';
			} elseif ($item->cash_register_income){
				return 'Prijaté pokladničné doklady';
			} elseif ($item->cash_register_expense){
				return 'Vydané pokladničné doklady';
			}
		});
		$d->addColumnText("mask","Formát");
		//$d->addColumnText("")
		$d->addColumnText("renew_monthly", "Obdobie")->setRenderer(function (ActiveRow $item){
			if($item->renew_monthly){
				return "Mesačne";
			} elseif ($item->renew_yearly){
				return "Ročne";
			} elseif ($item->renew_noreset){
				return "Neresetovať";
			}
		});
		/*$d->addColumnDateTime("valid_from","Platný od");
		$d->addColumnText("active","Stav")->setAlign("center")->setRenderer(function ($item) use($activeSequences){
			return $activeSequences[$item->invoice_type]->id === $item->id ? '<span class="tx-bold tx-success">Aktívny</span>' : '<span class="tx-danger">Neaktívny</span>';
		})->setTemplateEscaping(false);
*/


		$d->addAction("default","","defaultSequence!")->setIcon("star sharp")->setClass("btn btn-sm btn-warning ajax")->setDataAttribute("toggle","tooltip")->setTitle("Vypnúť predvolený");
		$d->addAction("undefault","","defaultSequence!")->setIcon("star")->setClass("btn btn-sm btn-outline-warning ajax")->setDataAttribute("toggle","tooltip")->setTitle("Predvolený");

		//$d->addAction("active","","activeSequence!")->setIcon("eye")->setClass("btn btn-sm btn-outline-success ajax")->setDataAttribute("toggle","tooltip")->setTitle("Aktívovať");
		//$d->addAction("deactive","","activeSequence!")->setIcon("eye-slash")->setClass("btn btn-sm btn-outline-danger ajax")->setDataAttribute("toggle","tooltip")->setTitle("Deaktívovať");

		$d->addAction("delete", "","deleteSequence!")->setClass("btn btn-sm btn-danger ajax")->setIcon("times")->setConfirmation(new StringConfirmation("Naozaj ?"));

		$d->allowRowsAction("delete", function (ActiveRow $item){
			return $item->invoice_supplier !== null && $item->related("invoice")->count("id") === 0;
		});

		//$d->allowRowsAction("active", fn($row) => !$row->active && $row->invoice_supplier !== null);
		//$d->allowRowsAction("deactive", fn($row) => !!$row->active && $row->invoice_supplier !== null);
		$d->allowRowsAction("default", fn($row) => $row->default && $row->invoice_supplier !== null);
		$d->allowRowsAction("undefault", fn($row) => !$row->default && $row->invoice_supplier !== null);
		//if(!$this->invoiceSupplierModel->hasInvoices($supplierId, true)){
			$d->addModalToolbarButton("this","Pridať","#modalSequence","modalSequenceForm")->setIcon("plus")->setClass("btn btn-success btn-sm");
		//}

		$d->setAclConditionCallback(function (){
			return true;
		});

		return $d;
	}
}