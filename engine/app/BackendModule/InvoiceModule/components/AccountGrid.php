<?php

namespace App\Datagrid;

use App\Latte\CurrencyFilter;
use Kubomikita\Factory\DataGridFactory;
use Kubomikita\Factory\IDatagridItemFactory;
use Nette\ComponentModel\IContainer;
use Nette\Database\Table\ActiveRow;
use Nette\Utils\Html;
use Nette\Utils\Json;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\InvoiceClientModel;
use Webtec\Models\InvoiceModel;
use Webtec\Models\ModelFactory;

class AccountGrid implements IDatagridItemFactory
{

	protected ?int $supplierId = null;

	public function __construct(
		protected DataGridFactory $dataGridFactory,
		protected InvoiceClientModel $invoiceClientModel
	) {

	}

	public function create(IContainer $presenter = null, string $name = null): DataGrid
	{
		$d = $this->dataGridFactory->create($presenter, $name);
		//$d->addColumnNumber("id", "ID");
		$d->addColumnDateTime("date","Dátum a čas")->setFormat("d.m.Y H:i")->addCellAttributes(["width" => 150]);
		$d->addColumnText("volume","Suma")->setAlign("right")->setTemplateEscaping(false)->setRenderer(function (ActiveRow $item) use($presenter){
			$d = '<span class="tx-bold tx-success">%s</span>';
			if($item->volume < 0){
				$d = '<span class="tx-bold tx-danger">%s</span>';
			}
			$el = "";
			if($item->bmail !== null){
				$el = Html::el("a")->addAttributes([
					"href" => "javascript:;",
					"class"=> "modal-open tx-warning",
					"title" => "Nahraté z prijatého e-mailu.",
					//"data-title" => "Detail e-mailovej správy: ".$item->expense_name,
					"data-toggle" => "modal",
					"data-target" => "#modalPaymentBmailDetail",
					"data-content" => Json::encode(["subject" => $item->ref("bmail")->subject, "message" => $item->ref("bmail")->message])

				])->addHtml('<i class="fas fa-info-circle"></i> ');
				$el = '<div class="float-left">'.$el.'</div>';
			}

			return $el . sprintf($d, (new CurrencyFilter())($item->volume));
		})->addCellAttributes(["width" => 150]);
		$d->addColumnText("variableSymbol", "VS")->setFilterText();
		$d->addColumnText("iban", "Účet")->setRenderer(function (ActiveRow $item){
			$name = $item->ref("invoice_supplier_iban")?->name;
			$iban = $item->ref("invoice_supplier_iban")?->iban;
			$desc = $item->ref("invoice_supplier_iban")?->desc;
			if($name !== null && $iban !== null){
				return $name."<div class='tx-13'> **** ".substr($iban,-4) .($desc !== null ? ' <span class="tx-black tx-12">['.$desc."]</span>" : '')."</div>";
			}
			return chunk_split($item->iban, 4, ' ').'<div class="tx-danger tx-11 tx-bold">Neznámy (nepridaný) bankový účet.</div>';
		})->setTemplateEscaping(false);
		$d->addColumnText("note", "Poznámka")->setRenderer(function ($item){
			return $item->note ." ". $item->messageTo;
		});

		$d->addColumnText("doklad","Doklad")->setRenderer(function (ActiveRow $item) use ($presenter){
			if($item->invoice!== null){
				return '<a href="'.$presenter->link(":Backend:Invoice:Invoice:detail", ["id" => $item->invoice]).'" target="_blank"><i class="fas fa-file-pdf"></i> '.InvoiceModel::name($item->ref("invoice")).'</a>';
			} elseif($item->document!== null){
				return '<a href="'.$presenter->link(":Backend:Invoice:Expense:detail", ["id" => $item->document]).'" target="_blank"><i class="fas fa-file-pdf"></i> '.$item->ref("document")->expense_name.'</a>';
			} else {
				//if($item->volume > 0) {
					return Html::el("a")->addAttributes([
						"href" => "javascript:;",
						"class"=> "modal-open btn btn-outline-secondary btn-xs",
						"title" => "Nahraté z prijatého e-mailu.",
						//"data-title" => "Detail e-mailovej správy: ".$item->expense_name,
						"data-toggle" => "modal",
						"data-target" => "#modalInvoicePaymentAdd",
						"data-form" => "modalInvoicePaymentAddForm",
						"data-content" => Json::encode(["invoices" => $presenter->template->spinnerElement]),
						"data-onafter" => "handleInvoicePaymentAddLoad",
						"data-pv" => Json::encode($item->toArray()),
					])->addHtml('Pridať');

						//'<a href="#" class="btn btn-outline-secondary btn-xs"></a>';
				/*} elseif ($item->volume < 0){

				}*/
			}
			//dump($item);
		})->setTemplateEscaping(false);

		//$d->addModalAction("this", "Párovať s faktúrou", "#modalInvoicePair", "modalInvoicePairForm")->setClass("btn btn-xs btn-primary");



		return $d;
	}
}