<?php

namespace BackendModule\InvoiceModule;


use App\Datagrid\InvoiceGrid;
use App\Forms\ModalInvoiceSendForm;
use App\Forms\ModalInvoiceSequenceForm;
use App\Forms\ModalInvoiceSupplierEditForm;
use App\Latte\CurrencyFilter;
use App\ORM\Entity\Document;
use App\ORM\Entity\DocumentCompany;
use App\ORM\Entity\Invoice;
use App\ORM\Repository\InvoiceRepository;
use App\PdfResponse;
use Contributte\FormMultiplier\Multiplier;
use Contributte\FormMultiplier\Submitter;
use GuzzleHttp\Client;
use IPub\FlashMessages\Entities\Message;
use Kubomikita\Attributes\Description;
use Kubomikita\Events\InvoiceEvents;
use Kubomikita\Utils\DateTime;
use Kubomikita\Utils\FileLocator;
use Kubomikita\Utils\InvoiceSequenceVariable;
use Kubomikita\Utils\Strings;
use Latte\Runtime\Filters;
use Mpdf\Output\Destination;
use Nette\Application\Attributes\Persistent;
use Nette\Application\BadRequestException;
use Nette\Application\ForbiddenRequestException;
use Nette\Application\Responses\TextResponse;
use Nette\Database\Table\ActiveRow;
use Nette\Database\Table\Selection;
use Nette\DI\Attributes\Inject;
use Nette\Forms\Container;
use Nette\Forms\Controls\ChoiceControl;
use Nette\Forms\Controls\SelectBox;
use Nette\Forms\Controls\SubmitButton;
use Nette\Forms\Form as NetteForm;
use Nette\IOException;
use Nette\Utils\ArrayHash;
use Nette\Utils\Html;
use Nette\Utils\Image;
use Nette\Utils\Json;
use Ramsey\Uuid\Uuid;
use Tracy\Debugger;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\Column\Column;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\BmailModel;
use Webtec\Models\DocumentModel;
use Webtec\Models\InvoiceRateModel;
use Webtec\Models\InvoiceSupplierMailingTemplateModel;
use Webtec\Models\MailingTemplateModel;
use Webtec\Models\ModelFactory;
use Webtec\Models\UserModel;
use Webtec\UI\Forms\Form;
use function Clue\StreamFilter\fun;
#[Description("Faktúry")]
class InvoicePresenter extends BasePresenter {

	#[Inject]
	public DocumentModel $documentModel;
	#[Inject]
	public InvoiceGrid $invoiceGrid;
	#[Inject]
	public ModalInvoiceSequenceForm $modalInvoiceSequenceForm;
	#[Inject]
	public MailingTemplateModel $mailingTemplateModel;
	#[Inject]
	public ModalInvoiceSupplierEditForm $modalInvoiceSupplierEditForm;

	#[Inject]
	public InvoiceSupplierMailingTemplateModel $invoiceSupplierMailingTemplateModel;
	#[Inject]
	public InvoiceRateModel $invoiceRateModel;

	public FileLocator $invoiceFileLocator;
	public function startup()
	{
		parent::startup();
		$this->invoiceFileLocator = $this->context->getService("invoice.fileLocator");

		/** @var BmailModel $model */
		$model = $this->modelFactory->create('Bmail');
		foreach ($model->findBy(['id' => [26262, 26263,26264]]) as $bmail) {

			//bdump($bmail);
			//bdump($model->parsePrima($bmail->message));
		}


				//bdump($model->parsePrima());
	}

	public function actionRecurring() {

		/*$toReplace = ["number", "name", "variable", "note_before", "note_after"];

		$today = (new DateTime())->modifyClone("+3 week");

		foreach ($this->invoiceModel->findBy(["invoice_type" => $this->invoiceTypes["recurring"]->id, "recurring_frequency IS NOT NULL" , "recurring_date" => $today->format("Y-m-d")]) as $invoice){
			$isNotCreated = ($invoice->recurring_created === null || $invoice->recurring_created->format("Y-m-d") !== $today->format("Y-m-d"));
			if($isNotCreated) {
				try {
					$sequence = new InvoiceSequenceVariable($invoice->ref("invoice_sequence"), $today);
					$sequence->setLastSequence($this->invoiceModel->getLastSequence($this->supplierId, $sequence->id, $today));

					$this->invoiceModel->getDatabaseConnection()->transaction(function () use ($invoice, $sequence, $toReplace, $today) {


						$replaceData = [
							"number"    => $sequence->getNextSequence(),
							"variable"  => $sequence->toInteger(),
							"year"      => $today->format("Y"),
							"nextYear"  => $today->modifyClone("+1 year")->format("Y"),
							"lastYear"  => $today->modifyClone("-1 year")->format("Y"),
							"month"     => $today->format("m"),
							"nextMonth" => $today->modifyClone("+1 month")->format("m"),
							"lastMonth" => $today->modifyClone("-1 month")->format("m"),
							"day"       => $today->format("d"),
						];

						//dump($invoice, $sequence, $replaceData);
						$defaults                            = $invoice->toArray();
						unset($defaults["id"]);
						$defaults["invoice_type"]            = $this->invoiceTypes["regular"]->id;
						$defaults["recurring_date"]          = null;
						$defaults["recurring_frequency"]     = null;
						$defaults["recurring_infinity"]      = 0;
						$defaults["recurring_count"]         = null;
						$defaults["recurring_send"]          = 0;
						$defaults["recurring_email"]         = null;
						$defaults["recurring_date_delivery"] = null;
						$defaults["recurring_due_days"]      = null;
						$defaults["recurring_created"]       = null;

						$defaults["date_created"] = $today;

						if($invoice->recurring_date_delivery === "created"){
							$defaults["date_delivery"] = $today;
						}

						$defaults["date_due"] = $today->modifyClone("+".$invoice->recurring_due_days." day");



						$templates = [];
						foreach ($toReplace as $key) {
							$templates[$key] = $defaults[$key] ?? '';
						}
						$replacer = $this->invoiceModel->getReplacer($templates);
						foreach ($toReplace as $key) {
							$defaults[$key] = $replacer->renderToString($key, $replaceData);
						}

						$newInvoice = $this->invoiceModel->insert($defaults);

						$defaultsItems = [];
						foreach ($invoice->related("invoice_item") as $item) {
							$itemArray                = $item->toArray();
							unset($itemArray["id"]);
							$replacer                 = $this->invoiceModel->getReplacer([
								"name"        => $item->name ?? '',
								"description" => $item->description ?? ""
							]);
							$itemArray["invoice"] = $newInvoice->id;
							$itemArray["name"]        = $replacer->renderToString("name", $replaceData);
							$itemArray["description"] = $replacer->renderToString("description", $replaceData);
							$defaultsItems[]          = $itemArray;
						}
						$this->invoiceItemModel->insert($defaultsItems);
						$this->invoiceModel->toDocuments($newInvoice);

						//dump($defaults, $defaultsItems);

						$updateRecurring = ["recurring_created" => $today];
						if($invoice->recurring_frequency !== null){
							$updateRecurring["recurring_date"] = $today->modifyClone($invoice->recurring_frequency);
						}
						$invoice->update($updateRecurring);

						$inboxDataRecurring = [
							"invoice" => [
								"number" => $newInvoice->number,
								"client_name" => $newInvoice->client_name,
								"invoice_client" => $newInvoice->invoice_client,
								"id" => $newInvoice->id
							]
						];

						if($invoice->recurring_send && $invoice->recurring_email !== null){
							$emails = explode(",", $invoice->recurring_email);
							$emails = array_map("trim", $emails);

							$this->invoiceModel->sendToEmail($newInvoice, $emails);
							$inboxDataRecurring["emails"] = $invoice->recurring_email;

							$newInvoice->update(["sended" => new DateTime()]);

							$this->invoiceInboxModel->addInvoice($this->invoiceInboxModel::TYPE_RECURRING_INVOICE, $this->invoiceInboxModel::MESSAGE_RECURRING_INVOICE_EMAIL, $inboxDataRecurring, $newInvoice);
						} else {
							$this->invoiceInboxModel->addInvoice($this->invoiceInboxModel::TYPE_RECURRING_INVOICE, $this->invoiceInboxModel::MESSAGE_RECURRING_INVOICE, $inboxDataRecurring, $newInvoice);
						}
						echo "fa vytvorena<br>";

					});
				} catch (\Throwable $e){
					$inboxDataRecurring = [
						"invoice" => [
							"number" => $invoice->number,
							"client_name" => $invoice->client_name,
							"invoice_client" => $invoice->invoice_client,
							"id" => $invoice->id,
							"invoice_type" => $invoice->invoice_type
						]
					];
					$this->invoiceInboxModel->addInvoice($this->invoiceInboxModel::TYPE_RECURRING_INVOICE, $this->invoiceInboxModel::MESSAGE_RECURRING_INVOICE_ERROR, $inboxDataRecurring, $invoice);
					dump($e);
				}
			}
		}
*/

		exit;
	}



	public function actionDefault(string $invoiceType = 'regular', ?int $year = null, ?int $invoiceSequenceId = null) {
		$this->invoiceType = $invoiceType;

		$this->template->title = $this->selectedInvoiceType->group;

		$year = $year ?? $this->getSession($this->invoiceSessionNamespace)->get("year");
		$years = $this->invoiceModel->findBy(["invoice_supplier" => $this->supplierId,"invoice_type" => $this->selectedInvoiceType->id])->select("DISTINCT(YEAR(date_created)) AS year")->order("year DESC")->fetchPairs("year","year") + [date("Y") => (int) date("Y")];
		krsort($years);
		$years[0] = "Všetky";
		//dumpe($years, [null => "test"]);
		$this->template->years = $years;
		$this->template->year = $year;

		$this->template->selections = [];
		$selection = $this->getSession($this->invoiceSessionNamespace)->get("selection") ?? 'all';
		$this->template->selection = $this->selection = $selection;

		$sequenceId = $this->getSession($this->invoiceSessionNamespace)->get("sequenceId") ?? null;
		$this->template->sequenceId = $this->sequenceId = $sequenceId;
		$this->template->showCreateButton = true;
		$this->template->createButtonInfoText = null;

		if($this->selectedInvoiceType->hasInvoiceSequence && $this->selectedInvoiceType->hasInvoiceSequenceCreate) {
			$createdSequences = $this->invoiceModel->findBy([
				"invoice.invoice_supplier" => $this->supplierId,
				"invoice.invoice_type"     => $this->selectedInvoiceType->id,
				"invoice_sequence IS NOT NULL"
			])->select("invoice.invoice_sequence, invoice_sequence.name, invoice_sequence.mask, COUNT(invoice.id) AS invoice_count")->group("invoice.invoice_sequence")->fetchAssoc("invoice_sequence");
			//bdump($createdSequences);
			if(count($createdSequences) > 1) {
				$this->template->sequences = [0 => ["name" => "Všetky", "mask" => "Všetky"]] + $createdSequences;
			} else {
				if(!isset($createdSequences[$this->sequenceId])){
					$this->sequenceId = null;
					$this->getSession($this->invoiceSessionNamespace)->set("sequenceId", $this->sequenceId);
				}
			}
			//bdump($this->sequenceId);
			//bdump($createdSequences);
			//dumpe($this->template->sequences);
		}

		if($this->selectedInvoiceType->hasTypeFilter) {
			$this->template->selections = [
				"all"    => "Všetky",
				"paid"   => "Uhradené",
				"unpaid" => "Neuhradené",
				"due"    => "Po splatnosti",
			];
			$this->template->selection  = $this->getSession($this->invoiceSessionNamespace)->get("selection");
		}

		if($this->invoiceType === "cancel"){
			$this["invoiceGrid"]->removeToolbarButton("add");
			$this->template->showCreateButton = false;
			$this->template->createButtonInfoText = 'Dobropis je možné vystaviť len ku konkrétnej faktúre. Preto prejdite do "Faktúry" - "Vydané faktúry", ďalej kliknite na "Ceruzku", ktorú faktúru chcete dobropisovať a v detaile faktúry kliknite na "Vytvoriť" - "Dobropis".';
		}

		if(!$this->selectedInvoiceType->hasDue){
			$this["invoiceGrid"]->getColumn("date_created")->setName("Vytvorené")->setRenderer(function (ActiveRow $item){
				return $item->date_created->format("d.m.Y");
			});
		}
		if(!$this->selectedInvoiceType->hasPayment){
			$this["invoiceGrid"]->removeAction("payment");
		}

		if($this->invoiceType === "draft"){
			$this["invoiceGrid"]->removeColumn("number");
		}
		if(str_contains($this->selectedInvoiceType->key, "recurring")){
			$year = 0;
			$this->template->sequence = null;
			$this->template->years = null;
			//bdump();
			$this["invoiceGrid"]->getAction("detail")->setHref("edit");
			$this["invoiceGrid"]->removeColumn("number");
			$this["invoiceGrid"]->removeColumn("date_created");
			//$this["invoiceGrid"]->removeAction("detail");
			$this["invoiceGrid"]->removeAction("print");
			$this["invoiceGrid"]->removeAction("payment");
			$this["invoiceGrid"]->removeAction("pdf");

			$this["invoiceGrid"]->addColumnText("recurring_frequency", "Opakovanie")->addCellAttributes(["width" => "150"])->setAlign("center")->setRenderer(fn($item) => $this->invoiceModel::RECURRING_FREQUENCY[$item->recurring_frequency] ?? 'Neopakovať');
			$this["invoiceGrid"]->addColumnDateTime("recurring_date","Nasledujúca")->addCellAttributes(["width" => "150"])->setAlign("center")->setRenderer(fn($item) => $item->recurring_frequency === null ? '-' : $item->recurring_date->format("d.m.Y"));
			$this["invoiceGrid"]->addColumnText("ibans","Bankové účty")->setRenderer(function ($item){
				$return = [];

				foreach ($item->related("invoice_iban") as $iban){
					$return[] = $iban->iban;
				}

				return implode(", ", $return);
			});
			$this["invoiceGrid"]->addColumnCallback("is_paid",function (Column $column, ActiveRow $item) {
				if($item->recurring_frequency !== null){
					$column->setColumnClass('bg-success');
					$column->getElementPrototype("td")->addAttributes(["title" => "Aktívna", "data-toggle"=>"tooltip"]);
				} else {
					$column->setColumnClass('bg-dark');
					$column->getElementPrototype("td")->addAttributes(["title" => "Neaktívna", "data-toggle"=>"tooltip"]);
				}
			});

		}

		$this["invoiceGrid"]->setDataSource($this->getSelection($this->selection, $year ?? $this->year, $this->sequenceId));

		if($this->selectedInvoiceType->hasInvoiceSequence){
			//$this->template->missingNumbers = $this->invoiceModel->getMissingVariables($this->supplierId, $this->selectedInvoiceType->id);
		}





	}
	public function actionAdd(?int $invoiceId = null, ?bool $pair = null){
		$this->template->title = "Vytvorenie nového dokladu - ".$this->invoiceTypes[$this->invoiceType]->name;
		if($this->selectedInvoiceType->key === 'cancel' && $invoiceId === null) {
			throw new ForbiddenRequestException('Samostatný dobropis nieje možné vytvoriť.');
		}

		if($invoiceId !== null){
			$invoice = $this->invoiceModel->find($invoiceId)->fetch();
			if ( ! $invoice) {
				$this->flashMessage("Neexistujúca faktúra.");
				$this->redirect("Invoice:default");
			}

			$items = [];
			foreach ($invoice->related("invoice_item") as $itemRow){
				$item = $itemRow->toArray();
				unset($item->invoice);
				$item["price"] = sprintf("%.2F", $item["price"]);
				$item["unit_price"] = sprintf("%.4F", $item["unit_price"]);
				$items[] = $item;
			}

			$invoiceDefault = $invoice->toArray();

			unset($invoiceDefault["id"],
				$invoiceDefault["date_created"],
				$invoiceDefault["date_delivery"],
				$invoiceDefault["date_due"],
				$invoiceDefault["due"],
				$invoiceDefault["number"],
				$invoiceDefault["variable"],
				$invoiceDefault["name"],
				$invoiceDefault["issued_by"],
				$invoiceDefault["issued_by_phone"],
				$invoiceDefault["issued_by_email"],
				$invoiceDefault["invoice_sequence"],

			);

			$invoiceCopyType = $invoice->ref("invoice_type")->key;
			// Vytvarame ostru FA zo zalohovej FA
			if($this->invoiceType === "regular" && $invoiceCopyType === "proforma"){
				$invoiceDefault["invoice_type"] = $this->invoiceTypes[$this->invoiceType];
				$payment = $invoice->related("invoice_payment")->order("id DESC")->limit(1)->fetch();
				$invoiceDefault["note_after"] = $invoiceDefault["note_after"] ?? '';
				$invoiceDefault["note_after"] .= "\n\n".'Uhradené zálohovou faktúrou '.$invoice->number.' dňa '.$payment->datetime->format("d.m.Y");
				$invoiceDefault["show_payment_deposit"] = 1;
				$invoiceDefault["show_payment_deposit_price"] = (float) $invoice->related("invoice_payment")->sum("volume");
				$invoiceDefault["pair"] = $invoiceDefault["proforma"] = $invoice->id;
				$invoiceDefault["name_info"] = null; //$invoice->ref("invoice_type")->name_info;
			}
			// Vytvarame ostru alebo zálohovu FA z dodacieho listu
			if(($this->invoiceType === "regular" || $this->invoiceType === "proforma") && $invoiceCopyType === "delivery"){
				$invoiceDefault["invoice_type"] = $this->invoiceTypes[$this->invoiceType];
				$invoiceDefault["note_after"] = 'Faktúra bola vytvorená na základe dodacieho listu '.$invoice->number;
				$invoiceDefault["pair"] = $invoice->id;
				$invoiceDefault["name_info"] = null;
			}
			// Vytvarame ostru alebo zálohovu FA z cenovej ponuky
			if($invoiceCopyType === "estimate"){
				$invoiceDefault["invoice_type"] = $this->invoiceTypes[$this->invoiceType];
				$invoiceDefault["note_after"] = 'Na základe cenovej ponuky '.$invoice->number;
				$invoiceDefault["pair"] = $invoice->id;
				$invoiceDefault["name_info"] = null;
			}
			// Vytvarame dodaci list z ostrej faktury alebo zálohovej
			if($this->invoiceType === "delivery"){
				$invoiceDefault["invoice_type"] = $this->invoiceTypes[$this->invoiceType];
				$invoiceDefault["pair"] = $invoice->id;
				$invoiceDefault["name_info"] = "k faktúre ".$invoice->number;
			}

			if($this->invoiceType === "cancel"){
				$invoiceDefault["invoice_type"] = $this->invoiceTypes[$this->invoiceType];
				$invoiceDefault["pair"] = $invoiceDefault["cancel"] = $invoice->id;
				$invoiceDefault["name_info"] = "k faktúre ".$invoice->number;
				$invoiceDefault["show_payment_deposit"] = 0;
				$invoiceDefault["show_payment_deposit_price"] = null;
				$invoiceDefault["note_after"] = "";
				$invoiceDefault["show_payment_info"] = 0;
				foreach ($items as $k=> $item){
					$items[$k]["unit_price"] = sprintf("%.4F", ((float) $item["unit_price"]) * -1 );
					$items[$k]["price"] = sprintf("%.2F", ((float) $item["price"]) * -1 );
				}
			}


			/*$invoiceDefault["date_created"] = $invoiceDefault["date_created"]->format("d.m.Y");
			$invoiceDefault["date_delivery"] = $invoiceDefault["date_delivery"]->format("d.m.Y");
			$invoiceDefault["date_due"] = $invoiceDefault["date_due"]->format("d.m.Y");

			$invoiceDefault["due"] = "other";
*/
			$this["invoiceForm"]->setDefaults($invoiceDefault);
			$this->template->client = $this->getClient($invoice->invoice_client);


			$this["invoiceForm"]["items"]->setDefaults($items);

		}

		if($this->supplier->default_accounting_account !== null && (!isset($invoiceDefault["accounting_account"]))){
			$this["invoiceForm"]->setDefaults(["accounting_account" => $this->supplier->default_accounting_account]);
		}
		if($this->supplier->default_accounting_analytic_account !== null && !isset($invoiceDefault["accounting_analytic_account"])){
			$this["invoiceForm"]->setDefaults(["accounting_analytic_account" => $this->supplier->default_accounting_analytic_account]);
		}


		$this->setHiddens();
		$this->setSequences();
		//$this->modalInvoiceSupplierEditForm->setInvoice($invoice);
	}

	public function setHiddens(){
		$this->template->hidden["recurring-container"] = true;
		$this->template->hidden["recurring-date-delivery-container"] = true;
		$this->template->hidden["recurring-due-container"] = true;
		//bdump($this->selectedInvoiceType);
		if(!$this->selectedInvoiceType->hasDue){
			$this->template->invoiceData["date_due"] = null;
			$this->template->hidden["due-container"] = true;
			$this->template->hidden["due-select-container"] = true;
			//$this->template->hidden["variable-container"] = true;
		}
		if(!$this->selectedInvoiceType->hasDateDelivery){
			//$this->template->invoiceData["date_delivery"] = null;
			$this->template->hidden["date-delivery-container"] = true;
		}

		if($this->selectedInvoiceType->key === "delivery"){
			$this["invoiceForm"]["show_prices"]->setValue($this->supplier->default_prices_on_delivery);
		}

		if($this->selectedInvoiceType->key === "draft"){
			$this->template->hidden["contact-container"] = true;
			$this->template->hidden["specific-container"] = true;
			$this->template->hidden["constant-container"] = true;
			$this->template->hidden["order-container"] = true;
			$this->template->hidden["payment-container"] = true;
			$this->template->hidden["delivery-container"] = true;
			$this->template->hidden["edit-supplier-container"] = true;
			$this["invoiceForm"]["name"]->setValue("Koncept");
		}

		if(str_contains($this->selectedInvoiceType->key , "recurring") /*== "recurring"*/){
			$this->template->hidden["recurring-container"] = false;
			$this->template->hidden["date-created-container"] = true;
			$this->template->hidden["date-delivery-container"] = true;
			$this->template->hidden["edit-supplier-container"] = true;
			$this->template->hidden["due-container"] = true;
			$this->template->hidden["due-select-container"] = true;
			$this->template->hidden["recurring-date-delivery-container"] = false;
			$this->template->hidden["recurring-due-container"] = false;



			if($this->action === "add") {
				//bdump("reccurring actuibn");
				//bdump($this->sequence);
				$this["invoiceForm"]->setDefaults(["name"     => $this->sequence->getDocumentName().' {$number}',
				                                   "number"   => '{$number}',
				                                   "variable" => '{$variable}'
				]);

				foreach ($this->supplierIbansDefault as $ibanId => $iban){
					$this["invoiceForm"]["recurring_accounts"][$ibanId]->setDefaultValue(1);
				}

			}
		}
		if($this->supplier->paypal_email === null){
			$this->template->hidden["paypal-container"] = true;
		}
		//bdump($this->template->hidden);

		/*if($this->action === "add") {
			$this->template->sequencesList = [];
			if ($this->selectedInvoiceType->hasInvoiceSequence) {
				$this->template->sequencesList = $this->invoiceSupplierModel->fetchSequencesList($this->supplierId, $this->invoiceType, $this->invoiceTypes);
				//$this->invoiceTypes[$this->invoiceType]->id);
				$this["invoiceForm"]["invoice_sequence"]->setItems($this->template->sequencesList)->setDefaultValue($this->sequence->id);
			} else {
				$this->template->hidden["variable-container"]     = true;
				$this->template->hidden["number-container"]       = true;
				$this->template->hidden["date-created-container"] = true;
			}
		}*/
	}

	public function setSequences(): void
	{
		$this->template->sequencesList = [];
		if ($this->selectedInvoiceType->hasInvoiceSequence) {
			$this->template->sequencesList = $this->invoiceSupplierModel->fetchSequencesList($this->supplierId, $this->invoiceType, $this->invoiceTypes);
			//bdump($this->invoiceTypes[$this->invoiceType]->id);
			//bdump($this->template->sequencesList);
			$this["invoiceForm"]["invoice_sequence"]->setItems($this->template->sequencesList)->setDefaultValue($this->sequence->id);
		} else {
			$this->template->hidden["variable-container"]     = true;
			$this->template->hidden["number-container"]       = true;
			$this->template->hidden["date-created-container"] = true;
		}
	}

	public function actionEdit(int $id): void
	{
		$invoice = $this->invoiceModel->find($id)->fetch();
		if(!$invoice){
			throw new BadRequestException();
		}
		if($invoice->invoice_supplier !== $this->supplierId){
			throw new ForbiddenRequestException('Invoice is owned by other supplier. Selected supplier is ('.$this->supplier->name.')');
		}

		/*$this->template->sequencesList = $this->invoiceSupplierModel->fetchSequencesList($this->supplierId, $this->invoiceType, $this->invoiceTypes);
		//dumpe($this->template->sequencesList);
		$this["invoiceForm"]["invoice_sequence"]->setItems($this->template->sequencesList)->setDefaultValue($this->sequence->id);
*/
		$this->setSequences();

		$invoiceDefault = $invoice->toArray();

		$invoiceDefault["date_created"] = $invoiceDefault["date_created"]->format("d.m.Y");
		$invoiceDefault["date_delivery"] = $invoiceDefault["date_delivery"]->format("d.m.Y");
		$invoiceDefault["date_due"] = $invoiceDefault["date_due"]->format("d.m.Y");

		$invoiceDefault["due"] = "other";

		if($this->supplier->default_accounting_account !== null && $invoiceDefault["accounting_account"] === null){
			$invoiceDefault["accounting_account"] = $this->supplier->default_accounting_account;
		}
		if($this->supplier->default_accounting_analytic_account !== null && $invoiceDefault["accounting_analytic_account"] === null){
			$invoiceDefault["accounting_analytic_account"] = $this->supplier->default_accounting_analytic_account;
		}


		$this["invoiceForm"]->setDefaults($invoiceDefault);
		$this->template->client = $this->getClient($invoice->invoice_client);

		$items = [];
		foreach ($invoice->related("invoice_item") as $itemRow){
			$item = $itemRow->toArray();
			unset($item->invoice);
			$item["price"] = sprintf("%.2F", $item["price"]);
			$item["unit_price"] = sprintf("%.4F", $item["unit_price"]);
			$items[] = $item;
		}



		$this["invoiceForm"]["items"]->setDefaults($items);

		$this->template->title = "Úprava faktúry ".$invoice->number;

		$this->modalInvoiceSupplierEditForm->setInvoice($invoice);

		$this["invoiceForm"]["date_created"]->setHtmlAttribute("data-invoice-exclude", $invoice->number)->setHtmlAttribute("data-invoice-date", $invoiceDefault["date_created"])->setHtmlAttribute("data-invoice-sequence-id", $invoiceDefault["invoice_sequence"]);;


		// Ak je pravidelna FA označiť vybraty IBAN
		if(str_contains($invoice->ref("invoice_type")->key,"recurring")) {
			foreach ($invoice->related("invoice_iban") as $iban) {
				$this["invoiceForm"]["recurring_accounts"][$iban->invoice_supplier_iban]->setDefaultValue(1);
			}
		}
		$this->setHiddens();
		$this->setView("add");
	}



	public function actionDetail(int $id): void
	{
		$invoice = $this->invoiceModel->find($id)->fetch();
		if ( ! $invoice) {
			throw new BadRequestException();
		}
		if($invoice->invoice_supplier !== $this->supplierId){
			throw new ForbiddenRequestException('Invoice is owned by other supplier. Selected supplier is ('.$this->supplier->name.')');
		}

		if(str_contains($this->invoiceType,"recurring")){
			$this->redirect("Invoice:default");
		}

		$this->template->setParameters($this->invoiceModel->getDetailData($invoice));
		$this->template->trans = $this->invoiceTranslator;
		$this->template->trans->setLang($invoice->lang);

		$this->template->payments = $invoice->related("invoice_payment");
		$this->template->paymentButton = $this->getPaymentButton($invoice, $this->template->paid, $this->template->amount);
		$this->template->history = $invoice->related("invoice_history")->order("datetime DESC");
		$this->template->due = $this->invoiceModel->isAfterDue($invoice);

		if(empty($this->template->ibans)){
			if($invoice->show_qr){
				$invoice->update(["show_qr" => false]);
			}
			/** @var ChoiceControl $control */
			$control = $this["showForm"]["show_qr"];
			$control->setDisabled();
		}

		//$pdf = $this->invoiceModel->getPdf($invoice,  true);

		$this["showForm"]->setDefaults($this->template->invoiceData);
		$this["showForm"]["lang"]->getControlPrototype()->class("form-control-sm form-control select-flag-".$invoice->lang)->data("default-class", "form-control-sm form-control");
	}

	public function actionReciept($id, $destination = Destination::INLINE, $debug= null){
		$invoice = $this->invoiceModel->find($id)->fetch();
		if(!$invoice){
			throw new BadRequestException();
		}
		if($invoice->invoice_supplier !== $this->supplierId){
			throw new ForbiddenRequestException('Invoice is owned by other supplier. Selected supplier is ('.$this->supplier->name.')');
		}

		if($debug !== null){
			$this->sendResponse($this->invoiceModel->getReciept($invoice, TextResponse::class));
		}
		$this->sendResponse($this->invoiceModel->getReciept($invoice));
	}

	public function actionPdf($id,$destination = Destination::INLINE,$debug = null){
		$invoice = $this->invoiceModel->find($id)->fetch();
		if(!$invoice){
			throw new BadRequestException();
		}
		if($invoice->invoice_supplier !== $this->supplierId){
			throw new ForbiddenRequestException('Invoice is owned by other supplier. Selected supplier is ('.$this->supplier->name.')');
		}

		//$invoiceEntity = $this->entityManager->getRepository(Invoice::class)->find($id);
		//dump($invoiceEntity);
		//exit;

		if($debug !== null){
			//$this->sendResponse($this->invoiceModel->getPdf($invoice, TextResponse::class));
		}
		$this->sendResponse($this->invoiceModel->getPdf($invoice));
	}

	/*public function actionInbox(){

		/** @var Document $document */
	/*	$document = $this->entityManager->getRepository(Document::class)->find(1671);

		$data = $this->invoiceInboxModel->addExpenseInvoice($document, ["amount" => 150, "client_name" => "Jakub Mikita", "variable" => "123456", "document" => ["id" => $document->id, "expense_number" => $document->expense_number]]);

		dump($document->document_company->getInvoiceSupplier(), $data);
		exit;
	}*/

	public function handleclientCreateExpense(int $invoiceId) :void
	{
		$invoiceRow = $this->invoiceModel->find($invoiceId)->fetch();
		/** @var InvoiceRepository $invoiceRepository */
		$invoiceRepository = $this->entityManager->getRepository(Invoice::class);
		/** @var Invoice $invoice */
		$invoice = $this->entityManager->createEntity($invoiceRow, Invoice::class);
		if($invoiceRow !== null) {
			$document = $invoiceRepository->createExpense($invoice);

			$this->invoiceHistoryModel->addMessage('Odoslaná odberateľovi prostredníctvom '.$this->companyService->getCompany()->name, $invoice->id, $this->getUser());

			$paymentData = $invoice->getPaymentData();
			$this->invoiceInboxModel->addExpenseInvoice($document, ["amount" => $paymentData["amount"], "client_name" => $invoice->invoice_supplier->name, "variable" => $invoice->variable, "document" => ["id" => $document->id, "expense_number" => $document->expense_number]]);

			//$this->invoiceInboxModel->addExpenseInvoice($document->document_company);

			$this->invoiceModel->getPdf($invoiceRow, true);
			$this->template->clientExpense = 1;
			$this->redrawControl("clientHas");
			$this->redrawControl("history");
		}
	}

	public function handleCheckInvoiceNumber() {
		$date = new DateTime($this->getHttpRequest()->getQuery("invoiceDate"));
		$invoiceNo = $this->getHttpRequest()->getQuery("invoiceNo");
		$exclude = $this->getHttpRequest()->getQuery("invoiceExclude");
		$originalDate = ($od = $this->getHttpRequest()->getQuery("invoiceOriginalDate")) === "" ? null :$od;
		$sequenceId = ($s = $this->getHttpRequest()->getQuery("sequenceId")) === "" ? null : (int) $s;
		$originalSequenceId = ($s = $this->getHttpRequest()->getQuery("originalSequenceId")) === "" ? null : (int) $s;

		$originalSequence = $sequence = $this->sequence;

		if($sequenceId !== null && str_contains($invoiceNo, "{\$number}")){
			$sequence = new InvoiceSequenceVariable($this->invoiceSequenceModel->find($sequenceId)->fetch());
			//bdump($sequence);
			$this->payload->fill[$this["invoiceForm"]["name"]->getHtmlId()]          = $sequence->getDocumentName() . ' ' . $invoiceNo;
		} else {

			if ($sequenceId !== null) {
				$originalSequenceId = $originalSequenceId ?? $originalSequence->id;
				if ($sequenceId !== $originalSequence->id) {
					$sequence = new InvoiceSequenceVariable($this->invoiceSequenceModel->find($sequenceId)->fetch());

				}

				$sequence->setDateTime($date);
				$sequence->setLastSequence($this->invoiceModel->getLastSequence($this->supplierId, $sequence, $date,
					$exclude));

				$next = $sequence->getNextSequence();
				bdump($sequence, $next);
				$showNote = true;
				if ($originalDate !== null) {
					$originalDateObject = new DateTime($originalDate);
					if ($originalDateObject->format("Y") === $date->format("Y") && $originalDateObject->format("m") === $date->format("m") && $sequence->id === $originalSequenceId) {
						$next     = $exclude;
						$showNote = false;
					}
				}
				if ($sequence->id !== $originalSequenceId) {
					$showNote = true;
				}
				//bdump($next, $invoiceNo);
				if ($next !== $invoiceNo) {

					$noteText = 'Číslo dokladu bolo zmenené, pretože ste <strong>prešli do iného zdaňovacieho obdobia.</strong>';
					//if($sequence->id !== $originalSequence->id){
					if (count($this->template->sequencesList) > 1) {
						$noteText = 'Číslo dokladu bolo zmenené, pretože ste <strong>prešli do iného zdaňovacieho obdobia.</strong> alebo <strong>zmenili číselny rad</strong>';
					}
					//}

					$this->payload->fill[$this["invoiceForm"]["number"]->getHtmlId()]        = $next;
					$this->payload->fill[$this["invoiceForm"]["variable"]->getHtmlId()]      = $sequence->toInteger($next);
					$this->payload->readonly[$this["invoiceForm"]["variable"]->getHtmlId()]  = 1;
					$this->payload->fill[$this["invoiceForm"]["name"]->getHtmlId()]          = $sequence->getDocumentName() . ' ' . $next;
					$this->payload->fill[$this["invoiceForm"]["date_delivery"]->getHtmlId()] = $date->format("d.m.Y");

					$note = $showNote ? (string) Html::el("div")->setAttribute("class",
						"alert alert-warning tx-12 p-2")->addHtml($noteText) : ' ';

					$this->payload->note[$this["invoiceForm"]["number"]->getHtmlId()] = $note;
				} else {
					//$this->payload->note[$this["invoiceForm"]["number"]->getHtmlId()] = '';
				}
			} else {
				$note                                                                   = (string) Html::el("div")->setAttribute("class",
					"alert alert-danger tx-12 p-2")->addHtml('Vybrali ste možnosť bez číselneho radu. Vyplňte údaje o doklade ručne.');
				$this->payload->fill[$this["invoiceForm"]["number"]->getHtmlId()]       = $this->payload->fill[$this["invoiceForm"]["variable"]->getHtmlId()] = $this->payload->fill[$this["invoiceForm"]["name"]->getHtmlId()] = "";
				$this->payload->readonly[$this["invoiceForm"]["variable"]->getHtmlId()] = 0;
				$this->payload->note[$this["invoiceForm"]["number"]->getHtmlId()]       = $note;
			}
		}
	}

	public function handleLoadClientInfo(){
		$clientId = (int) $this->getHttpRequest()->getQuery("clientId");
		$this->getSession("invoice")->set("clientId", $clientId);
		$this->template->client = $this->getClient($clientId);
		$this->redrawControl("client");
	}

	public function createComponentShowForm($name) : Form
	{
		$f = $this->formFactory->create($this, $name);
		$f->addHidden("id")->addRule($f::INTEGER, "");
		$f->addSelect("lang", "Jazyk dokumentu", $this->invoiceTranslator->getLocales());
		$f->addCheckbox("show_payment_info","Info o úhrade");
		$f->addCheckbox("show_qr","QR kód");
		$f->addCheckbox("show_signature","Podpis");
		$f->addCheckbox("show_paypal","PayPal");
		$f->addCheckbox("show_prices", "Zobrazovať ceny");
		$f->addSubmit("save","Uložiť");
		$f->onSuccess[] = function (Form $form, ArrayHash $values){
			bdump($values);
			if($values->id > 0){
				$invoice = $this->invoiceModel->find($values->id)->fetch();
				$invoice->update((array) $values);

				$this->template->setParameters($this->invoiceModel->getDetailData($invoice));
				$this->template->trans->setLang($invoice->lang);

				$this->redrawControl("invoiceDocument");
			}
		};
		return $f;
	}

	public function getClient(?int $id = null) : ?ActiveRow
	{
		if($id === null){
			$id = $this->getSession("invoice")->get("clientId");
			if($id === null){
				return null;
			}
		}
		return $this->invoiceClientModel->find($id)->fetch();
	}


	public function handleDeleteInvoice(int $invoiceId) {
		$invoice = $this->invoiceModel->find($invoiceId)->fetch();

		try{
			$this->invoiceModel->deleteDocuments($invoice);
			$invoice->delete();

			$this->flashMessage($this->invoiceTypes[$this->invoiceType]->name." bola úspešne vymazaná.", Message::LEVEL_ERROR);

			if($this->isAjax()){
				//$this->template->missingNumbers = $this->invoiceModel->getMissingVariables($this->supplierId, $this->invoiceTypes[$this->invoiceType]->id);
				$this->redrawControl("years");
				$this["invoiceGrid"]->reload();
			}

		} catch (\Throwable $e){
			$this->flashMessage($this->logException($e), Message::LEVEL_ERROR);
		}
		if(!$this->isAjax()) {
			$this->redirect("Invoice:default");
		}

	}
	public function handledeletePayment(int $paymentId){
		$payment = $this->invoicePaymentModel->find($paymentId)->fetch();
		$invoice = $payment->ref("invoice");
		if($payment->invoice_supplier_bank_account !== null){
			$payment->ref("invoice_supplier_bank_account")->update(["invoice" => null]);
		}
		if($payment->invoice_cash_register_item !== null){
			$payment->ref("invoice_cash_register_item")->delete();
		}
		$this->invoiceHistoryModel->addMessage("Vymazaná úhrada ({$payment->payment_type}) v sume <strong>".(new CurrencyFilter())($payment->volume)."</strong>",$invoice->id, $this->getUser());
		$payment->delete();
		$this->template->payments = $invoice->related("invoice_payment");
		$this->template->paid = $invoice->related("invoice_payment")->sum("volume");
		$this->template->amount = $invoice->related("invoice_item")->sum("price");
		$this->template->isPaid = $this->template->paid == $this->template->amount;
		$this->template->due = $this->invoiceModel->isAfterDue($invoice);
		$this->template->paymentButton = $this->getPaymentButton($invoice, $this->template->paid, $this->template->amount);

		$this->flashMessage("Úhrada bola vymazaná.", Message::LEVEL_ERROR);
		$this->redrawControl("payments");
		$this->redrawControl("actions");
		$this->redrawControl("invoiceDocument");
		$this->redrawControl("history");
	}
	public function handleChangeYear(int|null $year){
		$this->getSession($this->invoiceSessionNamespace)->set("year",$year);
		$this->template->year = $year;
		$this["invoiceGrid"]->setDataSource($this->getSelection($this->getSession($this->invoiceSessionNamespace)->get("selection"), $year, $this->getSession($this->invoiceSessionNamespace)->get("sequenceId")));
		$this->redrawControl("years");
	}

	public function handleChangeSelection(string $type){
		$this->getSession($this->invoiceSessionNamespace)->set("selection",$type);
		$this->template->selection = $type;
		$this["invoiceGrid"]->setDataSource($this->getSelection($type, $this->getSession($this->invoiceSessionNamespace)->get("year"), $this->getSession($this->invoiceSessionNamespace)->get("sequenceId")));
		$this->redrawControl("years");
	}

	public function handleChangeSequenceId(?int $sequenceId){
		$sequenceId = $sequenceId === 0 ? null : $sequenceId;
		$this->getSession($this->invoiceSessionNamespace)->set("sequenceId",$sequenceId);
		$this->template->sequenceId = $sequenceId;
		$this["invoiceGrid"]->setDataSource($this->getSelection($this->getSession($this->invoiceSessionNamespace)->get("selection"), $this->getSession($this->invoiceSessionNamespace)->get("year"), $sequenceId));
		$this->redrawControl("years");
	}

	public function handleToDocuments(int $id){
		$invoice = $this->invoiceModel->find($id)->fetch();
		if($invoice){
			$document = $invoice->related("document")->fetch();
			if($document){
				$this->documentModel->deleteFromInvoice($invoice);
				$this->flashMessage("Faktúra bola odstránena z elektronického spisu.");
			} else {
				bdump("pridať");
				$this->documentModel->createFromInvoice($invoice);
				$this->flashMessage("Faktúra bola pridaná do elektronického spisu.");
			}
			$this["invoiceGrid"]->reload();
		}
	}

	public function getSelection(string $filter, int|string $year, ?int $sequenceId = null ) : Selection
	{

		/*$invoiceTypeId = $this->invoiceTypes[$this->invoiceType]->id;
		if($this->invoiceType === "cancel"){
			$invoiceTypeId = $this->invoiceTypes["regular"]->id;
		}*/
		$invoiceTypeId = $this->invoiceTypes[$this->invoiceType]->id;
		if($this->invoiceType === "regular"){
			$invoiceTypeId = [$this->invoiceTypes[$this->invoiceType]->id, $this->invoiceTypes["cancel"]->id];
		}

		$where = ["invoice_supplier" => $this->supplierId, "invoice_type" => $invoiceTypeId];

		$selection = $this->invoiceModel->findBy($where);
		if($filter === "paid"){
			$selection->where(["(ROUND((SELECT SUM(volume) FROM invoice_payment WHERE invoice = invoice.id),2) >= ROUND((SELECT SUM(price) FROM invoice_item WHERE invoice = invoice.id),2)) OR show_payment_deposit_price IS NOT NULL"]);
		} elseif ($filter === "unpaid"){
			$selection->where(["number NOT LIKE ?" => '%storno%',"((SELECT SUM(volume) FROM invoice_payment WHERE invoice = invoice.id) IS NULL OR ROUND((SELECT SUM(volume) FROM invoice_payment WHERE invoice = invoice.id),2) < ROUND((SELECT SUM(price) FROM invoice_item WHERE invoice = invoice.id),2)) AND show_payment_deposit_price IS NULL"]);
		} elseif ($filter === "due"){
			$selection->where(["number NOT LIKE ?" => '%storno%',"((SELECT SUM(volume) FROM invoice_payment WHERE invoice = invoice.id) IS NULL OR ROUND((SELECT SUM(volume) FROM invoice_payment WHERE invoice = invoice.id),2) < ROUND((SELECT SUM(price) FROM invoice_item WHERE invoice = invoice.id),2)) AND show_payment_deposit_price IS NULL"])->where("date_due < ?", new DateTime());
		}
		//bdump($year);
		if($year > 0) {
			$selection->where("YEAR(date_created) = ?", $year);
		}

		if($sequenceId !== null){
			$selection->where(["invoice_sequence" => $sequenceId]);
		}
		$selection->select("ROUND((SELECT SUM(price) FROM invoice_item WHERE invoice = invoice.id),2) AS amount,ROUND((SELECT SUM(volume) FROM invoice_payment WHERE invoice = invoice.id),2) AS paid");

		return $selection->order("number DESC");
	}






	public function createComponentInvoiceForm($name) : Form
	{

		$paymentTypes = $this->invoicePaymentTypeModel->findAll()->whereOr(["invoice_supplier" => $this->supplierId, "invoice_supplier IS NULL"])->fetchPairs("id", "name");
		$deliveryTypes = $this->invoiceDeliveryTypeModel->findAll()->whereOr(["invoice_supplier" => $this->supplierId, "invoice_supplier IS NULL"])->fetchPairs("id", "name");

		//Debugger::log(new IOException('test'));

		$f = $this->formFactory->create($this,$name);
		$f->addHidden("id")->addRule($f::INTEGER,"");
		$f->addHidden("proforma")->setNullable();
		$f->addHidden("cancel")->setNullable();
		$f->addHidden("invoice_type")->addRule($f::INTEGER,"")->setDefaultValue($this->invoiceTypes[$this->invoiceType]->id);
		$f->addHidden("invoice_supplier")->addRule($f::INTEGER,"")->setDefaultValue($this->supplierId);
		//$f->addHidden("invoice_sequence")->addRule($f::INTEGER,"")->setNullable();
		$f->addCheckbox("show_paypal", "PayPal platobné tlačidlo")->setDefaultValue(0);
		$f->addHidden("show_prices")->addRule($f::INTEGER,"")->setDefaultValue(1);
		$f->addHidden("show_payment_info")->addRule($f::INTEGER,"")->setDefaultValue(1);
		$f->addHidden("show_payment_deposit")->addRule($f::INTEGER,"")->setDefaultValue(0);
		$f->addHidden("show_payment_deposit_price")->setNullable();

		$f->addSearchSelect("accounting_account", "Syntetický účet", DocumentModel::ACCOUNTING_ACCOUNT["Výnosy"])->setPrompt("neviem uviesť");//->setRequired();
		$f->addSearchSelect("accounting_analytic_account","Analytický účet", DocumentModel::ANALYTIC_ACCOUNT)->setPrompt("-- vyberte --");

		$f->addHidden("pair")->setNullable();
		$f->addHidden("name_info")->setNullable();
		$f->addSearchSelect("invoice_sequence","Číselny rad", [])
		  ->setHtmlAttribute("data-invoice-watcher","sequence")
		  //->setDefaultValue($this->sequence->id)
		  ->setPrompt("-- bez číselneho radu ---");
		$f->addText("number","Číslo dokladu");//->setRequired()->setDefaultValue($this->sequence->getNextSequence())->setHtmlAttribute("data-invoice-watcher","number");
		$f->addDate("date_created","Dátum vystavenia")
		  ->setRequired()
		  ->setDefaultValue((new DateTime())->format("d.m.Y"))
		  ->setHtmlAttribute("data-invoice-watcher","date_created");
		$f->addText("variable","Variabilný symbol");//->setRequired()->setDefaultValue($this->sequence->toInteger())->setHtmlAttribute("readonly",true);
		$f->addDate("date_delivery","Dátum dodania")->setRequired()->setDefaultValue((new DateTime())->format("d.m.Y"));
		$f->addText("name","Názov dokladu");//->setRequired()->setDefaultValue($this->invoiceTypes[$this->invoiceType]->name . " ".$this->sequence->getNextSequence());

		$f->addSelect("due","Splatné do", $this->invoiceSupplierModel->getDefaultDueArray($this->supplier))->setRequired()->setDefaultValue($this->supplier->default_payment_days ?? 7)->addCondition($f::EQUAL, "other")->toggle("due-container");
		$f->addText("date_due","Dátum splatnosti")->setDefaultValue((new DateTime())->modifyClone("+".($this->supplier->default_payment_days ?? 7)." days")->format("d.m.Y"))->addConditionOn($f["due"],$f::EQUAL, "other")->setRequired();

		$f->addTextArea("note_before","Poznámka nad položkami", null, 2)->setNullable()->setHtmlAttribute("placeholder", "Poznámka nad položkami");
		$f->addTextArea("note_after","Poznámka", null, 7)->setNullable()->setHtmlAttribute("placeholder", "Poznámka")->setDefaultValue((!$this->isVatPayer ? 'Nie sme platiteľmi DPH.' : '') );

		/*dumpe(
			$this->invoiceClientModel->findBy(["user"=> $this->getUser()->getId()])->fetchPairs("id","name"),
			$this->invoiceClientModel->findBy(["user"=> $this->suppliers->fetchPairs("user","user")])->fetchPairs("id","name")
		);*/
		$f->addSearchSelect("invoice_client", "Odberateľ", $this->invoiceClientModel->findBy(["invoice_supplier"=> $this->supplierId])->order("name")->fetchPairs("id","name"))->setHtmlAttribute("data-invoice-watcher","client")->setRequired()->setPrompt("-- vyhľadajte odberateľa --");

		$f->addText("constant","Konštantný symbol")->setNullable()->setDefaultValue($this->supplier->default_constant);
		$f->addText("specific","Špecifický symbol")->setNullable();
		$f->addText("order_no", "Číslo objednávky")->setNullable();

		$f->addSelect("invoice_payment_type", "Forma úhrady", $paymentTypes)->setPrompt("")->setDefaultValue($this->supplier->default_payment_type);
		$f->addSelect("invoice_delivery_type", "Spôsob dodania", $deliveryTypes)->setPrompt("");


		$f->addText("issued_by", "Faktúru vystavil")->setNullable()->setDefaultValue(UserModel::formatName($this->selfUserModel, false, false, true))->setRequired();
		$f->addText("issued_by_phone","Telefón")->setNullable()->setDefaultValue($this->selfUserModel->name_list->phone);
		$f->addText("issued_by_web","Web")->setNullable()->setDefaultValue($this->selfUserModel->name_list->web ?? '');
		$f->addText("issued_by_email","E-mail")->setNullable()->setDefaultValue($this->selfUserModel->username);

		if($this->invoiceTypes[$this->invoiceType]->hasInvoiceSequence){
			$f["number"]->setRequired()->setDefaultValue($this->sequence->getNextSequence())->setHtmlAttribute("data-invoice-watcher","number");
			$f["variable"]->setRequired()->setDefaultValue($this->sequence->toInteger())->setHtmlAttribute("readonly",true);
			$f["name"]->setRequired()->setDefaultValue($this->invoiceTypes[$this->invoiceType]->name . " ".$this->sequence->getNextSequence());
		} else {
			$f["number"]->setNullable();
			$f["variable"]->setNullable();
			$f["name"]->setCaption('Názov '.$this->invoiceTypes[$this->invoiceType]->name."u")->setNullable();
		}


		$f->addSelect("recurring_date_delivery", "Dátum dodania",["created" => "V deň vystavenia faktúry"])->setPrompt("-- vyberte --");
		$f->addSelect("recurring_due_days", "Splatnosť", $this->invoiceSupplierModel->getDefaultDueArray($this->supplier, false) /*["0" => "Dátum vystavenia", "1" => "1 deň", "7" => "7 dní", "14" => "14 dní","20" => "20 dní","30" => "30 dní", "60" => "60 dní"]*/)->setPrompt("-- vyberte --");
		$f->addSelect("recurring_frequency", "Opakovanie", $this->invoiceModel::RECURRING_FREQUENCY)->setPrompt("Neopakovať");
		$f->addDate("recurring_date", "Nasledujúce")->setNullable();
		$f->addCheckbox("recurring_infinity","Opakovať neobmedzene");
		$f->addText("recurring_count", "Počet vystavení")->setHtmlType("number")->setNullable();
		$f->addCheckbox("recurring_send","Po vytvorení odoslať");
		$f->addText("recurring_email","E-mailové adresy")->setNullable();

		$accounts = $f->addContainer("recurring_accounts");
		foreach($this->supplierIbans as $iban){
			$accounts->addCheckbox($iban["id"], $iban["name"]);
		}


		if(str_contains($this->invoiceTypes[$this->invoiceType]->key, "recurring")) {
			$f["recurring_date_delivery"]->setValue("created")->setRequired();
			$f["recurring_due_days"]->setValue($this->supplier->default_payment_days ?? 7)->setRequired();
			$f["recurring_date"]->setDefaultValue((new DateTime())->format("d.m.Y"))->setRequired();
			$f["recurring_infinity"]->setDefaultValue(true)->addCondition($f::Blank, false)->toggle("recurring-count-container");
			$f["recurring_count"]->addConditionOn($f["recurring_infinity"], $f::Blank, false)->setRequired();
			$f["recurring_send"]->setDefaultValue(false)->addCondition($f::Filled, false)->toggle("recurring-email-container");
			$f["recurring_email"]->addConditionOn($f["recurring_send"], $f::Filled, false)->setRequired();
			//dumpe($this->supplierIbans, $this->supplierIbansDefault);
			//$f["recurring_account"]

		}

		$units = $this->invoiceUnitModel->findAll()->whereOr(["invoice_supplier" => $this->supplierId, "invoice_supplier IS NULL"])->fetchPairs("unit" , "unit") + ["own" => "Vlastná"];
		/** @var Multiplier $multiplier */
		$multiplier = $f->addMultiplier("items", function (Container $container, NetteForm $form) use($units){
			$defaultTax = 0;
			$defaultSumText = 'Celkom';
			if($this->isVatPayer){
				$defaultTax = $this->supplier->vat_default;
				$defaultSumText = 'Celkom s DPH';
			}
			$container->addHidden("id")->addRule($form::INTEGER, "");
			$container->addHidden("is_rounding")->addRule($form::INTEGER,"")->setDefaultValue(0);
			$container->addText("name","Názov položky")->setHtmlAttribute("class", 'form-control')->setHtmlAttribute("placeholder", "Názov položky")->setNullable();
			$container->addTextArea("description", "Popis položky", null, 2)->setHtmlAttribute("class","form-control")->setHtmlAttribute("placeholder", "Popis položky")->setNullable();
			$container->addText("quantity","Počet")->setHtmlAttribute("class","form-control")->setHtmlAttribute("data-invoice-watcher",'quantity')->setHtmlAttribute("placeholder", "Počet")->setNullable();
			//$container->addText("unit","Jednotka")->setHtmlAttribute("class","form-control")->setHtmlAttribute("placeholder", "Jednotka")->setNullable();
			$container->addSelect("unit","Jednotka", $units)//->setTranslator(null)
			          ->setHtmlAttribute("class","form-control")
			          ->checkDefaultValue(false)
			          ->setHtmlAttribute("placeholder","")
			          ->setPrompt("")->addCondition(Form::EQUAL, "own")->toggle("select-unit-container-%index%",false)->endCondition();


			$container->addText("unit_own","Jednotka")->setHtmlAttribute("class","form-control")->setHtmlAttribute("placeholder","")->addConditionOn($container["unit"],Form::EQUAL, "own")->toggle("own-unit-container-%index%");


			$container->addText("unit_price","Cena")->setHtmlAttribute("class","form-control")->setHtmlAttribute("data-invoice-watcher",'unit_price')->setHtmlAttribute("placeholder", "Cena");
			$container->addText("tax","DPH%")->setHtmlAttribute("class","form-control")->setHtmlAttribute("data-invoice-watcher",'tax')->setHtmlAttribute("placeholder", "DPH%")->setDefaultValue($defaultTax)->setNullable();
			$container->addText("price",$defaultSumText)->setHtmlAttribute("class","form-control")->setHtmlAttribute("data-invoice-watcher",'price')->setHtmlAttribute("placeholder", $defaultSumText)->setDefaultValue(0.00);
		});
		$multiplier->addCreateButton('add')->addOnCreateCallback(function (Submitter $submitter) {
			$submitter->onClick[] = function () use ($submitter): void {
				//bdump($submitter);
				$this->redrawControl("itemsSnippet");
			};
			$submitter->setCaption(Html::el()->addHtml('<i class="fas fa-plus-circle"></i> Pridať dalšiu položku'));
		})->addClass(' btn btn-sm');

		$multiplier->addRemoveButton('remove')->addOnCreateCallback(function (SubmitButton $submitter) {
			$submitter->onClick[] = function () {
				//bdump($this["surveyInputForm"]);
				$this->redrawControl("itemsSnippet");
			};
			$submitter->setCaption(Html::el("i")->setAttribute("class", "fas fa-times"));
		})->addClass("btn btn-default text-danger"); //->addClass(' btn btn-sm btn-danger');

		//$multiplier->setMinCopies(7);


		$f->addSubmit("save","Uložiť");
		$f->onValidate[] = function (Form $form, ArrayHash $values){
			$items = (array) $values->items;
			$firstItem = reset($items);

			if($firstItem["name"] === null && $form->isSubmitted()?->getName() === "save"){
				$errorMessage = "Dokument musí obsahovať aspoň jednu položku";
				$form->addError($errorMessage);
				$this->flashMessage($errorMessage, Message::LEVEL_ERROR, "Dokument nebol uložený");
			}
			if($values->date_delivery->getTimestamp() > $values->date_created->getTimestamp()){
				$form->addError("Dátum dodania musí byť menší alebo rovnaký ako dátum vystavenia.");
				$this->flashMessage("Dátum dodania musí byť menší alebo rovnaký ako dátum vystavenia.", Message::LEVEL_ERROR, "Chybné dátumy faktúry");
				//bdump($values);
			}
		};
		//$f->onValidate[] = function (Form $form, ArrayHash $values) use ($presenter) {


		//};
		$f->onError[] = function (Form $form){
			bdump($form->getErrors());
		};
		$f->onSuccess[] = function (Form $form, ArrayHash $values) use($paymentTypes, $deliveryTypes, $units){
			bdump($values);
			//exit;
			$items = $values->items;
			$pair = $values->pair;
			$itms = [];
			unset($values->items, $values->pair);

			//exit;
			$db = $this->invoiceModel->getDatabaseConnection();
			try {
				$db->beginTransaction();
				$client = $this->invoiceClientModel->find($values->invoice_client)->fetch();
				$clientData = $this->invoiceClientModel->getInvoiceColumns($client);

				unset($clientData["iban"]);

				$supplierData = $this->invoiceSupplierModel->getInvoiceColumns($this->supplier);
				$supplierData["supplier_vat_payer"]         = (int) $this->isVatPayer;
				unset($supplierData["supplier_iban"],$supplierData["supplier_bic"]);

				/*$supplierData["supplier_name"]              = $this->supplier->name;
				$supplierData["supplier_ico"]               = $this->supplier->ico;
				$supplierData["supplier_dic"]               = $this->supplier->dic;
				$supplierData["supplier_icdph"]             = $this->supplier->icdph;
				$supplierData["supplier_address"]           = $this->supplier->address;
				$supplierData["supplier_city"]              = $this->supplier->city;
				$supplierData["supplier_zip"]               = $this->supplier->zip;
				$supplierData["supplier_country"]           = $this->supplier->country;
				$supplierData["supplier_bussines_register"] = $this->supplier->business_register;
				$supplierData["supplier_vat_payer"]         = (int) $this->isVatPayer;
				//$supplierData["supplier_iban"]              = $this->supplier->iban;
				//$supplierData["supplier_bic"]               = $this->supplier->bic;*/

				//bdump($this->invoiceClientModel->getInvoiceColumns($this->invoiceClientModel->find($values->invoice_client)->fetch()));

				$values->payment_type  = $paymentTypes[$values->invoice_payment_type] ?? null;
				$values->delivery_type = $paymentTypes[$values->invoice_delivery_type] ?? null;

				if(!($values->date_created instanceof \DateTimeInterface)){
					$values->date_created  = new DateTime($values->date_created);
				}
				if(!($values->date_delivery instanceof \DateTimeInterface)) {
					$values->date_delivery = new DateTime($values->date_delivery);
				}
				$values->date_due      = new DateTime($values->date_due);

				if (is_numeric($values->due)) {
					$values->date_due = (clone $values->date_created)->modifyClone("+" . $values->due . " days");
				}
				unset($values->due);


				if ((int) $values->id === 0) {

					$values = (array) $values + $clientData + $supplierData;

					$recurring_accounts = (array) $values["recurring_accounts"];
					unset($values["recurring_accounts"]);
					//bdump($values);
					//bdump($recurring_accounts);
					//exit;
					$invoice = $this->invoiceModel->insert($values);
					$countIbans = 0;
					foreach ($recurring_accounts as $account_id => $show){
						if($show){
							$this->invoiceModel->addIban($invoice, $account_id);
							$countIbans++;
						}
					}

					foreach ($items as $item) {
						unset($item["id"]);
						$item->quantity = $item->quantity !== null ? (float) str_replace(",", ".", $item->quantity) : null;
						$item->unit_price = (float) str_replace(",", ".", $item->unit_price);
						$item->price      = (float) str_replace(",", ".", $item->price);

						$item->unit = $item->unit === "own" ? $item->unit_own : $item->unit;
						unset($item->unit_own);
						$this->invoiceUnitModel->checkUnit($units, $item->unit ?? '', $invoice->invoice_supplier);
						$units[$item->unit ?? ''] = $item->unit ?? '';
						$itms[]           = (array) $item + ["invoice" => $invoice->id];

					}
					$this->invoiceItemModel->insert($itms);
					if($countIbans === 0) {
						$countIbans = $this->invoiceModel->addIbans($invoice);
					}

					$iupdate = ["qr_hash" => $this->invoiceModel->getQrHash($invoice)];
					if($countIbans === 0){
						$iupdate["show_qr"] = false;
					}

					$invoice->update($iupdate);

					$this->invoiceHistoryModel->addMessage('Vystavená '.Strings::firstLower($this->selectedInvoiceType->name), $invoice->id, $this->getUser());

					//$this->invoiceModel->toDocuments($invoice);
					$this->flashMessage($this->selectedInvoiceType->name . " bola vytvorená.");

				} else {
					$invoice = $this->invoiceModel->find($values->id)->fetch();

					if(str_contains($invoice->ref("invoice_type")->key, "recurring")) {
						foreach ($values->recurring_accounts as $accountId => $visible) {
							if ($visible) {
								$this->invoiceModel->addIban($invoice, $accountId);
							} else {
								$this->invoiceModel->removeIban($invoice, $accountId);
							}
						}
					}
					unset($values->recurring_accounts);

					$qrHash = $this->invoiceModel->getQrHash($invoice);

					$values = (array) $values;
					//unset($supplierData["supplier_vat_payer"]);
					if($invoice->invoice_client != $values["invoice_client"]){
						$values += $clientData;
					}

					$invoice->update($values);


					$invoiceItems = $invoice->related("invoice_item")->fetchAll();
					$processed = [];
					foreach ($items as $item) {
						$item->quantity = $item->quantity !== null ? (float) str_replace(",", ".", $item->quantity) : null;
						$item->unit_price = (float) str_replace(",", ".", $item->unit_price);
						$item->price      = (float) str_replace(",", ".", $item->price);
						$item->unit = $item->unit === "own" ? $item->unit_own : $item->unit;
						unset($item->unit_own);
						$this->invoiceUnitModel->checkUnit($units, $item->unit ?? '', $invoice->invoice_supplier);
						$units[$item->unit ?? ''] = $item->unit ?? '';

						if(isset($invoiceItems[$item->id]) && $item->id > 0){
							$invoiceItems[$item->id]->update((array) $item);
							$processed[] = $item->id;
						} else {
							$insItem = $this->invoiceItemModel->insert((array) $item + ["invoice" => $invoice->id]);
							$processed[] = $insItem->id;
						}
					}

					if(!empty($processed)){
						$invoice->related("invoice_item")->where(["id NOT IN ?" =>$processed])->delete();
					}

					if($qrHash !== $this->invoiceModel->getQrHash($invoice)){
						$this->invoiceHistoryModel->addMessage('Upravená faktúra', $invoice->id, $this->getUser());
					}

					/*/** @var InvoiceEvents onUpdate
					$this->em->trigger("invoice.update", $invoice);*/
					$this->invoiceModel->toExpenseDocuments($invoice);
					//$redirect = null;

					$this->flashMessage("Faktúra bola upravená.");
				}
				$this->invoiceModel->toDocuments($invoice);
				$invoice->update(["qr_hash" => $this->invoiceModel->getQrHash($invoice)]);

				if($pair !== null){
					bdump($pair);
					bdump($invoice);
					$rowPair = $this->invoicePairModel->insert(["invoice" => $invoice->id, "invoice_pair" => $pair]);
					bdump($rowPair);
					//exit;
				}



				$redirect = "Invoice:detail";
				$redirectParams = ["id" => $invoice->id];
				$db->commit();
			} catch (\Throwable $e){
				$db->rollBack();
				$this->flashMessage($this->logException($e), Message::LEVEL_ERROR, "Pri ukladaní sa vyskytol problém.");
			}
			if(isset($redirect)) {
				$this->redirect($redirect, $redirectParams ?? []);
			}
		};
		return $f;
	}



	public function createComponentInvoiceGrid($name) : DataGrid
	{
		$factory = $this->invoiceGrid->setIsVatPayer($this->isVatPayer)->setSupplier($this->supplier);
		return $factory->create($this, $name);
	}

	public function createComponentModalInvoiceSequenceForm($name) : Form
	{
		$this->modalInvoiceSequenceForm->setSupplierId($this->supplierId);
		$this->modalInvoiceSequenceForm->setInvoiceTypes($this->invoiceTypes);
		$this->modalInvoiceSequenceForm->setInvoiceType($this->invoiceType);
		$form = $this->modalInvoiceSequenceForm->create($this, $name);
		$form->onSuccess[] = function (){
			$this->initSequence();
			$this->flashMessage("Číselnik bol nastavený.");
			$this->getComponent("invoiceGrid")->reload();
			$this->redrawControl("years");
			$this->payload->closeModal = 1;
		};
		return $form;
	}

	public function createComponentModalInvoiceSupplierEditForm($name) : Form
	{
		$this->modalInvoiceSupplierEditForm->setSupplierIbans($this->supplierIbans);
		return $this->modalInvoiceSupplierEditForm->create($this,$name, function (Form $form, ArrayHash $values){
			if(empty($form->getErrors())){
				$this->payload->closeModal = 1;
				$this->flashMessage("Údaje na faktúre boli upravené.", Message::LEVEL_SUCCESS);
			} else {
				$this->flashMessage(implode("<br>", $form->getErrors()), Message::LEVEL_ERROR,"Pri ukladaní nastala chyba");
			}
		});
	}


	public function getPaymentButton(ActiveRow $invoice, float $paid, float $amount) : ?\Latte\Runtime\Html
	{
		if($paid < $amount){
			$paymentDefaults = Json::encode([
				"volume" => $amount - $paid ,
				"invoice" => $invoice->id,
				"datetime" => (new DateTime())->format("d.m.Y"),

			]);
			$paymentButton = Html::el("a")
			                     ->setAttribute("href", $this->link("this"))
			                     ->setAttribute("data-toggle","modal")
			                     ->setAttribute("data-target","#modalInvoicePayment")
			                     ->setAttribute("data-form","modalInvoicePaymentForm")
			                     ->setAttribute("data-pv",$paymentDefaults)
			                     ->setAttribute("data-title","Pridať úhradu faktúre č. ".$invoice->number)
			                     ->setAttribute("class","btn btn-block btn-light btn-with-icon modal-open")
			                     ->addHtml('<div class="ht-40"><span class="icon wd-40"><i class="fas fa-euro-sign"></i></span><span class="pd-x-15">Pridať úhradu</span></div>');

			return new \Latte\Runtime\Html($paymentButton);
		}

		return null;
	}
}

