<?php

namespace BackendModule\InvoiceModule;


use App\Api\V2\Facade\InvoiceFacade;
use App\Api\V2\Request\Dto\CreateInvoiceReqDto;
use App\ORM\Entity\InvoicePaymentType;
use App\ORM\Repository\InvoicePaymentTypeRepository;
use App\Payments\Barion;
use App\Utils\BarionPayment;
use BackendModule\InvoiceModule\BasePresenter;
use IPub\FlashMessages\Entities\Message;
use Kubomikita\Attributes\Description;
use Kubomikita\Utils\DateTime;
use <PERSON><PERSON><PERSON>kita\Utils\InvoiceSequenceVariable;
use Kubo<PERSON>kita\Utils\Strings;
use Nette\Application\BadRequestException;
use Nette\Database\Table\ActiveRow;
use Nette\DI\Attributes\Inject;
use Nette\Utils\Random;
use Tracy\Debugger;
use <PERSON>\ILogger;
use Webtec\Models\InvoicePlanOrderModel;

#[Description("Objednať predplatné")]
class BuyPresenter extends BasePresenter {

	const SESSION_NAMESPACE = "invoicing_buy";
	const LENGTH = [
		"10year" => "10 rokov",
		"5year" => "5 rokov",
		"2year" => "2 roky",
		"year" => "12 mesiacov",
		"half" => "6 mesiacov",
		"quarter" => "3 mesiace",
		"month" => "1 mesiac",
	];
	const LENGTH_TO_DATE = [
		"10year" => "+10 year",
		"5year" => "+5 year",
		"2year" => "+2 year",
		"year" => "+1 year",
		"month" => "+1 month",
		"half" => "+6 month",
		"quarter" => "+3 month"
	];

	const LENGTH_CLASS = [
		"year" => "success",
		"month" => "secondary",
		"half" => "info",
		"quarter" => "info"
	];
	const LENGTH_TO_MONTH = [
		"10year" => 120,
		"5year" => 60,
		"2year" => 24,
		"year" => 12,
		"month" => 1,
		"half" => 6,
		"quarter" => 3
	];

	const PAYMENTS = [
		"transfer" => [
			"name" => "Bankový prevod",
			"icon" => 'fa-solid fa-building-columns',
		],
		"barion" => [
			"name" => "Platba kartou BARION",
			"icon" => 'fa-brands fa-cc-visa',
			"callback" => [Barion::class, "isInvoicingAllowed"],
			'desc' => '<img src="/assets/theme/default/css/barion-card.svg" class="img-fluid">'
		],
		"trustpay" => [
			"name" => 'Platba kartou',
            "icon" => 'fa-brands fa-cc-visa',
			"hidden" => true
		],
		'without' => [
			'name' => 'Bez úhrady',
			'hidden' => true,
		]
	];

	public ActiveRow $invoicingSupplier;
	public InvoiceSequenceVariable $invoicingSequence;
	public InvoiceSequenceVariable $invoicingProformaSequence;
	#[Inject]
	public InvoicePlanOrderModel $invoicePlanOrderModel;

	/** @var InvoicePaymentType[] */
	public array $payments;
	public function startup()
	{
		parent::startup();

		if(($invoicing_supplier_id = $this->getCompany()->invoicing_supplier) === null){
			$this->flashMessage("Nieje vybratá firma pre predaj FA systému.", Message::LEVEL_ERROR);
			$this->redirect(":Backend:Invoice:Subscription:default");
		}

		$this->invoicingSupplier = $this->invoiceSupplierModel->find($invoicing_supplier_id)->fetch();
		$this->invoicingSequence = new InvoiceSequenceVariable($this->invoiceSequenceModel->find($this->getCompany()->invoicing_sequence)->fetch());

		$this->template->invoicingSupplier = $this->invoicingSupplier;
		$this->template->invoicingSupplierVatPayer = $this->invoiceSupplierModel->isVatPayer($this->invoicingSupplier);


		$this->invoicingProformaSequence = new InvoiceSequenceVariable($this->invoiceSequenceModel->find($this->getCompany()->invoicing_proforma_sequence)->fetch());


		$this->payments = $this->entityManager->getRepository(InvoicePaymentType::class)->findByPairs("ident", ["ident" => array_keys(self::PAYMENTS)]);
		//bdump($this->payments);
	}

	public function actionDefault(){
		$this->template->buyPlans = $this->invoicePlanModel->findAll()->whereOr(["price_month IS NOT NULL", "price_year IS NOT NULL","price_half IS NOT NULL","price_quarter IS NOT NULL"])->order("price_month ASC");
		$this->getSession(self::SESSION_NAMESPACE)->remove("plan_id");
		$this->getSession(self::SESSION_NAMESPACE)->remove("length");
	}

	public function handleSelectPlan(int $id){
		$this->getSession(self::SESSION_NAMESPACE)->set("plan_id", $id);

		$this->redirect("Buy:length");
	}

	public function actionLength(){
		if(($plan_id = $this->getSession(self::SESSION_NAMESPACE)->get("plan_id")) === null){
			$this->flashMessage("Najprv vyberte plán.", Message::LEVEL_ERROR);
			$this->redirect("Buy:default");
		}
		$this->getSession(self::SESSION_NAMESPACE)->remove("length");
		$this->template->p  = $plan  = $this->invoicePlanModel->find($plan_id)->fetch();

		$options = [];
		foreach (static::LENGTH as $key => $value){
			$price = $plan->{"price_".$key};
			if($price !== null){
				$options[$key]["textPeriod"] = $value;
				$options[$key]["price"] = $price;
				$options[$key]["class"] = static::LENGTH_CLASS[$key] ?? "dark";
				if($plan->price_month !== null) {
					$options[$key]["priceMonth"] = round($price / static::LENGTH_TO_MONTH[$key], 2);
					$options[$key]["discount"] = round(($plan->price_month * static::LENGTH_TO_MONTH[$key]) - $price, 2);
				}
			}
			//bdump($price);
		}

		$this->template->options = $options;
		//bdump($options);

	}

	public function handleSelectLength(string $length){
		$this->getSession(self::SESSION_NAMESPACE)->set("length", $length);
		//$this->getSession(self::SESSION_NAMESPACE)->set("lengthName", );
		$plan_id = $this->getSession(self::SESSION_NAMESPACE)->get('plan_id');
		$plan = $this->invoicePlanModel->find($plan_id)->fetch();
		if($plan->{'price_'.$length} == 0){
			$this->handleSelectPayment('without');
		} else {
			$this->redirect("Buy:payment");
		}
	}

	public function actionCredentials(){
		if(($plan_id = $this->getSession(self::SESSION_NAMESPACE)->get("plan_id")) === null || ($length = $this->getSession(self::SESSION_NAMESPACE)->get("length")) === null){
			$this->flashMessage("Najprv vyberte dlžku predplatného.", Message::LEVEL_ERROR);
			$this->redirect("Buy:length");
		}
	}

	public function actionPayment() {
		if(($plan_id = $this->getSession(self::SESSION_NAMESPACE)->get("plan_id")) === null || ($length = $this->getSession(self::SESSION_NAMESPACE)->get("length")) === null){
			$this->flashMessage("Najprv vyberte dlžku predplatného.", Message::LEVEL_ERROR);
			$this->redirect("Buy:length");
		}
		$this->getSession(self::SESSION_NAMESPACE)->remove("payment");

		$this->template->p = $this->invoicePlanModel->find($plan_id)->fetch();
		$this->template->length = self::LENGTH[$length];
		$this->template->price = $this->template->p->{'price_'.$length};

		$this->template->payments = [];
		foreach (self::PAYMENTS as $key => $PAYMENT){
			$hidden = $PAYMENT["hidden"] ?? false;
			if(isset($PAYMENT["callback"])){
				$hidden = !$PAYMENT["callback"]($this->companyService);
			}
			if(!$hidden){
				$this->template->payments[$key] = $PAYMENT;
			}
		}

	}

	public function handleSelectPayment(string $payment){
		$this->getSession(self::SESSION_NAMESPACE)->set("payment", $payment);
		$this->redirect("Buy:summary");
	}

	public function actionSummary(){
		if(
			($plan_id = $this->getSession(self::SESSION_NAMESPACE)->get("plan_id")) === null ||
		    ($length = $this->getSession(self::SESSION_NAMESPACE)->get("length")) === null ||
			($payment = $this->getSession(self::SESSION_NAMESPACE)->get("payment")) === null
		){
			$this->flashMessage("Najprv vyberte spôsob úhrady.", Message::LEVEL_ERROR);
			$this->redirect("Buy:payment");
		}

		$this->template->p = $this->invoicePlanModel->find($plan_id)->fetch();
		$this->template->length = self::LENGTH[$length];
		$this->template->price = $this->template->p->{'price_'.$length};
		$this->template->payment = self::PAYMENTS[$payment]["name"]; // == 'transfer' ? "Bankový prevod" : ($payment == "trustpay" ? "Platba kartou" : null);
	}

	public function handleConfirm() {
		if(
			($plan_id = $this->getSession(self::SESSION_NAMESPACE)->get("plan_id")) === null ||
			($length = $this->getSession(self::SESSION_NAMESPACE)->get("length")) === null ||
			($payment = $this->getSession(self::SESSION_NAMESPACE)->get("payment")) === null
		){
			$this->flashMessage("Najprv vyberte spôsob úhrady.", Message::LEVEL_ERROR);
			$this->redirect("Buy:payment");
		}

		$plan = $this->invoicePlanModel->find($plan_id)->fetch();
		if(!$plan){
			throw new BadRequestException();
		}




		$lengthName = self::LENGTH[$length];
		$price = $plan->{'price_'.$length};

		$db = $this->invoiceModel->getDatabaseConnection();
		$success = true;

		if($price > 0) {

			try {
				$db->beginTransaction();
				$invoiceFacade = clone $this->context->getByType(InvoiceFacade::class);
				$invoiceFacade->setInvoiceSupplier($this->invoicingSupplier);

				$dto = new CreateInvoiceReqDto();
				$dto->fromArray([
					"invoiceType"     => "proforma",
					"invoiceSequence" => $this->invoicingProformaSequence->id,
					"dateCreated"     => (new DateTime())->format("Y-m-d"),
					"dateDelivery"    => (new DateTime())->format("Y-m-d"),
					"dateDue"         => (new DateTime())->modifyClone("+1 week")->format("Y-m-d"),
					"paymentType"     => $payment,
					"client"          => [
						"name"    => $this->supplier->name,
						"ico"     => $this->supplier->ico,
						"dic"     => $this->supplier->dic,
						"icDph"   => $this->supplier->icdph,
						"address" => $this->supplier->address,
						"city"    => $this->supplier->city,
						"zip"     => $this->supplier->zip,
						"country" => $this->supplier->country,
						"email"   => $this->supplier->ref("user")->username,
					],
					"items"           => [
						[
							"name"       => Strings::firstUpper($plan->name) . " na " . $lengthName,
							"quantity"   => 1,
							"unit"       => "ks",
							"unit_price" => $price,
							"tax"        => $this->template->invoicingSupplierVatPayer ? 20 : 0,

						]
					]
				]);

				$proformaCreated = $invoiceFacade->create($dto);

				$proforma = $this->invoiceModel->find($proformaCreated["invoiceId"])->fetch();
				$proforma->update(["proforma_to_regular_sequence" => $this->invoicingSequence->id]);


				$this->invoiceModel->sendToEmail($proforma, $this->supplier->ref("user")->username);
				$proforma->update(["sended" => new DateTime()]);
				$this->invoiceHistoryModel->addMessage("Automaticky odoslaná na " . $this->supplier->ref("user")->username,
					$proforma->id);
				//$db->rollBack();
				$db->commit();
			} catch (\Throwable $e) {
				$db->rollBack();;
				Debugger::log($e, ILogger::EXCEPTION);
				$success = false;
			}
		}

		$order = $this->invoicePlanOrderModel->insert([
			"invoice_supplier" => $this->supplierId,
			"user"             => $this->getUser()->getId(),
			"datetime"         => new DateTime(),
			"invoice_plan"     => $plan->id,
			"plan"             => $plan->name,
			"price"            => $price,
			"period"           => $lengthName,
			"periodType"       => $length,
			"payment"          => $payment,
			"invoice_proforma" => $proforma->id ?? null,
		]);

		if(isset($order)){
			$this->redirect("Buy:ordered", ["order_no" => $order->id]);
		}
	}

	public function actionOrdered(int $order_no){

		$order = $this->invoicePlanOrderModel->find($order_no)->select("*")->fetch();
		if(!$order){
			throw new BadRequestException();
		}

		$this->getSession(self::SESSION_NAMESPACE)->remove("payment");
		$this->getSession(self::SESSION_NAMESPACE)->remove("plan_id");
		$this->getSession(self::SESSION_NAMESPACE)->remove("length");

		$this->template->order = $order;
		$proforma = $order->ref("invoice_proforma");
		if($proforma !== null) {
			$this->template->proforma = $this->invoiceModel->getDetailData($proforma);
		}
		/** @var InvoicePaymentType $invoicePaymentType */
		$invoicePaymentType = $this->entityManager->getRepository(InvoicePaymentType::class)->findOneBy(["ident" => $order->payment]);

		if($invoicePaymentType?->online) {
			$this->template->paymentUrl = $this->link("Buy:createPaymentUrl", ["order_no" => $order_no]);
		}

		if($proforma === null && $order->price == 0){
			// TODO: zjednotit do jednej entity
			$last_invoice_plan = $order->ref("invoice_supplier")->related("invoice_supplier_plan")->order("date_end DESC")->fetch();
			if($last_invoice_plan->date_end->getTimestamp() > time()) {
				$dateStart = $last_invoice_plan->date_end;
			} else {
				$dateStart = new DateTime();
			}
			$dateEnd = $dateStart->modifyClone(BuyPresenter::LENGTH_TO_DATE[$order->periodType]);

			$this->invoiceSupplierPlanModel->insert([
				"invoice_supplier"   => $order->invoice_supplier,
				"invoice_plan"       => $order->invoice_plan,
				"name"               => Strings::firstUpper($order->plan) . " na " . $order->period,
				"price"              => $order->price,
				"date_start"         => $dateStart,
				"date_end"           => $dateEnd,
				"invoice_plan_order" => $order->id,
				"payment_type"       => $order->payment,
				//"invoice"            => $related->id,
			]);
		}
	}

	public function actionCreatePaymentUrl(int $order_no){
		$order = $this->invoicePlanOrderModel->find($order_no)->select("*")->fetch();
		if(!$order){
			throw new BadRequestException();
		}
		if($order->payment === "barion") {
			$proforma = $order->ref("invoice_proforma");
			$proformaDetails = $this->invoiceModel->getDetailData($proforma);

			/** @var Barion $barion */
			$barion = $this->context->getByType(Barion::class);
			$barion->createClient($this->company->invoicing_barion_posKey,$this->company->invoicing_barion_userEmail);

			$this->redirectUrl($barion->preparePayment($proforma->number, $proforma->variable, $this->getUser()->getIdentity()->username, $proformaDetails["amount"], [["name" => $order->plan." na ".$order->period, "quantity" => 1, "unit" => "ks", "unitPrice" => $proformaDetails["amount"], "totalPrice" => $proformaDetails["amount"]]]));

		}
	}
}