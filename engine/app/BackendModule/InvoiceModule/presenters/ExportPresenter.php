<?php

namespace BackendModule\InvoiceModule;

use App\Forms\ExportIsdocForm;
use App\Forms\ExportMrpForm;
use App\Forms\ExportOmegaForm;
use App\Forms\ExportPohodaForm;
use Kubomikita\Attributes\Description;
use Nette\DI\Attributes\Inject;
use Webtec\UI\Forms\Form;

#[Description("Exporty")]
class ExportPresenter extends BasePresenter {

	public string $svgPath = 'assets/admin/img/accounting/';
	public array $exports = [
		"omega.svg" => 	[
			"modal" => 'modalExportOmega'
		],
		"isdoc.svg" => [
			"modal" => 'modalExportIsdoc',
			"title" => 'Export do formátu ISDOC',
		],
		"mksoft.svg" => [
			"modal" => 'modalExportIsdoc',
			"title" => "Export agendy do MkSoft"
		],
		"pohoda.svg" => [
			"modal" => 'modalExportPohoda',
			"title" => "Export do Pohody"
		],
		"mrp.svg" => [
			"modal" => 'modalExportMrp',
			//"active" => 0,
			"title" => "Export do MRP"
		],
		"money-new.svg" => [
			"modal" => 'modalExportCommingSoon',
			"active" => 0,
			"title" => "Export do Money"
		],
		"oberon.svg" => [
			"modal" => 'modalExportCommingSoon',
			"active" => 0,
			"title" => "Export do Oberon"
		],
		"tangram.svg" => [
			"modal" => 'modalExportCommingSoon',
			"active" => 0,
			"title" => "Export do Tangram"
		]
	];
	#[Inject]
	public ExportOmegaForm $exportOmegaForm;
	#[Inject]
	public ExportIsdocForm $exportIsdocForm;
	#[Inject]
	public ExportMrpForm $exportMrpForm;
	#[Inject]
	public ExportPohodaForm $exportPohodaForm;

	public function actionDefault(){
		$this->template->svgPath = $this->svgPath;
		$this->template->exports = $this->exports;
	}

	public function handleCountInvoices(){
		if(($period = $this->getHttpRequest()->getQuery("period")) !== null && ($type = $this->getHttpRequest()->getQuery("type")) !== null && ($ident = $this->getHttpRequest()->getQuery("ident")) !== null) {
			bdump($ident);
			$this->{"export".$ident."Form"}->setSupplier($this->supplier);
			$this->template->invoiceCount = $this->{"export".$ident."Form"}->getSelection($period, $type)->count("id");
			$this->template->invoiceType = $type === "invoice" ? "faktúry" : "náklady";
			$this->redrawControl("invoiceCount".$ident);
		}
	}

	public function createComponentModalExportOmegaForm($name) : Form
	{
		$this->exportOmegaForm->setSupplier($this->supplier);
		return $this->exportOmegaForm->create($this, $name);
	}
	public function createComponentModalExportIsdocForm($name) : Form
	{
		$this->exportIsdocForm->setSupplier($this->supplier);
		return $this->exportIsdocForm->create($this, $name);
	}

	public function createComponentModalExportMrpForm($name) : Form
	{
		$this->exportMrpForm->setSupplier($this->supplier);
		return $this->exportMrpForm->create($this, $name);
	}

	public function createComponentModalExportPohodaForm($name) : Form
	{
		$this->exportPohodaForm->setSupplier($this->supplier);
		return $this->exportPohodaForm->create($this, $name);
	}

	//public function

	public function actionOmega(){

	}
}