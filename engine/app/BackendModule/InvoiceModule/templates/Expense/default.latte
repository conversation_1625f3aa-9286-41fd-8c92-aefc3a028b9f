{var $title = _("Náklady")}
{block content}

<div class="card bd-0 invoice-container" n:ifset="$supplierId">
    {include "../@submenu.latte"}

    <div class="card-body" n:snippetArea="expenseList">
        <div n:snippet="years">
            <div class="row no-gutters mb-3 align-items-center">
                <div class="col-auto pr-3" n:if="!empty($selections)">

                    <div class="btn-group btn-group-sm" role="group">
                        <a type="button" n:href="changeSelection!, type => $k"  data-spinner=".expense-grid-container" class="btn btn-xs btn-light ajax {if $selection===$k }active tx-bold{/if}" n:foreach="$selections as $k => $s">{$s}</a>
                    </div>

                </div>
                <div class="col-auto pr-3" n:if="!empty($years)">
                    Rok:
                    <div class="btn-group btn-group-sm" role="group">
                        <a type="button" n:href="changeYear!, year => $ykey"  data-spinner=".expense-grid-container" class="btn btn-xs btn-light ajax {if $year===$ykey}active tx-bold{/if}" n:foreach="$years as $ykey => $y">{$y}</a>
                    </div>
                </div>
                <div class="col-auto pr-3 d-flex align-items-center"  n:if="!empty($months)">
                    Mesiac vytvorenia:
                    <div class="pl-2" style="min-width: 110px;">
                        <select onchange="javascript:changeMonth('monthIssue',this.value);" class="form-control form-control-sm select2">
                            {foreach $months as $id => $acc}
                                <option value="{$id}" {if $monthIssue===$id}selected{/if}>
                                    {$acc}
                                </option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="col-auto pr-3 d-flex align-items-center"  n:if="!empty($months)">
                    Mesiac dodania:
                    <div class="pl-2" style="min-width: 110px;">
                        <select onchange="javascript:changeMonth('monthDelivery',this.value);" class="form-control form-control-sm select2">
                            {foreach $months as $id => $acc}
                                <option value="{$id}" {if $monthDelivery===$id}selected{/if}>
                                    {$acc}
                                </option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="col-auto pr-3 d-flex align-items-center"  n:if="!empty($months)">
                    Mesiac splatnosti:
                    <div class="pl-2" style="min-width: 110px;">
                        <select onchange="javascript:changeMonth('monthDue',this.value);" class="form-control form-control-sm select2">
                            {foreach $months as $id => $acc}
                                <option value="{$id}" {if $monthDue===$id}selected{/if}>
                                    {$acc}
                                </option>
                            {/foreach}
                        </select>
                    </div>
                </div>
            </div>
            <div n:if="!empty($alerts)">
                <div n:foreach="$alerts as $alert" class="alert alert-danger mb-1">{$alert|noescape}</div>
            </div>
            {var $hasInvoices = $presenter['expenseGrid']->getDataSource()->getCount() > 0}

            <div n:if="!$hasInvoices">
                <div class="alert alert-info"><i class="fas fa-info-circle"></i> Zadanému filtru nezodpovedajú žiadne doklady. <a n:if="$presenter->invoiceType !== 'cancel'" n:href=":Backend:Invoice:Expense:upload, invoiceType: $presenter->invoiceType">Pokračujte vytvorením prvého nákladu...</a></div>
                <a n:if="$years !== null && count($years) > 2" class="ajax btn btn-primary" n:href="changeYear!, year: ($year - 1)">Zobraziť doklady za minulý rok</a>

                <div class=" my-3 my-lg-4 text-center">
                    <a n:href=":Backend:Invoice:Expense:add" class="btn btn-success btn-p">Vytvoriť náklad</a>
                    <a n:if="$years !== null" n:href=":Backend:Invoice:Expense:upload" class="btn btn-primary btn-p">Nahrať viac nákladov</a>
                </div>
            </div>
            <div class="expense-grid-container">
            <div n:if="empty($alerts) && $hasInvoices">
                {control expenseGrid}
            </div>
            </div>
        </div>
    </div>
</div>

<div id="modalExpenseMailDetail" class="modal fade" aria-hidden="true" style="display: none;">

    <div class="modal-dialog modal-dialog-semiwide modal-dialog-centered" role="document">
        <div class="modal-content bd-0 tx-14">
            <div class="modal-header bg-gray-400 pd-y-20 pd-x-25">
                <h6 class="tx-14 mg-b-0 tx-inverse tx-bold">
                    Detail e-mailovej správy
                </h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body pd-25">
                <div class="mb-3">
                    <strong>Predmet:</strong>
                    <div data-content-id="subject"></div>
                </div>
                <div class="mb-3">
                    <strong>Správa:</strong>
                    <div data-content-id="message"></div>
                </div>

            </div>
            <div class="modal-footer d-block">
                <div class="row no-gutters">
                    <div class="col-12 text-center text-lg-right">

                        <button type="button" class="btn btn-danger" data-dismiss="modal">{_"Zavrieť"}</button>
                    </div>
                </div>

            </div>
        </div>
    </div>

</div>

{/block}

{block scripts}
<script>
    function changeMonth(column, month){
	    spinnerOn(1,".expense-grid-container");
	    $.nette.ajax({
		    url: {link changeMonth!},
		    data: {
			    month: month,
                column: column
		    }
	    })
    }
</script>
{/block}