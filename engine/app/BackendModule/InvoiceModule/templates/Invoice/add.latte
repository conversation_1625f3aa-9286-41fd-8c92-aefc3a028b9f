{block content}

    <div class="card bd-0 invoice-container" n:ifset="$supplierId">
        {include "../@submenu.latte"}
        <div class="" n:snippetArea="invoice">

            <div class="pl-4 pt-4">
                <h3>{$title}</h3>
            </div>
            <div class="card-body" n:snippet="itemsSnippet">
                {form invoiceForm, class=>"ajax"}
                    <div class="{$presenter->getBlockClass($hidden, 'recurring-container', "card mb-3")}" id="recurring-container">
                        <div class="card-header tx-medium bg-gray-200">
                            Nastavenia pravidelnej faktúry
                        </div><!-- card-header -->
                        <div class="card-body">

                            <div class="form-label-group">
                                {input recurring_frequency, class => "form-control"}
                                {label recurring_frequency /}
                            </div>
                            <div class="form-label-group">
                                {input recurring_date, class => "form-control datepicker"}
                                {label recurring_date /}
                            </div>
                            <div class="d-none"> {* TODO: doriesit aj len ciastocne vystavenia *}
                                <div class="form-group">
                                    {input recurring_infinity, class => "form-control"}
                                    {label recurring_infinity /}
                                </div>
                                <div id="recurring-count-container">
                                <div class="form-label-group">
                                    {input recurring_count, class => "form-control "}
                                    {label recurring_count /}
                                </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="ckbox ckbox-primary mb-0">
                                    {$form["recurring_send"]->getControlPart()}
                                    <span>{$form["recurring_send"]->getLabelPart()|stripHtml}</span>
                                </label>
                            </div>
                            <div id="recurring-email-container">
                                <div class="form-label-group">
                                    {input recurring_email, class => "form-control"}
                                    {label recurring_email /}
                                </div>
                            </div>
                            <div id="paypal-container" class="{$presenter->getBlockClass($hidden, 'paypal-container', "mr-3")}">
                                <label class="ckbox ckbox-dark">
                                    {$form["show_paypal"]->getControlPart()}
                                    <span>{$form["show_paypal"]->getLabelPart()|stripHtml}</span>
                                </label>
                            </div>

                            <h6 class="mt-4">Bankové účty</h6>
                            {*php dump($form["accounts"])*}
                            <div class="form-group" n:foreach="$form['recurring_accounts']->getComponents() as $item">
                                {*php dump($item->getName(), $supplierIbans[$item->getName()])*}
                                <label class="ckbox ckbox-dark d-flex align-items-center">
                                    {$item->getControlPart()}
                                    <span class="ml-1">
                                        <div class="tx-bold">{$supplierIbans[$item->getName()]->name} {ifset $supplierIbans[$item->getName()]->desc}<span class="tx-black">[{$supplierIbans[$item->getName()]->desc}]</span>{/ifset}</div>
                                            <div>{$supplierIbans[$item->getName()]->iban}</div>
                                        </span>
                                </label>
                            </div>

                        </div>

                        <div class="card-header tx-normal tx-black bg-gray-200">
                            V pravidelnej faktúre môžete použiť nasledovné skratky, ktoré budú nahradené aktuálnymi hodnotami v čase vystavenia faktúry:
                        </div><!-- card-header -->
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-4">
                                    <ul>
                                        <li><strong class="tx-black">{l}$number{r}</strong>  Číslo dokladu</li>
                                        <li><strong class="tx-black">{l}$variable{r}</strong> Variabilný symbol</li>
                                        <li><strong class="tx-black">{l}$year{r}</strong> Aktuálny rok</li>
                                    </ul>
                                </div>
                                <div class="col-lg-4">
                                    <ul>
                                        <li><strong class="tx-black">{l}$nextYear{r}</strong> Nasledujúci rok</li>
                                        <li><strong class="tx-black">{l}$lastYear{r}</strong> Predchádzajúci rok</li>
                                        <li><strong class="tx-black">{l}$month{r}</strong> Aktuálny mesiac</li>
                                    </ul>
                                </div>
                                <div class="col-lg-4">
                                    <ul>
                                        <li><strong class="tx-black">{l}$nextMonth{r}</strong> Nasledujúci mesiac</li>
                                        <li><strong class="tx-black">{l}$lastMonth{r}</strong> Predchádzajúci mesiac</li>
                                        <li><strong class="tx-black">{l}$day{r}</strong>  Ďeň vystavenia</li>
                                    </ul>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="card mb-3">
                        <div class="card-header tx-medium bg-gray-200">
                            Všeobecné informácie
                        </div><!-- card-header -->
                        <div class="card-body">
                            <div class="row no-gutters">
                                <div class="col-xl-8">
                                    <div n:class="count($sequencesList) > 1 ? 'row no-gutters align-items-center':'d-none'">
                                        <div class="col-lg-6 pr-lg-2">
                                            <div class="form-label-group">
                                                {input invoice_sequence, class => "form-control"}
                                                {label invoice_sequence /}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row no-gutters align-items-center">
                                        <div class="{$presenter->getBlockClass($hidden, 'number-container', "col-lg-6 pr-lg-2")}" id="number-container">
                                            <div >
                                            <div class="form-label-group">
                                                {input number, class => "form-control"}
                                                {label number /}
                                            </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 pr-lg-2">
                                            <div class="{$presenter->getBlockClass($hidden, 'recurring-date-delivery-container', "form-label-group")}" id="recurring-date-delivery-container">
                                                {*input date_created, class => "form-control datepicker"}
                                                {label date_created /*}

                                                {input recurring_date_delivery, class => "form-control"}
                                                {label recurring_date_delivery /}
                                            </div>
                                            <div class="{$presenter->getBlockClass($hidden, 'date-created-container', "form-label-group")}" id="date-created-container">
                                                {input date_created, class => "form-control datepicker"}
                                                {label date_created /}
                                            </div>
                                        </div>
                                        {*<div class="{$presenter->getBlockClass($hidden, 'date-created-container', "col-lg-6 pr-2")}" id="date-created-container">
                                            <div class="form-label-group">
                                                {input date_created, class => "form-control datepicker"}
                                                {label date_created /}
                                            </div>
                                        </div>*}
                                        <div class="{$presenter->getBlockClass($hidden, 'variable-container', "col-lg-6 pr-lg-2")}" id="variable-container">
                                            <div >
                                            <div class="form-label-group">
                                                {input variable, class => "form-control"}
                                                {label variable /}
                                            </div>
                                            </div>
                                        </div>

                                        <div class="col-lg-6 pr-lg-2" >
                                            <div >
                                                <div class="{$presenter->getBlockClass($hidden, 'date-delivery-container', "form-label-group")}" id="date-delivery-container">
                                                    {input date_delivery, class => "form-control datepicker"}
                                                    {label date_delivery /}
                                                </div>
                                                <div class="{$presenter->getBlockClass($hidden, 'recurring-due-container', "form-label-group")}" id="recurring-due-container">
                                                    {input recurring_due_days, class => "form-control"}
                                                    {label recurring_due_days /}

                                                </div>
                                            </div>
                                        </div>



                                        <div class="col-lg-6 pr-lg-2">
                                            <div class="form-label-group">
                                                {input name, class => "form-control"}
                                {label name /}
                                            </div>
                                        </div>
                                        <div class="col-lg-6 pr-lg-2">
                                            <div id="due-select-container" n:class="$presenter->getAction() !== 'edit' ? 'form-label-group' : 'd-none'">
                                                {input due, class => "form-control select2"}
                                                {label due /}
                                            </div>
                                            <div >
                                                <div class="{$presenter->getBlockClass($hidden, 'due-container', "form-label-group")}" id="due-container">
                                                    {input date_due, class => "form-control datepicker"}
                                                    {label date_due /}
                                                </div>
                                            </div>
                                        </div>

                                        <div class="{$presenter->getBlockClass($hidden, 'constant-container', "col-lg-3 pr-lg-2")}" id="constant-container">
                                            <div class="form-label-group">
                                                {input constant, class => "form-control"}
                                                {label constant /}
                                            </div>
                                        </div>
                                        <div class="{$presenter->getBlockClass($hidden, 'specific-container', "col-lg-3 pr-lg-2")}" id="specific-container">
                                            <div class="form-label-group">
                                                {input specific, class => "form-control"}
                                                {label specific /}
                                            </div>
                                        </div>
                                        <div class="{$presenter->getBlockClass($hidden, 'order-container', "col-lg-3 pr-lg-2")}" id="order-container">
                                            <div class="form-label-group">
                                                {input order_no, class => "form-control"}
                                                {label order_no /}
                                            </div>
                                        </div>
                                        <div class="col-lg-3  pr-lg-2 text-center" n:if="$presenter->getParameter('id') !== null">
                                            <div class="{$presenter->getBlockClass($hidden, 'edit-supplier-container')}" id="edit-supplier-container">
                                            <a href="#" class="d-inline-block mb-3 modal-open" data-toggle="modal" data-target="#modalInvoiceSupplierEdit"><i class="fas fa-pencil-alt"></i> Upraviť moje údaje na tomto doklade</a>
                                            </div>
                                        </div>

                                        <div class="{$presenter->getBlockClass($hidden, 'payment-container', "col-lg-4 pr-lg-2")}" id="payment-container">
                                            <div class="form-label-group">
                                                {input invoice_payment_type}
                                                {label invoice_payment_type /}
                                            </div>
                                        </div>
                                        <div class="{$presenter->getBlockClass($hidden, 'delivery-container', "col-lg-4 pr-lg-2")}" id="delivery-container">
                                            <div class="form-label-group">
                                                {input invoice_delivery_type}
                                                {label invoice_delivery_type /}
                                            </div>
                                        </div>

                                        <div class="col-12 pr-lg-2">
                                            <div class="form-label-group mb-lg-0">
                                                {input note_before, class => "form-control"}
                                                {label note_before /}
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="col-xl-4">
                                    <div class="form-label-group">
                                        {input invoice_client}
                                        {label invoice_client /}
                                    </div>
                                    <div class=" align-self-center ">
                                        <div n:snippet="client" class="pl-3 tx-dark">
                                            {if isset($client) || ($client = $presenter->getClient())}

                                                {$client->address}<br>
                                            {$client->zip} {$client->city}<br>
                                            {$client->country}
                                                <div class="my-3">
                                                    <div n:ifset="$client->ico">IČO: {$client->ico}</div>
                                                    <div n:ifset="$client->dic">DIČ: {$client->dic}</div>
                                                    <div n:ifset="$client->ic_dph">IČ DPH: {$client->ic_dph}</div>
                                                </div>

                                            {/if}
                                        </div>
                                        <div  class="pl-3">
                                            <a
                                                    n:href="Invoice:add"
                                                    data-toggle="modal"
                                                    data-target="#modalClient"
                                                    data-form="modalClientForm"
                                                    data-pv='{"id":"","user":"","name":"","ico":"","dic":"","ic_dph":"","email":"","address":"","city":"","zip":"","country":"Slovensko","phone":"","iban":""}'
                                                    data-title="Pridať odberateľa"
                                                    class="mb-3 d-inline-block"
                                            ><i class="fas fa-plus-circle"></i> Pridať odberateľa</a>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div><!-- card-body -->
                    </div>
                    <div class="card mb-3">
                        <div class="card-header tx-medium bg-gray-200">
                            Účtovné informácie
                        </div><!-- card-header -->
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-3">
                                    <div class="form-label-group">
                                        {input accounting_account}
                                        {label accounting_account/}
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-label-group">
                                        {input accounting_analytic_account}
                                        {label accounting_analytic_account/}
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header tx-medium bg-gray-200">
                            Položky dokladu
                        </div><!-- card-header -->
                        <div class="card-body">
                            <div n:multiplier="items">
                                {var $components = (array)$formContainer->getComponents()}
                                {*php dump($formContainer)*}
                                <div class="row no-gutters border-bottom mb-2 pb-2">
                                    <div class="col-lg pr-lg-1">
                                        <div class="form-label-group mb-1">
                                            {input name, class => "form-control form-control-sm form-row-name"}
                                            {label name /}
                                        </div>
                                        <div class="form-label-group mb-lg-0 mb-1">
                                            {input description, class: "form-control form-control-sm"}
                                            {label description /}
                                        </div>
                                    </div>
                                    <div class="col-lg-auto px-lg-1">
                                        <div class="form-label-group mb-1">
                                            {input quantity}
                                            {label quantity /}
                                        </div>
                                    </div>
                                    <div class="col-lg-auto px-lg-1">
                                        <div class="form-label-group mb-1" id="select-unit-container-{$formContainer->getName()}">
                                            {input unit}
                                            {label unit /}

                                        </div>
                                        <div class="form-label-group mb-1" id="own-unit-container-{$formContainer->getName()}">
                                            {input unit_own}
                                            {label unit_own /}
                                        </div>
                                        {*<div class="tx-12 text-center">
                                            <a href="#"><i class="fas fa-plus-circle"></i> Pridať vlastnú</a>
                                        </div>*}
                                    </div>
                                    <div class="col-lg-auto px-lg-1">
                                        <div class="form-label-group mb-1">
                                            {input unit_price}
                                            {label unit_price /}
                                        </div>
                                    </div>
                                    <div n:class="$isVatPayer ? 'col-lg-auto px-lg-1' : 'd-none'">
                                        <div class="form-label-group mb-1">
                                            {input tax}
                                            {label tax /}
                                        </div>
                                    </div>
                                    <div class="col-lg-auto px-lg-1">
                                        <div class="form-label-group mb-1">
                                            {input price, class=>"tx-bold form-control"}
                                            {label price /}
                                        </div>
                                    </div>

                                    <div n:class="!isset($components['multiplier_remover']) ? 'd-none' : 'col-lg-auto text-center'">{multiplier:remove}</div>
                                </div>
                            </div>

                            <div class="d-flex">
                                {multiplier:add $form['items']}

                                <div class="dropdown ml-2 add-rounding-button">
                                        <a class="btn btn-light btn-sm dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-expanded="false">
                                            Pridať zaokrúhlenie
                                        </a>

                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="javascript:Invoice.startAddRounding('math2one');">Matematicky na celé číslo</a>
                                            <a class="dropdown-item" href="javascript:Invoice.startAddRounding('math2tenth');">Matematicky na desatiny</a>
                                            <a class="dropdown-item" href="javascript:Invoice.startAddRounding('up2one');">Na celé číslo hore</a>
                                            <a class="dropdown-item" href="javascript:Invoice.startAddRounding('down2one');">Na celé číslo dole</a>
                                            <a class="dropdown-item" href="javascript:;" onclick="javascript:Invoice.startAddRounding('math5cent', {_'Zaokrúhlenie na 5 centov pre platbu v hotovosti'});">Na 5 centov</a>
                                        </div>
                                </div>
{*
                                <a href="javascript:Invoice.startAddRounding('math2one');" class=" btn btn-light btn-sm add-rounding-button">Pridať zaokrúhlenie na celé číslo</a>
                                <a href="javascript:Invoice.startAddRounding('math2tenth');" class=" btn btn-light btn-sm add-rounding-button">Pridať zaokrúhlenie na 10 tky centov</a>
                                <a href="javascript:Invoice.startAddRounding('up2one');" class=" btn btn-light btn-sm add-rounding-button">Pridať zaokrúhlenie na celé hore</a>
                                <a href="javascript:Invoice.startAddRounding('down2one');" class=" btn btn-light btn-sm add-rounding-button">Pridať zaokrúhlenie na celé dole</a>
                                }<a href="javascript:Invoice.startAddRounding('math5cent');" class=" btn btn-light btn-sm add-rounding-button">Pridať zaokrúhlenie na 5 centov</a>*}
                            </div>


                        </div><!-- card-body -->


                    </div>
                    <div class="row">
                        <div class="col-lg-8 mb-3">
                            <div class="form-label-group mb-0">
                                {input note_after, class => "form-control"}
                                {label note_after /}
                            </div>

                        </div>
                        <div class="col-lg-4">
                            <div class="card mb-3">
                                <div class="card-header tx-medium bg-primary tx-white">
                                    Sumár
                                </div><!-- card-header -->
                                <div class="card-body">

                                    <div id="invoice_summary" class="row tx-18 tx-black tx-normal">
                                        <div class="col-8" n:if="$isVatPayer">Základ DPH</div>
                                        <div class="col-4 tx-right" n:if="$isVatPayer"><span id="raw_total">0.00</span> €</div>
                                        <div  class="col-8" n:if="$isVatPayer">DPH</div>
                                        <div class="col-4 tx-right" n:if="$isVatPayer"><span id="vat_total">0.00</span> €</div>

                                        <div n:if="$form['show_payment_deposit']->getValue()"  class="col-8">Uhradená záloha</div>
                                        <div n:if="$form['show_payment_deposit']->getValue()" class="col-4 tx-right "><span id="invoice_deposit" data-deposit="{$form['show_payment_deposit_price']->getValue()}">{$form['show_payment_deposit_price']->getValue()|currency}</span></div>

                                        <div  class="col-8 tx-bold">Celková suma</div>
                                        <div class="col-4 tx-right tx-bold"><span id="invoice_total">0.00</span> €</div>

                                        <div n:if="$form['show_payment_deposit']->getValue()" class="col-8 tx-bold">Suma na úhradu</div>
                                        <div n:if="$form['show_payment_deposit']->getValue()" class="col-4 tx-right tx-bold"><span id="invoice_total_deposit">0.00</span> €</div>

                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="{$presenter->getBlockClass($hidden, 'contact-container', "card mb-3")}" id="contact-container">
                        <div class="card-header tx-medium bg-gray-200">
                            Kontaktné informácie
                        </div><!-- card-header -->
                        <div class="card-body">
                            <div class="row no-gutters">
                                <div class="col-lg-4">
                                    <div class="form-label-group">
                                        {var $userName = \Webtec\Models\UserModel::formatName($selfUser, false, false, true)}
                                        {input issued_by, class => "form-control"}
                                        {label issued_by /}
                                        <div n:if="strlen(trim($userName)) === 0" class="help-block ml-0 pt-2 tx-dark"><i class="fas fa-info-circle"></i> Ak si doplníte do <a n:href=":Backend:Client:Edit, id: $user->getId()">profilu</a> meno a priezvisko, vyplníme ho automaticky.</div>
                                    </div>
                                </div>
                                <div class="col-lg-4 px-lg-2">
                                    <div class="form-label-group">
                                        {input issued_by_phone, class => "form-control"}
                                        {label issued_by_phone /}
                                        <div n:if="$selfUser->name_list->phone === null || strlen(trim($selfUser->name_list->phone)) === 0" class="help-block ml-0 pt-2 tx-dark"><i class="fas fa-info-circle"></i> Ak si doplníte do <a n:href=":Backend:Client:Edit, id: $user->getId()">profilu</a> telefónne číslo, vyplníme ho automaticky.</div>
                                    </div>
                                    {*php dump($selfUser)*}
                                </div>
                                <div class="{*col-lg-3 pr-2*} d-none">
                                    <div class="form-label-group">
                                        {input issued_by_web, class => "form-control"}
                                {label issued_by_web /}
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="form-label-group">
                                        {input issued_by_email, class => "form-control"}
                                {label issued_by_email /}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="text-center">
                        {input save, class => "btn btn-p btn-success"}
                    </div>



                {/form}
            {*<div class="d-flex justify-content-center">
                <div class="page">
                    <h1>Faktúra {$sequence->getNextSequence()}</h1>
                    <div class="row no-gutters">
                        <div class="col-lg-6">
                            <div class="tx-bold">Dodávateľ:</div>
                            {$supplier->name}<br>
                            {$supplier->address}<br>
                            {$supplier->zip} {$supplier->city}<br>
                            {$supplier->country}
                            <div class="mt-3"></div>
                            <div>IČO: {$supplier->ico}</div>
                            <div>DIČ: {$supplier->dic}</div>
                            <div>IČ DPH: {$supplier->icdph}</div>
                            <div>{$supplier->business_register}</div>
                            {php dump($supplier)}
                        </div>
                        <div class="col-lg-6">
                            <div class="tx-bold">Odberateľ:</div>
                        </div>
                    </div>
                </div>

            </div>*}
            </div>
        </div>
    </div><!-- card -->
    <div id="modalInvoiceSupplierEdit" class="modal fade" aria-hidden="true" style="display: none;">
        {form modalInvoiceSupplierEditForm, class=>"ajax"}
            <div class="modal-dialog modal-dialog-centered modal-dialog-wide" role="document">
                <div class="modal-content bd-0 tx-14">
                    <div class="modal-header bg-gray-400 pd-y-20 pd-x-25">
                        <h6 class="tx-14 mg-b-0 tx-inverse tx-bold">
                            Dodávateľské údaje na faktúre
                        </h6>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body pd-25">



                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-label-group">
                                    {input supplier_name, class => "form-control"}
                                    {label supplier_name /}
                                </div>
                                <div class="form-label-group">
                                    {input supplier_ico, class => "form-control"}
                                    {label supplier_ico /}
                                </div>
                                <div class="form-label-group">
                                    {input supplier_dic, class => "form-control"}
                                    {label supplier_dic /}
                                </div>
                                <div class="form-label-group">
                                    {input supplier_icdph, class => "form-control"}
                                    {label supplier_icdph /}
                                </div>
                                <div class="form-label-group">
                                    {input supplier_address, class => "form-control"}
                                    {label supplier_address /}
                                </div>
                                <div class="form-label-group">
                                    {input supplier_zip, class => "form-control"}
                                    {label supplier_zip /}
                                </div>
                                <div class="form-label-group">
                                    {input supplier_city, class => "form-control"}
                                    {label supplier_city /}
                                </div>
                                <div class="form-label-group">
                                    {input supplier_country, class => "form-control"}
                                    {label supplier_country /}
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <h4 class="mb-3">Bankové účty</h4>
                                {*php dump($form["accounts"])*}
                                <div class="form-group" n:foreach="$form['accounts']->getComponents() as $item">
                                    {*php dump($item->getName(), $supplierIbans[$item->getName()])*}
                                    <label class="ckbox ckbox-dark d-flex align-items-center">
                                        {$item->getControlPart()}
                                        <span class="ml-1">
                                            <div class="tx-bold">{$supplierIbans[$item->getName()]->name} {ifset $supplierIbans[$item->getName()]->desc}<span class="tx-black">[{$supplierIbans[$item->getName()]->desc}]</span>{/ifset}</div>
                                            <div>{$supplierIbans[$item->getName()]->iban}</div>
                                        </span>
                                    </label>
                                </div>
                            </div>

                        </div>

                    </div>
                    <div class="modal-footer d-block">
                        <div class="row no-gutters">
                            <div class="col-12 text-center text-lg-right">
                                {input save, class=>"btn btn-primary btn-p"}
                                <button type="button" class="btn btn-danger" data-dismiss="modal">{_"Zavrieť"}</button>
                            </div>
                        </div>

                    </div>
                </div>
            </div><!-- modal-dialog -->
        {/form}
    </div>
    <style>
		/*:root {
			--bleeding: 0.5cm;
			--margin: 0.5cm;
		}
		.page {
			display: inline-block;
			position: relative;
			height: 297mm;
			width: 210mm;
			font-size: 10pt;
			margin: 2em auto;
			padding: calc(var(--bleeding) + var(--margin));
			box-shadow: 0 0 0.5cm rgba(0, 0, 0, 0.5);
			background: white;
		}

		@media screen {
			.page::after {
				position: absolute;
				content: '';
				top: 0;
				left: 0;
				width: calc(100% - var(--bleeding) * 2);
				height: calc(100% - var(--bleeding) * 2);
				margin: var(--bleeding);
				outline: thin dashed black;
				pointer-events: none;
				z-index: 9999;
			}
		}

		@media print {
			.page {
				margin: 0;
				overflow: hidden;
			}
		}*/
    </style>
        {*php dump($selectedInvoiceType)*}
    <script>

		class Invoice {

			prices = new Array();

			constructor() {
				//	window.prices = new Array();
				$("select > option[value='own']").addClass('own-option');

				for (var i = 0; i < document.forms.length; i++) {
					var form = document.forms[i];
					/*if(this.options.formClass !== null && !this.hasClass(form, this.options.formClass)){
                        continue;
                    }*/
					for (var j = 0; j < form.elements.length; j++) {
						if (form.elements[j].getAttribute('data-invoice-watcher')) {
							this.initForm(form);
							break;
						}
					}
				}

			}

			initForm(form) {
				for (var i = 0; i < form.elements.length; i++) {

					//this.setupHandlers(form.elements[i]);
					if(form.elements[i].getAttribute('data-invoice-watcher') !== null) {
						if (form.elements[i].getAttribute('data-invoice-watcher').indexOf("date_") !== -1) {
							this.setupDateHandlers(form.elements[i]);
						} else if(form.elements[i].getAttribute('data-invoice-watcher').indexOf("client") !== -1){
							this.setupClientHandlers(form.elements[i]);
						} else if(form.elements[i].getAttribute('data-invoice-watcher').indexOf("number") !== -1) {
							this.setupNumberHandlers(form.elements[i]);
						} else if(form.elements[i].getAttribute('data-invoice-watcher').indexOf("sequence") !== -1) {
							this.setupSequenceHandlers(form.elements[i]);
						} else {
							this.setupHandlers(form.elements[i]);
						}
					}
				}
			}

			setupNumberHandlers(el){
				if (el.getAttribute("data-invoice-initialized"))
					return;

				el.setAttribute('data-invoice-initialized', 'true');

				let self = this;
				let handler = function (event){
					let value = (event.target ? event.target : event.srcElement).value;

					let variable = value.replace(/[^0-9]+/,"");
					console.log(value, variable);
					let name = {$selectedInvoiceType->name} + ' ' + value;
					$("input[name='variable']").val(variable);
					$("input[name='name']").val(name);

                };

				el.addEventListener('change', handler);

				el.addEventListener('keydown', function (event) {
					if (!self.isSpecialKey(event.which)) {

						// Cancel timeout to run validation handler
						if (self.timeout) {
							clearTimeout(self.timeout);
						}
					}
				});

				el.addEventListener('keyup', function (event) {

					event = event || window.event;
					if (event.keyCode !== 9) {
						if (self.timeout) clearTimeout(self.timeout);
						self.timeout = setTimeout(function () {
							handler(event);
						}, 500);
					}

				});

            }

			setupClientHandlers(el){
				if (el.getAttribute("data-invoice-initialized"))
					return;

				el.setAttribute('data-invoice-initialized', 'true');
				let handler = function (event) {
					spinnerOn();
					let clientId = (event.target ? event.target : event.srcElement).value;
					$.nette.ajax({
						type  : "GET",
						url   : {link loadClientInfo!},
						data  : {
							clientId: clientId,
						}
					})

				}
				$(el).on("change", handler);
			}
			setupSequenceHandlers(el){
				if (el.getAttribute("data-invoice-initialized"))
					return;

				el.setAttribute('data-invoice-initialized', 'true');
				let self = this;
				let handler = function (event) {
					//console.log("sequence changed");
					let date = document.getElementById({$presenter["invoiceForm"]["date_created"]->getHtmlId()});
					date.dispatchEvent(new Event("changeDate"));
					//console.log(date);
				}

				$(el).on("change", handler);
            }
			setupDateHandlers(el) {

				if (el.getAttribute("data-invoice-initialized"))
					return;

				el.setAttribute('data-invoice-initialized', 'true');

				let self = this;
				let handler = function (event) {
					let date = (event.target ? event.target : event.srcElement).value;
					let exclude = (event.target ? event.target : event.srcElement).getAttribute("data-invoice-exclude");
					let originalDate = (event.target ? event.target : event.srcElement).getAttribute("data-invoice-date");
					let originalSequenceId = (event.target ? event.target : event.srcElement).getAttribute("data-invoice-sequence-id");
					//console.log(exclude);
					$.nette.ajax({
						type  : "GET",
						url   : {link checkInvoiceNumber!},
						data  : {
							invoiceDate: date,
							invoiceNo: $("#" + {$presenter["invoiceForm"]["number"]->getHtmlId()}).val(),
							invoiceExclude: exclude,
							invoiceOriginalDate: originalDate,
                            sequenceId: $("#" + {$presenter["invoiceForm"]["invoice_sequence"]->getHtmlId()}).val(),
                            originalSequenceId: originalSequenceId,
						}
					});
				}

				$(el).on("changeDate", handler);


				///$(el).on("changeYear", handler);
				//el.addEventListener('datepicker.changeDate', handler);
				//el.addEventListener('changeDate', handler);
				//el.addEventListener('dp.changeDate', handler);
			}

			setupHandlers(el){
				if (el.getAttribute("data-invoice-initialized"))
					return;

				el.setAttribute('data-invoice-initialized', 'true');

				var self = this;

				var handler = function (event) {
					event = event || window.event;
					self.validateInvoiceItem(event, (event.target ? event.target : event.srcElement) /*+ "|" + event.type*/);
					//console.log(event.type);
					//console.error("validatecontrol");
				};


				//console.log(keyupEvent);

				el.addEventListener('change', handler);

				el.addEventListener('keydown', function (event) {
					if (!self.isSpecialKey(event.which)) {

						// Cancel timeout to run validation handler
						if (self.timeout) {
							clearTimeout(self.timeout);
						}
					}
				});

				el.addEventListener('keyup', function (event) {

					event = event || window.event;
					if (event.keyCode !== 9) {
						if (self.timeout) clearTimeout(self.timeout);
						self.timeout = setTimeout(function () {
							handler(event);
						}, 500);
					}

				});
			}

			validateInvoiceItem(event, elem, watcher, value){
				watcher = watcher || elem.getAttribute('data-invoice-watcher');
				value = value === undefined ?  new Number(this.getValue(elem).replace(/\s/,"").replace(/\€/,"").replace(",",".")).toFixed(2) : value;

				//console.log(watcher);

				//let rowId = elem.getAttribute("name").match(/\d+/g)[0];

				let quantity = parseFloat(this.getElementInRow(elem, watcher, "quantity").value.replace(",","."));
				if(isNaN(quantity)){
					quantity = 1;
				}
				//console.log(quantity);
				let tax = parseInt(this.getElementInRow(elem,watcher,"tax").value);
				let koef = (tax / 100) + 1;
				let unitPrice = parseFloat(this.getElementInRow(elem,watcher,"unit_price").value.replace(",","."));



				if(watcher === "price"){
					koef = (tax / 100) + 1;
					let sum = value;
                    //console.log(value, sum);
					unitPrice = (sum / koef) / quantity;

					this.setValue(elem,watcher,"unit_price", new Number(unitPrice).toFixed(4));
					if(event.type === "change") {
						this.setValue(elem, watcher, "price", new Number(sum).toFixed(2))
					}
				} else {

					let price = (unitPrice * koef) * quantity;
					if(isNaN(price)){
						price = 0;
					}

					this.setValue(elem, watcher, "price", new Number(price).toFixed(2));
				}

				this.showTaxes();


				/*if(watcher === "unit_price"){

					var unitPrice = value;

                    console.log(quantity,this.getElementInRow(elem, watcher, "quantity").value);
					this.setValue(elem, watcher, "price", new Number((unitPrice * koef) * quantity).toFixed(2));

                } else if(watcher === "tax"){
					var tax = parseInt(value);
					var koef = (tax / 100) + 1;
					var unitPrice = parseFloat(this.getElementInRow(elem,watcher,"unit_price").value);

					this.setValue(elem, watcher, "price", new Number((unitPrice * koef) * quantity).toFixed(2));

                } else if(watcher === "price"){
					var tax = parseInt(this.getValue(this.getElementInRow(elem, watcher, "tax")));
					var koef = (tax / 100) + 1;
					var sum = value;

					var unitPrice = sum / koef;

					this.setValue(elem,watcher,"unit_price", new Number(unitPrice).toFixed(2));
					if(event.type === "change") {
						this.setValue(elem, watcher, "price", new Number(sum).toFixed(2))
					}

                }*/
				//window.prices[rowId] = { unit_price: parseFloat(unitPrice), price: parseFloat(elSum.value), tax: tax };
				//console.log(window.prices);
			}

            getInvoiceTotal(){
	            let rows = document.querySelectorAll(".form-row-name");
	            let totalPrice = 0
                for(var i=0; i < rows.length; ++i) {
	                let elem = rows[i];
					if(typeof this.getElementInRow(elem, "name", "name").dataset.rounding !== "undefined"){
						continue;
                    }
	                let watcher = "name";
	                let quantity = parseFloat(this.getElementInRow(elem, watcher, "quantity").value.replace(",","."));
	                if(isNaN(quantity)){
		                quantity = 1;
	                }
	                let tax = parseInt(this.getElementInRow(elem,watcher,"tax").value);
	                let koef = (tax / 100) + 1;
	                let unitPrice = parseFloat(this.getElementInRow(elem,watcher,"unit_price").value.replace(",",".")) * quantity;
	                if(isNaN(unitPrice)){
		                unitPrice = 0;
	                }

	                let price = (unitPrice * koef);
	                totalPrice += parseFloat(price.toFixed(2));
                }
				return totalPrice;
            }

			numberFormat(number, decimals, dec_point, thousands_sep){
				number  = number*1;//makes sure `number` is numeric value
				var str = number.toFixed(decimals?decimals:0).toString().split('.');
				var parts = [];
				for ( var i=str[0].length; i>0; i-=3 ) {
					parts.unshift(str[0].substring(Math.max(0,i-3),i));
				}
				str[0] = parts.join(thousands_sep?thousands_sep:' ');
				return str.join(dec_point?dec_point:'.');
            }

			getTaxes(){
				let rows = document.querySelectorAll(".form-row-name");

				let totalPrice = 0, totalBase = 0, totalTaxVolume = 0;

				for(var i=0; i < rows.length; ++i){
					let elem = rows[i];
					let watcher = "name";
					let quantity = parseFloat(this.getElementInRow(elem, watcher, "quantity").value.replace(",","."));
					if(isNaN(quantity)){
						quantity = 1;
					}
					let tax = parseInt(this.getElementInRow(elem,watcher,"tax").value);
					let koef = (tax / 100) + 1;
					let unitPrice = parseFloat(this.getElementInRow(elem,watcher,"unit_price").value.replace(",",".")) * quantity;
					if(isNaN(unitPrice)){
						unitPrice = 0;
					}

					let price = (unitPrice * koef);
					let taxVolume = price - unitPrice

					totalBase += parseFloat(unitPrice.toFixed(2));
					totalPrice += parseFloat(price.toFixed(2));
					totalTaxVolume += parseFloat(taxVolume.toFixed(2));


				}
				//console.log(totalBase, this.numberFormat(totalBase, 2))
				return {
					totalBase: totalBase,
                    totalPrice: totalPrice,
                    totalTaxVolume: totalTaxVolume,
                }
            }

			showTaxes() {

				//console.log(this.isRoundingAdded(),window.roundingType)
				if(this.isRoundingAdded()){
					//console.log(this.getInvoiceTotal());
					this.updateRounding(window.roundingType);
					//this.setRounding(window.roundingType);
					//	window.roundingAdded = false;
				}

				let taxes = this.getTaxes();


				if(document.getElementById("raw_total") !== null) {
					document.getElementById("raw_total").innerHTML = this.numberFormat(taxes.totalBase,2);//.toFixed(2);
				}
				if(document.getElementById("vat_total") !== null) {
					document.getElementById("vat_total").innerHTML = this.numberFormat(taxes.totalTaxVolume,2); //.toFixed(2);
				}
				if(document.getElementById("invoice_total") !== null) {
					document.getElementById("invoice_total").innerHTML = this.numberFormat(taxes.totalPrice, 2); //.toFixed(2);
				}
				if(document.getElementById("invoice_total_deposit") !== null && document.getElementById("invoice_deposit") !== null) {
					console.log(document.getElementById("invoice_deposit").dataset.deposit);
					document.getElementById("invoice_total_deposit").innerHTML = ((taxes.totalPrice) - parseFloat(document.getElementById("invoice_deposit").dataset.deposit)).toFixed(2);
				}

			}

			getValue(elem){
				return elem.value.replace('\r', '').replace(/^\s+|\s+$/g, '');
			}

			getElementInRow(elem, watcher, elementName) {
				return document.querySelector("[name='" + elem.getAttribute("name").replace(watcher,elementName) + "']")
			}

			setValue(elem, watcher, elementName, value) {
				document.querySelector("[name='" + elem.getAttribute("name").replace(watcher,elementName) + "']").value = value;
			}


			isSpecialKey(k){
				return (k == 20 /* Caps lock */
					|| k == 16 /* Shift */
					|| k == 9 /* Tab */
					|| k == 27 /* Escape Key */
					|| k == 17 /* Control Key */
					|| k == 91 /* Windows Command Key */
					|| k == 19 /* Pause Break */
					|| k == 18 /* Alt Key */
					|| k == 93 /* Right Click Point Key */
					|| (k >= 35 && k <= 40) /* Home, End, Arrow Keys */
					|| k == 45 /* Insert Key */
					|| (k >= 33 && k <= 34) /*Page Down, Page Up */
					|| (k >= 112 && k <= 123) /* F1 - F12 */
					|| (k >= 144 && k <= 145)); /* Num Lock, Scroll Lock */
			}


			toggleRoundingButton() {
				$(".add-rounding-button").show();
				$(document).find("input[name$='[is_rounding]']").each(function (){
					//console.log($(this).val());
					if(parseInt($(this).val()) === 1){
						$(".add-rounding-button").hide();
						return;
					}
				});
            }
            isRoundingAdded(){
				return $(document).find("input[data-rounding='1']").length
            }

			getRoundingValue(roundingType){
				let roundingValue = 0;
				let invoiceTotal = this.getInvoiceTotal();
				switch (roundingType) {
					case 'math2one':
						roundingValue = (invoiceTotal.toFixed(0) - invoiceTotal).toFixed(2); //(Math.ceil(taxes.totalPrice) - taxes.totalPrice).toFixed(2);
						break;
					case 'math2tenth':
						roundingValue = (invoiceTotal.toFixed(1) - invoiceTotal).toFixed(2);
						break;
					case 'math5cent':
						let lastCent = parseInt(invoiceTotal.toString().substring(invoiceTotal.toString().length -1))
						let plus, minus = 0;

						if(lastCent < 3){
							plus = 0;
						} else if (lastCent > 2 && lastCent < 8) {
							plus  = 0.05;
						} else if (lastCent >= 8 /*|| $cents == 0*/) {
							plus  = 0.1;
						}
						minus = lastCent / 100;

						roundingValue += plus;
						roundingValue -= minus;
						console.log(roundingValue);
						roundingValue = roundingValue.toFixed(2);
						break;
					case 'up2one':
						roundingValue = (Math.ceil(invoiceTotal)  - invoiceTotal).toFixed(2);
						break;
					case 'down2one':
						roundingValue = (Math.floor(invoiceTotal)  - invoiceTotal).toFixed(2);
						break;

				}
				return roundingValue;
            }

			updateRounding(roundingType) {
                let invoiceTotal = this.getInvoiceTotal();
				if(invoiceTotal > 0){
					let priceEl = $(document).find("input[data-rounding='1']").parents(".row").find("input[data-invoice-watcher='price']");
					let unitPriceEl = $(document).find("input[data-rounding='1']").parents(".row").find("input[data-invoice-watcher='unit_price']");
					let roundingValue = this.getRoundingValue(roundingType);
                    priceEl.val(roundingValue);
					unitPriceEl.val(roundingValue);
				}
            }
			setRounding(roundingType){
				//let taxes = this.getTaxes();
				let invoiceTotal = this.getInvoiceTotal();
				if(invoiceTotal > 0){

					if(typeof window.roundingText === "undefined"){
						window.roundingText = {_"Zaokrúhlenie"};
                    }
					console.log(roundingText);

					let roundingValue = this.getRoundingValue(roundingType);
                    let roundingIndexArray = [];
					$(".form-row-name").each(function (){
						roundingIndexArray.push(parseInt($(this).attr("id").replace("frm-invoiceForm-items-","").replace("-name","")));
					});

					let roundingIndex = Math.max(... roundingIndexArray);

					$("#frm-invoiceForm-items-" + roundingIndex + "-name").val(window.roundingText).attr("data-rounding",1);
					$("#frm-invoiceForm-items-" + roundingIndex + "-price").val(roundingValue);
					$("input[name='items[" + roundingIndex + "][is_rounding]']").val(1);

					let changeEvent = new Event("change");
					$("#frm-invoiceForm-items-" + roundingIndex + "-price")[0].dispatchEvent(changeEvent);
				}
            }

			toggles() {
				let rows = document.querySelectorAll(".form-row-name");
				for(var i=0; i < rows.length; ++i){
					$(rows[i]).parents("div.row").find("[data-nette-rules*='%index%']").each(function (){
						let index = $(rows[i]).attr("id").replace("frm-invoiceForm-items-","").replace("-name","");
						$(this).attr("data-nette-rules", $(this).attr("data-nette-rules").replace("%index%",index));
                    });
				}
				Nette.toggleForm($(rows[0]).parents("form")[0]);
			}

			static startAddRounding(roundingType, roundingText) {
				$("button[name='items[multiplier_creator]']").click();
				window.roundingAdded = true;
				window.roundingType = roundingType;
				window.roundingText = roundingText;
            }
		}

		$(document).ready(function () {

            {ifset $hidden}
            {foreach $hidden as $h => $isHidden}
            {if $isHidden}
			$("#" + {$h}).hide();
            {else}
			$("#" + {$h}).show();
            {/if}
            {/foreach}
            {/ifset}

            //$(document).find("#" + {$presenter["invoiceForm"]["invoice_sequence"]->getHtmlId()}).on("change", function (){
				//console.log("change seqenece");
				//document.getElementById({$presenter["invoiceForm"]["date_created"]->getHtmlId()}).dispatchEvent(new Event("change"));
				 //$(document).find("#" + ).change();
            //})

			let invoice = new Invoice();
			invoice.toggleRoundingButton();
			invoice.showTaxes();
			invoice.toggles();
			$.nette.ext({
				success: function (payload) {
					let invoice = new Invoice();
					invoice.showTaxes();
					if(window.roundingAdded){
						invoice.setRounding(window.roundingType);
						window.roundingAdded = false;
                    }
					invoice.toggleRoundingButton();
					invoice.toggles();
				}
			});
		});
    </script>

{/block}
