{default $hasPayBySquare = false}
{var $defaultRoundingPrecisionItem = $invoice->ref('invoice_supplier')->default_rounding_precision_item}
{var $defaultRoundingPrecisionTotal = $invoice->ref('invoice_supplier')->default_rounding_precision_total}

<style>
	.table thead > tr > th, .table tfoot > tr > th
        vertical-align: middle !important;
    }
</style>
<div class="row no-gutters invoice-border-bottom ">
    <div class="col-lg-6 invoice-border-right">
        <div class="pdf-logo " n:if="$logo" style="position: absolute;right:1rem; top:1rem;">
            <img class="wd-150" src="{$logo|dataStream}">
        </div>
        {include "@invoice_supplier_info.latte"}
    </div>
    <div class="col-lg-6">
        {include "@invoice_client_info.latte"}{*$invoice->lang*}

        {*php dump($trans)*}
    </div>
</div>
<div class="p-3 ">
    {$invoice->note_before}&nbsp;
</div>
<div class="table-responsive">
    <table class="table border-bottom mb-0">
        <thead class="bg-gray-300 tx-inverse">
        <tr>
            {*php dump($showVat, $vatPayer)*}
            <th class="tx-left">{$trans->translate("Názov a popis položky")}</th>
            <th class="tx-center" width="50"><span n:if="$showPrices">{$trans->translate("Počet")}</span></th>
            <th class="tx-center" width="50"><span n:if="$showPrices">{$trans->translate("Jednotka")}</span></th>
            <th n:if="$showVat && $showPrices" class="tx-right" width="100">{$trans->translate("Jedn. cena")}<br> {$trans->translate("bez DPH")}</th>
            <th n:if="!$showVat && $showPrices" class="tx-right" width="180">{$trans->translate("Jednotková cena")}</th>
            <th n:if="$showVat && $showPrices" class="tx-center" width="50">{$trans->translate("DPH")}</th>
            <th n:if="$showVat && $showPrices" class="tx-right" width="100">{$trans->translate("Jedn. cena")}<br> {$trans->translate("s DPH")}</th>
            <th n:if="$showVat && $showPrices" class="tx-right" width="120">{$trans->translate("Celkom s DPH")}</th>
            <th n:if="!$showVat && $showPrices" class="tx-right" width="120">{$trans->translate("Celkom")}</th>
        </tr>
        </thead>
        <tbody>
        <tr n:foreach="$items as $item">
            {var $koef = ($item->tax / 100) + 1}
            <td>
                <div>{$item->name}</div>
                <div class="pdf-desc">{$item->description}</div>
            </td>
            <td class="tx-center">{$item->quantity}</td>
            <td class="tx-center">{$trans->translate($item->unit)}</td>
            <td n:if="$showPrices" class="tx-right">{$item->unit_price|currency:"EUR", $defaultRoundingPrecisionItem}</td>
            <td n:if="$showVat && $showPrices" class="tx-center">{$item->tax}%</td>
            <td n:if="$showVat && $showPrices" class="tx-right">{($item->unit_price * $koef)|currency}</td>
            <td n:if="$showPrices" class="tx-right">{$item->price|currency:'EUR', $defaultRoundingPrecisionTotal}</td>
        </tr>
        </tbody>
    </table>
</div>
{if isset($isPdf) && $itemsCount > 10}
<pagebreak>
{/if}
<div class="row no-gutters">
    <div class="col-lg-7 pdf-col-7 {*invoice-border-right*}" >
        <div class="p-3">
            <strong>{$trans->translate("Poznámka")}:</strong>
            <div>{$invoice->note_after|breaklines}</div>
            <div n:if="$endingNote !== null" class="mt-2">{$endingNote}</div>
            <div class="pdf-spacer" n:if="isset($isPdf)"></div>
            <div class="invoice-qr mt-3 tx-12" n:if="$invoiceData->show_qr && ($hasPayBySquare || isset($isPdf))">
                <img src="{$basePath}/assets/admin/img/paybysquare-sample.svg" n:if="!isset($isPdf)">
                <span n:if="!isset($isPdf)">Tento QR kód je iba ilustračný, správny bude v PDF faktúre.</span>
                <img n:if="isset($qrData) && $qrData !== null && isset($isPdf)" src="{$qrData|dataStream}" style="max-width: 120px">
            </div>
            {*php dump($invoiceData->show_paypal, $paypalUrl)*}
            <div class="mt-3" n:if="$invoiceData->show_paypal  && isset($paypalUrl) && ($amount - $paid) > 0">
                <div class="text-center tx-12 mb-1" style="width: 120px;">{$trans->translate("Zaplatiť cez")}</div>
                <a href="{$paypalUrl}"><img src="{$paypalButton|dataStream}" style="max-width: 120px"></a>
            </div>
        </div>
        <div class="p-3" n:if="$showPrices && $invoice->ref('invoice_type')->key === 'cancel' && $showVat && $showVatTable">

            {var $main = $invoice->ref("invoice","cancel")}
            {php $mainSumPrice = $mainSumPriceVat = $mainSumVat = []}
            {foreach $invoiceModel->getVats($main) as $tax => $row}
                {php $mainSumPrice[] = $row["price"]}
                {php $mainSumPriceVat[] = $row["priceVat"]}
                {php $mainSumVat[] = $row["vat"]}
            {/foreach}

            {var $mainRelated = $invoiceModel->getRelatedData($main)["related"]->order("id")}

            {php $sumPrice = $sumPriceVat = $sumVat = []}
            {php $cancels = 0}
            {foreach $mainRelated as $rel}
                {if $rel->ref("invoice")->ref("invoice_type")->key === "cancel"}
                    {var $cancel = $rel->ref("invoice")}
                {elseif $rel->ref("invoice_pair")->ref("invoice_type")->key === "cancel"}
                    {var $cancel = $rel->ref("invoice_pair")}
                {else}
                    {continueIf true}
                {/if}
                {*php dump($cancel)*}
                {foreach $invoiceModel->getVats($cancel) as $tax => $row}
                    {php $sumPrice[] = $row["price"]}
                    {php $sumPriceVat[] = $row["priceVat"]}
                    {php $sumVat[] = $row["vat"]}
                {/foreach}
                {php $cancels++}
                {if $cancel->id === $invoice->id}
                    {breakIf true}
                {/if}

            {/foreach}
            {*foreach $invoice_taxes as $tax => $row}
                {php $sumPrice[] = $row["price"]}
                {php $sumPriceVat[] = $row["priceVat"]}
                {php $sumVat[] = $row["vat"]}
            {/foreach*}

            <table class="table table-sm tx-12 text-right border-bottom">
                <thead>
                <tr>
                    <th></th>
                    <th align="right">Pôvodné</th>
                    <th align="right">Oprava</th>
                    <th align="right">Po oprave</th>
                </tr>
                </thead>

                <tbody>
                    <tr>
                        <td>Základ dane</td>
                        <td  style="background:#fbbbc2">{array_sum($mainSumPrice)|currency}</td>
                        <td style="background:#fbbbc2">{array_sum($sumPrice)|currency}</td>
                        <td style="background:#fbbbc2"><strong>{array_sum($mainSumPrice)+array_sum($sumPrice)|currency}</strong></td>
                    </tr>
                    <tr>
                        <td>DPH</td>
                        <td  style="background:#fbbbc2">{array_sum($mainSumVat)|currency}</td>
                        <td style="background:#fbbbc2">{array_sum($sumVat)|currency}</td>
                        <td style="background:#fbbbc2"><strong>{array_sum($mainSumVat)+array_sum($sumVat)|currency}</strong></td>
                    </tr>
                    <tr>
                        <td>Celková suma</td>
                        <td  style="background:#fbbbc2">{array_sum($mainSumPriceVat)|currency}</td>
                        <td style="background:#fbbbc2">{array_sum($sumPriceVat)|currency}</td>
                        <td style="background:#fbbbc2"><strong>{array_sum($mainSumPriceVat)+array_sum($sumPriceVat)|currency}</strong></td>
                    </tr>
                </tbody>
            </table>
            <div n:if="$cancels > 1" class="text-right">Započítané sú všetky vystavené dobropisy ({$cancels}) k faktúre {$main->number}</div>

        </div>
    </div>
    <div class="col-lg-5 pdf-col-5" >
        {*php dump($showVat)*}
        <div class="p-3 invoice-border-bottom" n:if="$showPrices" n:snippet="summary">
            {var $defaultRoundingPrecisionItem = $invoice->ref('invoice_supplier')->default_rounding_precision_item}
            {var $defaultRoundingPrecisionTotal = $invoice->ref('invoice_supplier')->default_rounding_precision_total}
            <div class="row no-gutters justify-content-end" n:if="$showVat && $showVatTable">
                <div class="col-10 pdf-float-right">
                    <table class="table table-sm tx-12 text-right border-bottom">
                        <thead>
                        <tr>
                            <th></th>
                            <th align="right">{$trans->translate("Základ DPH")}</th>
                            <th align="right">{$trans->translate("Výška DPH")}</th>
                        </tr>
                        </thead>
                        {php $sumPrice = $sumPriceVat = $sumVat = []}
                        <tbody>
                        <tr n:foreach="$invoice_taxes as $k => $v">
                            <td>{$trans->translate("DPH")} {$k}%</td>
                            <td>{$v["price"]|currency}</td>
                            <td>{$v["vat"]|currency}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row no-gutters tx-bold tx-18 tx-right">
                <div class="col-auto pdf-col-7 ml-auto">{$trans->translate("Celková suma")}</div>
                <div class="col-5">{$amount|currency:'EUR', $defaultRoundingPrecisionTotal}</div>
            </div>
            <div class="row no-gutters tx-18 tx-right" n:if="$invoiceData->show_payment_info && $showPrices">
                <div class="col-auto pdf-col-7 ml-auto" n:if="!isset($paidText)">{$trans->translate("Uhradené")}</div>
                <div class="col-auto pdf-col-7 ml-auto" n:if="isset($paidText)">{$trans->translate($paidText)}</div>
                <div class="col-5 ">{$paid|currency:'EUR', $defaultRoundingPrecisionTotal}</div>
            </div>
            <div class="row no-gutters tx-bold tx-18 tx-right" n:if="$invoiceData->show_payment_info && $showPrices">
                <div class="col-auto pdf-col-7 ml-auto">{$trans->translate("Suma na úhradu")}</div>
                <div class="col-5 ">{$amount - $paid|currency:'EUR', $defaultRoundingPrecisionTotal}</div>
            </div>
        </div>
        <div class="p-3 pb-5">
            {$trans->translate("Podpis a pečiatka")}:
            <div class="text-center">
                <img n:if="$signature && $invoice->show_signature" class="img-fluid" style="max-width: 220px;" src="{$signature|dataStream}">
            </div>
        </div>

    </div>
    {*}<div class="col-12">

        {var $defaultIban = reset($ibans)}

        <div class="row no-gutters" style="background: rgb(80, 137, 216);color:#FFF;">
            <div class="col" style="width: 39%;border-right: 2px solid white;padding:5px 5px">
                <div style="font-size:14px;">IBAN</div>
                <div style="font-weight: bold;font-size:17px;">{$defaultIban->iban ?? $invoice->supplier_iban}</div>
            </div>
            <div class="col" style="border-right: 2px solid white;padding:5px 5px">
                <div style="font-size:14px;">Variabilný symbol</div>
                <div style="font-weight: bold;font-size:17px;">{$invoice->variable}</div>
            </div>
            <div class="col" style="border-right: 2px solid white;padding:5px 5px">
                <div style="font-size:14px;">Dátum splatnosti</div>
                <div style="font-weight: bold;font-size:17px;">{$invoice->date_due|date:"d.m.Y"}</div>
            </div>
            <div class="col" style="padding:5px 5px">
                <div style="font-size:14px;">Suma na úhradu</div>
                <div style="font-weight: bold;font-size:17px;">{$amount - $paid|currency}</div>
            </div>
        </div>
    </div>*}
</div>

<div class="p-3" n:if="!isset($isPdf)">
    <div class="row no-gutters">
        <div class="col-lg text-lg-left text-center">{$trans->translate("Vystavil")}: {$invoice->issued_by}</div>
        <div class="col-lg text-center">
            <span n:if="$invoice->issued_by_phone !== null && strlen($invoice->issued_by_phone) > 0">{$trans->translate("Tel.")}:</span>
            {$invoice->issued_by_phone}
        </div>
        <div class="col-lg text-center">
            <span n:if="$invoice->issued_by_email !== null && strlen($invoice->issued_by_email) > 0">{$trans->translate("E-mail")}:</span>
            {$invoice->issued_by_email}
        </div>
    </div>
</div>