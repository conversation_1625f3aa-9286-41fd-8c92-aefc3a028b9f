<?php
namespace BackendModule;


use <PERSON><PERSON>mi<PERSON><PERSON>\Attributes\Description;
use Nette\Application\BadRequestException;
use Webtec\Models\RetailCategoryModel;
use Webtec\Models\RetailProductImageModel;
use Webtec\Models\RetailProductModel;
#[Description("Maloobchod")]
class RetailPresenter extends BasePresenter {
	protected bool $checkPrivileges = false;
	public RetailCategoryModel $categoryModel;
	public RetailProductModel $retailProductModel;

	protected function startup() {
		parent::startup();
		$this->categoryModel = $this->modelFactory->create("retailCategory");
		$this->retailProductModel = $this->modelFactory->create("retailProduct");
		$this["breadcrumb"]->addItem("retail","Maloobchod",":Backend:Retail:default", [], ["category_id" => 1]);
	}

	public function actionDefault(int $category_id){
		$c = $this->categoryModel->find($category_id)->fetch();
		if(!$c){
			throw new BadRequestException();
		}
		$path = array_reverse($this->categoryModel->getParents($category_id, RetailCategoryModel::LIST_TYPE_PAIR) , true);

		foreach($path as $id => $name) {
			$this["breadcrumb"]->addItem( "category-".$id, $name,":Backend:Retail:default", [] , ["category_id" => $id] );
		}

		$categories = [$category_id => $category_id];
		$related = $c->related("retail_category","retail_category_id")->fetchPairs("id","id");
		$categories += $related;

		//if(){}

		$this->template->categoryName = $c->name;
		$this->template->items = $this->retailProductModel->findByOr([":retail_product_category.retail_category_id" => $categories,"retail_product.retail_category_id" => $categories])->where(["retail_product.active" => 1])->order("ordr ASC");

	}
}