<?php
namespace BackendModule;

use Ales<PERSON>ita\DropzoneUploader\DropzoneUploader;
use Ales<PERSON><PERSON>\DropzoneUploader\Factory;
use Contributte\Mailing\IMailSender;
use IPub\FlashMessages\Entities\Message;
use Kubomikita\Attributes\Description;
use Kubomi<PERSON><PERSON>\Components\DocumentViewer;
use Kubomikita\Factory\Control\DocumentViewerFactory;
use Kubomikita\FileManager\Adapters\S3Adapter;
use Kubomikita\Utils\Strings;
use Nette\Http\SessionSection;
use Nette\InvalidArgumentException;
use Nette\Mail\SmtpMailer;
use Nette\Utils\DateTime;
use Nette\Utils\FileSystem;
use Nette\Utils\Html;
use Nette\Utils\Validators;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\MailingModel;
use Webtec\Models\MailingTemplateModel;
use Webtec\Models\UserModel;
use Webtec\UI\Forms\Form;
#[Description("Hromadné e-maily")]
class MailingPresenter extends BasePresenter {
	/** @var MailingModel */
	public $mailingModel;
	public $default_message;

	/** @var Factory @inject */
	public $dropzoneFactory;
	/** @var SessionSection */
	public $ses;
	/** @var array  */
	public $sender = [
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>",
		"<EMAIL>" => "<EMAIL>"
	];

	protected bool $checkPrivileges = false;

	protected function startup() {
		parent::startup();
		$this->mailingModel = $this->modelFactory->create("mailing");
		$this["breadcrumb"]->addItem("mailing","Hromadné e-maily",":Backend:Mailing:default");
		$this->ses = $this->getSession("mailing");

		$email_sender = $this->context->getByType(IMailSender::class);

		if($email_sender->typeOf(SmtpMailer::class)){
			$email_smtp = $this->context->parameters["smtp"];
			//bdump($email_smtp);
			$this->sender = [];
			foreach($email_smtp as $smtp){
				$this->sender[$smtp["username"]] = $smtp["username"];
			}
		}

	}

	public function actionDefault(?int $blog_article_id = null, ?int $birthday_user = null){
		$this["mailingForm"]["from"]->setDefaultValue(reset($this->sender));

		$this->default_message = Strings::normalizeWhitespaces('<br>'.
		                         $this->getUserSignature(
			                         $this->modelFactory->create("user")->find($this->getUser()->getId())->fetch()
		                         ));
		$this["mailingForm"]["message"]->setDefaultValue(
			$this->default_message
		);
		$this["mailingForm"]["testemail"]->setDefaultValue($this->getUser()->getIdentity()->username);
		//$mailDir = "uploads/".$this->ses->uniq."/";
		//$attachmentDir = WWW_DIR."/".$mailDir;
		//$this["mailingForm"]["summernoteUploadDir"]->setDefaultValue($attachmentDir);

		if($blog_article_id !== null){
			$articleModel = $this->modelFactory->create("blogArticle");
			$article = $articleModel->find($blog_article_id)->fetch();

			$template = $this->createTemplate();
			$template->setFile(__DIR__."/../templates/@mail/article.latte");
			$template->article = $article;
			$template->link = $this->template->baseUrl.$this->link(":Backend:Blog:article",["id" => $article->id]);


			$defaults = ["subject" => $article->name,"message" => $template->renderToString()];
			$this["mailingForm"]->setDefaults($defaults);
		}
		if($birthday_user !== null){
			/** @var UserModel $userModel */
			$userModel = $this->modelFactory->create("user");
			$usr = $userModel->find($birthday_user)->fetch();

			$users = [];
			foreach (
				$this->modelFactory->create( "user" )->findBy( [
					"type" => [
						UserModel::TYPE_COWORKER,
						UserModel::TYPE_SPECIALIST,
						UserModel::TYPE_CLIENT
					]/*,"enabled" => 1, "login_allowed" => 1*/
				] ) as $user
			) {
				$users[ $user->username ] = UserModel::formatName( $user );
			}

			$this["mailingForm"]["users"]->setItems( $users );
			$this["mailingForm"]->setDefaults(["to"      => MailingModel::TYPE_USERS,"users" => $usr->username]);
			try {
				/** @var MailingTemplateModel $mailingTemplateModel */
				$mailingTemplateModel = $this->modelFactory->create( "mailingTemplate" );
				$template             = $mailingTemplateModel->getByType( MailingTemplateModel::TYPE_BIRTHDAY_WISH, [
					"fullname"  => UserModel::formatName( $usr, false, false ),
					"signature" => '', //$userModel->getMailSignature( $this->createTemplate(), $this->getUser()->getId() )
				] );

				$defaults = [
					"subject" => $template->subject, //"Všetko najlepšie k narodeninám!",
					"message" => $template->message.$userModel->getMailSignature( $this->createTemplate(), $this->getUser()->getId() ), //renderToString(),
					"type"    => UserModel::MARKETING_BIRTHDAY
				];
				$this["mailingForm"]->setDefaults( $defaults );
			} catch (\Throwable $e){
				bdump($e);
				if($this->getSignal() === null) {
					$this["mailingForm"]["subject"]->addError( "Pre narodeninové blahoželania nieje vytvorená šablóna. Alebo šablóna obsahuje neplatné znaky." );
				}
			}
		}
		$disable_users = $this->modelFactory->create("user")->findBy(["type" => [UserModel::TYPE_COWORKER,UserModel::TYPE_SPECIALIST], "login_allowed" => 1,"enabled" => 1, "marketing_mailing" => 0])->fetchPairs("id","username");
		if(!empty($disable_users)) {
			$this["mailingForm"]["users"]->setDisabled( $disable_users );
			if($birthday_user !== null && isset($disable_users[$birthday_user]) && !$this->isAjax()){
				$this->flashMessage("Vybratý užívateľ si nepraje od nás dostávať e-maily.", Message::LEVEL_ERROR,"Nemožno vybrať e-mail adresu");
			}
		}


		if(!isset($this->ses->uniq)){
			$this->ses->uniq = uniqid("mail");
		}
		$this["documentViewer"]->setDocumentDir(UPLOAD_DIR."/".$this->ses->uniq);
		$this["dropzoneForm"]->setDocumentViewer($this["documentViewer"]);
		$this["dropzoneForm"]->onBeginning[] = function (DropzoneUploader $uploader): void {
			$uploader->setFolder($this->ses->uniq);
		};
	}
	public function actionSent(){
		$this["breadcrumb"]->addItem("sent","Odoslané e-maily");
		if($this->isSuperadmin()){
			$dataSource = $this->modelFactory->create("mailing")->findAll()->order("id DESC");
		} else {
			$dataSource = $this->modelFactory->create("mailing")->findBy(["visibility" => 1])->order("id DESC");
		}
		$this["sentEmailsGrid"]->setDataSource($dataSource);
	}

	protected function createComponentSentEmailsGrid($name) : DataGrid{
		$d = $this->dataGridFactory->create($this,$name);
		$d->setItemsDetail();
		$d->setColumnsHideable();
		$d->setTemplateFile(__DIR__ . '/../templates/Mailing/@detail_row.latte');

		$d->addColumnText("type","Typ e-mailu")->setRenderer(function ($item){return UserModel::MARKETING_TYPES[$item->type];});
		$d->addColumnDateTime("datetime","Dátum a čas vytvorenia")->addCellAttributes(["style"=>"width:200px;"])->setFormat("d.m.Y H:i:s");
		$d->addColumnText("from","E-mail");
		$d->addColumnText("user","Odosielateľ")->setRenderer(function ($item){
			if($item->user === null){
				return "- cron -";
			}
			return UserModel::formatName($item->ref("user"));
		});
		$d->addColumnText("reply","Odpoveď pre")->setDefaultHide();
		$d->addColumnText("to","Adresáti")->setRenderer(function ($item){
			return MailingModel::TYPES[$item->to];
		});
		$d->addColumnText("subject","Predmet");
		$d->addColumnText("text","Správa")->setRenderer(function ($item){
			return Strings::shortText(strip_tags($item->text),60,"...",false);
		});
		$d->addColumnText("sent","Odoslané")->setAlign("right")->setTemplateEscaping(false)->setRenderer(function ($item){
			$all = $item->related("mailing_send")->count("*");
			$sent = $item->related("mailing_send")->where(["sent"=>1])->count("*");
			$class = "tx-success";
			if($sent < $all){
				$class = "tx-warning";
			}
			return '<div class="tx-bold '.$class.'">'.$sent ." / ".$all.'</div>';
		});
		$d->setRowCallback(function ($item,$tr){
			$all = $item->related("mailing_send")->count("*");
			$sent = $item->related("mailing_send")->where(["sent"=>1])->count("*");
			if( $all ==0 && $sent == 0){
				$tr->addAttributes(["class" => "table-danger"]);
			}
		});
		$d->setItemsPerPageList([50,100,200,500,1000]);

		$d->addFilterMultiSelect("type","",UserModel::MARKETING_TYPES);
		$d->addFilterDateRange("datetime","");
		$d->addFilterText("text","");

		$d->addAction("delete", "","delete!")->setIcon("times")->setClass("btn btn-xs btn-danger ajax")->setConfirmation(new StringConfirmation("Naozaj?"));
		$d->allowRowsAction("delete",function ($item) {
			return !$item->related("mailing_send")->where(["sent"=>1])->count("id");
		});
		return $d;
	}

	public function handleDelete(int $id) {

		$mailing = $this->modelFactory->create("mailing")->find($id)->fetch();
		$mailing->delete();

		$this->flashMessage("Odoslanie e-mailu bolo zrušené.", Message::LEVEL_ERROR);
		$this["sentEmailsGrid"]->reload();
	}

	protected function createComponentMailingForm($name) : Form{
		$f = $this->formFactory->create($this,$name);
		//$f->addHidden("summernoteUploadDir");
		//$f->addText("from","Odosielateľ")->setAttribute("readonly","readonly");
		$f->addSelect("from","Odosielateľ",$this->sender)->setRequired();
		$f->addSearchSelect("to","Adresáti",MailingModel::TYPES)
		  ->setPrompt("-- vyberte --")
		  ->setRequired()
		  ->addCondition($f::EQUAL,MailingModel::TYPE_USERS)
		    ->toggle("toggle-users");
		$users = [];
		foreach($this->modelFactory->create("user")->findBy(["type"=>[UserModel::TYPE_COWORKER,UserModel::TYPE_SPECIALIST],"enabled" => 1, "login_allowed" => 1]) as $user){
			$users[$user->username] = UserModel::formatName($user);
		}

		$f->addMultiSelect("users","Užívatelia", $users )
		  ->addConditionOn($f["to"],$f::EQUAL,MailingModel::TYPE_USERS)
		    ->setRequired();
		$f->addMultiSelect("bcc","Skrytá kópia",$users);
		//$f->addCoworker("users","");
		$f->addText("subject","Predmet")->setRequired();
		$f->addWysiwyg("message","Správa")->setRequired();
		$f->addHidden("type","mailing");

		$f->addSubmit("send","Odoslať");
		$f->addText("testemail","Testovací e-mail")->addCondition($f::FILLED, true)->addRule($f::EMAIL,"Zadajte správnu e-mailovú adresu.");
		$f->addSubmit("test","Odoslať testovací e-mail")->setValidationScope([$f["subject"], $f["testemail"],$f["message"], $f["from"]]);

		$f->onValidate[] = function (Form $form){
			$values = $form->getUnsafeValues(null);

			$message = Strings::normalizeWhitespaces($values->message);
			if($message == $this->default_message){
				$error = "Prosím vyplňte správu!";
				$form->addError($error);
				$this->flashMessage($error,Message::LEVEL_ERROR);
			}
		};

		$f->onError[] = function (Form  $form) {
			bdump($form->getValues());
			bdump($form->getErrors());
		};

		$f->onSuccess[] = function (Form $form){
			$values = $form->getValues();
			$success = true;
			if($form->isSubmitted()->getName() === "test") {
				try {
					$mail = $this->mailBuilderFactory->create();
					$mail->setSubject( $values->subject );
					$mail->addTo( $values->testemail );

					$attachmentDir = WWW_DIR . "/uploads/" . $this->ses->uniq ;

					foreach ($this->fileManager->listFiles($attachmentDir,false,true) as $file){
						$mail->getMessage()->addAttachment($file["name"], $file["content"], $file["mimeType"]);
					}

					$mail->setFrom( $values->from );
					$mail->getMessage()->addReplyTo( $values->from );
					$mail->setTemplateFile( MAIL_DIR . "/message.latte" );
					$mail->setParameters( [ "message" => $values->message, "signature" => "" ] );
					$mail->send();
					$this->flashMessage("Na e-mailovú adresu ".$values->testemail, Message::LEVEL_SUCCESS, "Testovací e-mail bol odoslaný.");
				} catch (\Throwable $e){
					bdump($e);
					$this->flashMessage($this->logException($e), Message::LEVEL_ERROR,"Testovací e-mail sa nepodarilo odoslať.");
				}
			} else {

				try {
					$sender  = $values->from;
					$subject = $values->subject;

					//." | Klient: ".UserModel::formatClientName($status->ref("trade")->ref("client")->ref("name_list")));
					//$mail->addTo($email);
					$emails = [];
					if ( $values->to == MailingModel::TYPE_USERS ) {
						$users = $form->getHttpData();
						foreach ( $users["users"] as $email ) {
							//$mail->addTo( $email );
							$emails[] = $email;
						}
					} elseif ( isset( MailingModel::TYPES[ $values->to ] ) && $values->to != MailingModel::TYPE_USERS ) {
						$userModel = $this->modelFactory->create( "user" );
						if ( $values->to == MailingModel::TYPE_COWORKERS ) {
							$userModelRequest = $userModel->findBy( [
								"type"              => [
									UserModel::TYPE_COWORKER,
									UserModel::TYPE_SPECIALIST
								],
								"login_allowed"     => 1,
								"enabled"           => 1,
								"marketing_mailing" => 1
							] );
						} elseif ( $values->to == MailingModel::TYPE_SPECIALISTS ) {
							$userModelRequest = $userModel->findBy( [
								"type"              => UserModel::TYPE_SPECIALIST,
								"login_allowed"     => 1,
								"enabled"           => 1,
								"marketing_mailing" => 1
							] );
						} elseif ( $values->to == MailingModel::TYPE_SPECIALISTS_ADV ) {
							$userModelRequest = $userModel->findBy( [
								"type"                => UserModel::TYPE_SPECIALIST,
								"name_list.career_id" => 1,
								"login_allowed"       => 1,
								"enabled"             => 1,
								"marketing_mailing"   => 1
							] );
						} elseif ( $values->to == MailingModel::TYPE_SPECIALISTS_OTHER ) {
							$userModelRequest = $userModel->findBy( [
								"type"                   => UserModel::TYPE_SPECIALIST,
								"name_list.career_id <>" => 1,
								"login_allowed"          => 1,
								"enabled"                => 1,
								"marketing_mailing"      => 1
							] );
						} elseif ( $values->to == MailingModel::TYPE_COWORKERS_ADV ) {
							$userModelRequest = $userModel->findBy( [
								"type"                => [
									UserModel::TYPE_SPECIALIST,
									UserModel::TYPE_COWORKER
								],
								"name_list.career_id" => 1,
								"login_allowed"       => 1,
								"enabled"             => 1,
								"marketing_mailing"   => 1

							] );
						} elseif ($values->to == MailingModel::TYPE_ALL) {
							$userModelRequest = $userModel->findBy( [
								"type"                => [
									UserModel::TYPE_SPECIALIST,
									UserModel::TYPE_COWORKER,
									UserModel::TYPE_CLIENT
								],
								//"name_list.career_id" => 1,
								//"login_allowed"       => 1,
								//"enabled"             => 1,
								"marketing_mailing"   => 1

							] );
						} else {
							throw new InvalidArgumentException( "Typ adresáta nieje definovaný." );
						}

						foreach ( $userModelRequest as $user ) {
							//$mail->addTo($user->username);
							$emails[] = $user->username;
						}
					}
					$bcc = [];
					if ( isset( $form->getHttpData()["bcc"] ) ) {
						foreach ( $form->getHttpData()["bcc"] as $b ) {
							if ( ! in_array( trim( $b ), $emails ) ) {
								$bcc[] = $b;
							}
						}
					}
					//bdump($bcc);
					if ( empty( $emails ) ) {
						throw new InvalidArgumentException( "V tejto skupine adresátov sa nenachádzaju žiadný užívatelia." );
					}

					//exit;
					/*$y = 0;
					foreach($emails as $email) {
						$mail = $this->mailBuilderFactory->create();
						$mail->setSubject( $subject );
						$mail->addTo($email);
						$attachmentDir = WWW_DIR . "/uploads/" . $this->ses->uniq . "/*";
						foreach ( glob( $attachmentDir ) as $file ) {
							$mail->getMessage()->addAttachment( $file );
						}
						if(!empty($bcc) && $y==0){
							foreach($bcc as $b){
								$mail->addBcc($b);
							}
						}

						$mail->setFrom( $sender );
						$mail->getMessage()->addReplyTo( $sender );
						$mail->setTemplateFile( MAIL_DIR . "/message.latte" );
						$mail->setParameters( [ "message" => $values->message, "signature" => "" ] );
						$mail->send();
						$y++;
					}*/

					$data    = [
						"user"            => $this->getUser()->getId(),
						"datetime"        => new DateTime(),
						//"emails" => json_encode($emails),
						"subject"         => $subject,
						"text"            => $values->message,
						"to"              => $values->to,
						"from"            => $values->from,
						"attachments_dir" => $this->ses->uniq,
						"type"            => $values->type,
						"useS3" => ($this->fileManager->getAdapter() instanceof S3Adapter),
					];
					$mailing = $this->modelFactory->create( "mailing" )->insert( $data );
					$mails   = [];
					$emails  = array_merge( $emails, $bcc );
					foreach ( $emails as $email ) {
						$mails[] = [ "mailing_id" => $mailing->id, "email" => $email ];
					}
					$this->modelFactory->create( "mailingSend" )->insert( $mails );
					if ( $this->getParameter( "blog_article_id" ) ) {
						$this->modelFactory->create( "blogArticle" )->find( $this->getParameter( "blog_article_id" ) )->update( [ "sended" => 1 ] );
					}

				} catch ( \Exception $e ) {
					$success = false;
					$this->flashMessage( $e->getMessage(), Message::LEVEL_ERROR, "E-maily sa nepodarilo odoslať." );
				}
				if ( $success ) {
					unset( $this->ses->uniq );
					$this->flashMessage( "E-maily boli zaradené do fronty na odoslanie.", Message::LEVEL_SUCCESS );
					if ( $this->user->isAllowed( $this->resource(), "sent" ) ) {
						$this->redirect( ":Backend:Mailing:sent" );
					} else {
						$this->redirect( "this" );
					}
				}
			}
		};

		return $f;
	}
	protected function createComponentDocumentViewer() : DocumentViewer {
		$dv = $this->context->getByType(DocumentViewerFactory::class)->create($this);
		$dv->setAllowedViews([DocumentViewer::VIEW_LIST]);
		$dv->setAllowDelete(true);
		$dv->setAllowFolders(false);
		$dv->setSortable(false);
		$dv->setDropzone($this["dropzoneForm"]);

		return $dv;
	}
	/**
	 * @return DropzoneUploader
	 */
	protected function createComponentDropzoneForm(): DropzoneUploader
	{
		return $this->dropzoneFactory->getDropzoneUploader();
	}

	public function actionTest(){
		$mail = $this->context->getByType( \Contributte\Mailing\IMailBuilderFactory::class )->create();
		$mail->setSubject( "test");
		$mail->addTo( "<EMAIL>" );


		$mail->setFrom( "<EMAIL>");
		/*if($e->reply !== null && Validators::isEmail($e->reply)){
			$mail->getMessage()->addReplyTo( $e->reply );
		} else {
			$mail->getMessage()->addReplyTo( $e->from );
		}*/
		$mail->setTemplateFile( MAIL_DIR . "/message.latte" );
		$mail->setParameters( [ "message" =>"čau ahoj", "signature" => "podpis" ] );
		$mail->send();
		dumpe($mail);
	}
}