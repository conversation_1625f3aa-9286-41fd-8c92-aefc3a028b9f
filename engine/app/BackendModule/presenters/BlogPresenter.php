<?php
namespace BackendModule;


use <PERSON>es<PERSON><PERSON>\DropzoneUploader\DropzoneUploader;
use <PERSON>es<PERSON><PERSON>\DropzoneUploader\Factory;
use <PERSON><PERSON>mi<PERSON><PERSON>\Components\DocumentViewer;
use <PERSON><PERSON>mi<PERSON><PERSON>\Factory\Control\DocumentViewerFactory;
use Nette\Application\BadRequestException;
use Nette\DI\Attributes\Inject;
use Nette\Utils\Paginator;
use Webtec\Models\BlogArticleModel;
use Webtec\Models\BlogCategoryModel;

class BlogPresenter extends BasePresenter {
	#[Inject]
	public BlogArticleModel $articleModel;
	#[Inject]
	public BlogCategoryModel $categoryModel;
	#[Inject]
	public DocumentViewerFactory $documentViewerFactory;

	protected function startup() {
		parent::startup();
		$this["breadcrumb"]->addItem( "blog", "Novinky", "Blog:default",
			[ "icon" => "far fa-newspaper" ] );

		$this->articleModel->setAdminMode(false);
		$this->categoryModel->setAdminMode(false);

	}

	public function actionDefault(?int $category_id = null, ?int $page = 1){
		if($category_id !== null) {
			$category = $this->categoryModel->find($category_id)->fetch();
			$this["breadcrumb"]->addItem("category",$category->name, "Blog:default",[],  ["category_id" => $category_id]);
			$selection = $this->articleModel->findBy( [ ":blog_article_category.blog_category_id" => $category_id ] );
		} else {
			$selection = $this->articleModel->findAll();

		}
		$selection->where(["blog_article.active" => 1])->order("id DESC");
		$this->getSession("blog")->category = $category_id;
		$this->getSession("blog")->page = $page;

		$articlesCount = $selection->count("*");

		$paginator = new Paginator();
		$paginator->setItemCount($articlesCount); // celkový počet článků
		$paginator->setItemsPerPage(16); // počet položek na stránce
		$paginator->setPage($page); // číslo aktuální stránky

		$articles = $selection->limit($paginator->getLength(),$paginator->getOffset());

		$this->template->articles = $articles;
		$this->template->paginator = $paginator;
		$this->template->category_id = $category_id;


	}


	public function actionArticle(int $id) {
		$article = $this->articleModel->find($id)->fetch();
		if(!$article){
			throw new BadRequestException();
		}

		$reads = $article->related("blog_article_read");
		if(!$reads->where(["user_id"=>$this->getUser()->getId()])->count("*")){
			$reads->insert(["user_id" => $this->getUser()->getId()]);;
		}

		$this["breadcrumb"]->addItem("article",$article->name);
		$this->template->article = $article;
		$this->template->category_id = $this->getSession("blog")->category;
		$this->template->page = $this->getSession("blog")->page;

		$this["documentViewer"]->setDocumentDir(UPLOAD_DIR."/".$article->document_dir);//->setDropzone($this["dropzoneForm"]);
		/*$this["dropzoneForm"]->setDocumentViewer($this["documentViewer"]);
		$this["dropzoneForm"]->onBeginning[] = function (DropzoneUploader $uploader) use ($article): void {
			$uploader->setFolder($article->document_dir);
		};*/

	}

	protected function createComponentDocumentViewer() : DocumentViewer {
		$dv = $this->documentViewerFactory->create($this);
		$dv->setAllowedViews([DocumentViewer::VIEW_LIST]);
		$dv->setAllowFolders(false);
		$dv->setAllowDelete(false);
		$dv->setSortable(false);
		$dv->setTitle("Prílohy");
		return $dv;
	}

}