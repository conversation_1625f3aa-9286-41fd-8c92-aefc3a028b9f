<?php
namespace BackendModule;


use IPub\FlashMessages\Entities\Message;
use Kubomikita\Attributes\Description;
use Nette\Application\BadRequestException;
use Nette\Application\Responses\FileResponse;
use Nette\Database\Explorer;
use Nette\Database\ResultSet;
use Nette\Database\Table\Selection;
use Nette\DI\Attributes\Inject;
use Nette\Utils\ArrayHash;
use Nette\Utils\Paginator;
use Webtec\Models\HelpdeskModel;
use Webtec\UI\Forms\Form;

class HelpdeskPresenter extends BasePresenter {

	#[Inject]
	public HelpdeskModel $helpdeskModel;

	public int $conversationMaxPerPage = 15;
	protected ?Paginator $paginator = null;
	protected array $availableFilterKeys = [
		"trade",
		"closed",
		"favorite"
	];

	public function handleMessage(?int $message_id = null){

		$message = $this->helpdeskModel->find($message_id)->fetch();
		if($message) {
			$this->template->messages = $this->helpdeskModel->getMessages($message_id); //$message;
			$this->template->visible = $this->session->getSection(static::class)->visible;
			$this->session->getSection(static::class)->message_id = $message_id;
			$this->redrawControl("message");
		}
	}

	public function handleDeleteConversation(int $message_id){
		$mids = [];
		foreach ($this->helpdeskModel->getGroupMessages($message_id) as $m){
			$mids[] = $m->id;
		}

		$this->helpdeskModel->findBy(["id" => $mids])->delete();
		$this->template->messages = [];
		$this->template->tickets = $this->getTickects();
		$this->redrawControl("message");
		$this->redrawControl("conversations");
	}
	public function handleCloseConversation(int $message_id) {
		$mids = [];
		foreach ($this->helpdeskModel->getGroupMessages($message_id) as $m){
			$mids[] = $m->id;
		}

		$this->helpdeskModel->findBy(["id" => $mids])->update(["closed"=>1]);
		$this->template->tickets = $this->getTickects();
		$this->redrawControl("conversations");
	}
	public function handleFavoriteConversation(int $message_id) {
		$message = $this->helpdeskModel->find($message_id)->fetch();
		$mids = [];
		foreach ($this->helpdeskModel->getGroupMessages($message_id) as $m){
			$mids[] = $m->id;
		}

		$this->helpdeskModel->findBy(["id" => $mids])->update(["favorite"=>!$message->favorite]);
		$this->template->tickets = $this->getTickects();
		$this->redrawControl("conversations");
	}

	public function handleMessageOpen(?int $message_id  = null,?bool $open = null){
		$this->session->getSection(static::class)->visible[$message_id] = $open;
	}
	public function handleFilter(string $key, ?int $value = null){
		$this->template->filter = $this->payload->query["filter"] = $this->session->getSection(static::class)->filter = [];
		$filter = [];
		if($value !== null) {
			$this->session->getSection(static::class)->filter[$key] = $value;
			$filter[$key] = $value;
			$this->payload->query["filter"] = $this->processFilter($filter);
		} else {
			unset($this->session->getSection(static::class)->filter[$key]);
			unset($this->payload->query["filter"]);
		}

		$this->template->filter = $this->processFilter($filter);
		if(!empty($search = $this->session->getSection(static::class)->query)){
			$this->payload->query["search"] = $search;
		}

		$this->template->paginator = $this->getPaginator()->setItemCount($this->getTicketsCount())->setPage(1);
		$this->template->tickets = $this->getTickects();

		$this->redrawControl("filter");
		$this->redrawControl("conversations");
		$this->redrawControl("paginator");
	}

	public function handlePage(?int $page = null){
		$this->template->paginator = $this->getPaginator()->setItemCount($this->getTicketsCount())->setPage($page === null ? 1 : $page);
		$this->template->tickets = $this->getTickects();
		$this->payload->query = [];

		if(!empty($filter = $this->session->getSection(static::class)->filter)){
			$this->payload->query["filter"] = $filter;
		}
		if(!empty($search = $this->session->getSection(static::class)->query)){
			$this->payload->query["search"] = $search;
		}

		if($page !== null) {
			$this->payload->query["page"] = $page;
		}


		$this->redrawControl("conversations");
		$this->redrawControl("paginator");
	}

	public function actionDefault(int $page = 1, array $filter = [], ?string $search = null) {
		if(!empty($filter) && !$this->isAjax()){
			$this->session->getSection(static::class)->filter = $this->processFilter($filter);
		}
		if($search !== null && strlen(trim($search)) > 0 && !$this->isAjax()){
			$this->session->getSection(static::class)->query = $search;
		}
		$this->template->filter = $this->session->getSection(static::class)->filter;

		$this->template->paginator = $this->getPaginator()->setPage($page);
		$this->template->tickets = $this->getTickects();

		if($message_id = $this->session->getSection(static::class)->message_id){
			$this->template->active = $message_id;
			$this->template->messages = $this->helpdeskModel->getMessages($message_id);
			$this->template->visible = $this->session->getSection(static::class)->visible;
		}

		$this["searchForm"]->setDefaults(["query" => $this->session->getSection(static::class)->query]);
	}

	public function createComponentMessageForm($name) : Form {
		$f = $this->formFactory->create($this,$name);

		return $f;
	}

	public function createComponentSearchForm($name) : Form {
		$f = $this->formFactory->create($this,$name);
		$f->addText("query","Vyhľadávanie");
		$f->addSubmit("search","Vyhľadať");
		$f->onSuccess[] = function (Form $form, ArrayHash $values){
			$this->session->getSection(static::class)->query = $values->query;

			$this->template->paginator = $this->getPaginator()->setItemCount($this->getTicketsCount())->setPage(1);
			$this->template->tickets = $this->getTickects();
			$this->payload->query = [];
			if(!empty($filter = $this->session->getSection(static::class)->filter)){
				$this->payload->query["filter"] = $filter;
			}
			if(strlen(trim($values->query)) > 0 ) {
				$this->payload->query["search"] = $values->query;
			}
			$this->redrawControl("conversations");
			$this->redrawControl("paginator");
			$this->redrawControl("search");
		};
		return $f;
	}

	public function getQueryBuilder() : array
	{
		$connection = $this->helpdeskModel->getDatabaseConnection();
		$where = [
			$connection::literal("(SELECT COUNT(id) FROM helpdesk WHERE message_reply = h.message_id) = 0")
		];
		if(($filter = $this->session->getSection(static::class)->filter) !== null && !empty($filter)){
			foreach($filter as $k => $v){
				if((int) $v === -1){
					$where[] = $connection::literal($k." IS NULL");
				} else {
					$where[$k] = $v;
				}
			}
			//$where
		}

		if(($query = $this->session->getSection(static::class)->query) !== null && strlen($query) > 0){
			$where[] = $connection::literal("?or",[
				"subject LIKE ?" => '%'.$query.'%',
				"message LIKE ?" => '%'.$query.'%',
				"from LIKE ?" => '%'.$query.'%',
			]);
			//$where[] = $connection::literal("MATCH(h.subject, h.message, h.from) AGAINST  (? IN BOOLEAN MODE)", trim($query));
		}
		return $where;
	}
	public function getTicketsCount() : int
	{
		$connection = $this->helpdeskModel->getDatabaseConnection();

		$where = $this->getQueryBuilder();
		$row = $connection->query('SELECT COUNT(h.id) AS pocet FROM `helpdesk` as h WHERE', $where)->fetch();
		return $row->pocet;
	}

	public function getPaginator() : Paginator {
		if($this->paginator === null) {
			$this->paginator = new Paginator();
			$this->paginator->setItemsPerPage($this->conversationMaxPerPage);
			$this->paginator->setItemCount($this->getTicketsCount());
		}
		return $this->paginator;
	}

	public function getTickects(?Paginator $paginator = null) : ResultSet|Selection {
		if($paginator === null){
			$paginator = $this->getPaginator();
		}

		$connection = $this->helpdeskModel->getDatabaseConnection();

		$where = $this->getQueryBuilder();
		$message_ids = $connection->query('SELECT h.id
										FROM `helpdesk` as h
										WHERE',$where,"ORDER BY datetime DESC LIMIT " .$paginator->getLength(). " OFFSET " . $paginator->getOffset())->fetchPairs("id","id");
		return $this->helpdeskModel->findBy(["id" => array_keys($message_ids)])->order("datetime DESC");
	}

	public function processFilter(array $filter = []) : array
	{
		foreach ($filter as $k => $v){
			if(!in_array($k, $this->availableFilterKeys)){
				unset($filter[$k]);
			}
		}
		return $filter;
	}

	public function createComponentModalAddTradeForm($name) :Form
	{
		$f = $this->formFactory->create($this,$name);
		$f->addHidden("id")->addRule($f::INTEGER,"");
		$f->addTrade("trade","Vyhľadajte obchod")->setRequired();
		$f->addSubmit("save","Priradiť");

		$f->onSuccess[] = function (Form $form, ArrayHash $values){
			bdump($values);

			$helpdesk = $this->helpdeskModel->find($values->id);

			if($helpdesk){
				$messagesId = array_values($this->helpdeskModel->getMessages($values->id, true));
				bdump($messagesId);
				$this->helpdeskModel->findBy(["id" => $messagesId])->update(["trade" => $values->trade]);

				$this->flashMessage("Obchod bol priradený.", Message::LEVEL_SUCCESS);
				$this->payload->closeModal = 1;
				$this->redrawControl();

			}


		};
		return $f;
	}
}