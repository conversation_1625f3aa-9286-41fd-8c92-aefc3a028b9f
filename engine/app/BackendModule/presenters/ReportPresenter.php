<?php
namespace BackendModule;

use App\Latte\CurrencyFilter;
use App\Latte\PointsFilter;
use <PERSON>bo<PERSON><PERSON><PERSON>\Attributes\Description;
use <PERSON>bo<PERSON><PERSON>ta\Utils\DateTime;
use Nette\Application\UI\Multiplier;
use Nette\Database\Table\ActiveRow;
use Nette\Database\Table\Selection;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\MeetingModel;
use Webtec\Models\TradeModel;
use Webtec\Models\UserModel;
use Webtec\UI\Forms\Form;

#[Description("Obchodné výsledky")]
class ReportPresenter extends BasePresenter {
	/** @var SessionSection */
	public $ses;
	/** @var UserModel */
	public $userModel;
	/** @var TradeModel */
	public $tradeModel;

	public $dateStart;
	public $dateEnd;

	public $select;

	protected function startup() {
		parent::startup();
		$this->ses =$this->getSession("report_".$this->getUser()->getId());
		$this->ses->setExpiration(0);
		//$this->ses->values = [];

		$this["breadcrumb"]->addItem("report","Obchodné výsledky","Report:default",["icon"=>"fas fa-chart-pie"]);
		$this->userModel = $this->modelFactory->create("user");
		$this->tradeModel = $this->modelFactory->create("trade");
	}

	public function actionDefault(){
		$this["breadcrumb"]->addItem("coworker","Spolupracovníci", "Report:default");
		$ses = $this->getSession("report_".$this->getUser()->getId());

		$defaults = [
			"user" => [$this->getUser()->getId()],
			"start" => new \DateTime("last week monday"),
			"end" => new \DateTime("last week sunday")
		];

		if(!empty($ses->values)){
			$defaults = $ses->values;
		} else {
			$ses->values = $defaults;
		}


		$this["reportForm"]->setDefaults($defaults);

		$monthNames = [1=>"Január","Február","Marec","Apríl","Máj","Jún","Júl","August","September","Október","November","December"];
		$months = [];
		foreach ($monthNames as $order => $name){
			$startDate = new DateTime("1.".$order.".".date("Y"));
			$endDate = $startDate->modifyClone("last day of this month");

			if($endDate->getTimestamp() <= (new DateTime())->modify("last day of this month")->getTimestamp() ) {
				$months[] = [
					"name" => $name . " " . date( "Y" ),
					"value" => Json::encode( [
						"start" => $startDate->format( "d.m.Y" ),
						"end"   => $endDate->format( "d.m.Y" )
					] )
				];
			}
		}
		$this->template->months = $months;

		$numOfWeeks = (int) (new DateTime("31.12.".date("Y")))->format("W");
		$weeks = [];
		for($i=1;$i<=$numOfWeeks;$i++){
			$startDate = new DateTime();
			$startDate->setISODate(2020, $i);
			$endDate = $startDate->modifyClone("+6 day");
			if($endDate->getTimestamp()  <= (new DateTime())->modify("next sunday midnight")->getTimestamp()) {
				$weeks[] = [
					"name" => $i . ". týždeň ". date( "Y" ),
					"value" => Json::encode( [
						"start" => $startDate->format( "d.m.Y" ),
						"end"   => $endDate->format( "d.m.Y" )
					] )
				];
			}
		}
		$this->template->weeks = $weeks;

		$quarterNames= [1 => "1. kvartál",4 => "2. kvartál", 7=> "3. kvartál", 10 => "4. kvartál"];
		$quarters = [];
		foreach ($quarterNames as $order => $name){
			$startDate = new DateTime("1.".$order.".".date("Y"));
			$endDate = $startDate->modifyClone("+2 month");
			$endDate->modify("last day of this month");
			$offset = 3-(date('n')%3); // modulo ftw again
			$endQuarter = (new DateTime("last day of +$offset month"))->modify("23:59:59");

			if($endDate->getTimestamp() <= $endQuarter->getTimestamp()) {
				$quarters[] = [
					"name" => $name. " ". date( "Y" ),
					"value" => Json::encode( [
						"start" => $startDate->format( "d.m.Y" ),
						"end"   => $endDate->format( "d.m.Y" )
					] )
				];
			}
		}

		$this->template->quarters = $quarters;

		$years = [];
		$startYear = 2018;
		do {
			$startDate = new DateTime("1.1.".$startYear);
			$endDate = $startDate->modifyClone("last day of december")->modify("23:59:59");
			$years[] = [
				"name" => "Rok ".$startYear,
				"value" => Json::encode( [
					"start" => $startDate->format( "d.m.Y" ),
					"end"   => $endDate->format( "d.m.Y" )
				] )
			];
			$startYear++;
		} while($startYear <=  (int) date("Y"));
		$this->template->years = $years;
	}

	public function actionTest(){
		/** @var TradeModel $tradeModel */
		$tradeModel = $this->modelFactory->create("trade");

		$trades = UserModel::getGroupTrades( $this->modelFactory, 16, 1, [
			":trade_statuses.trade_status.event LIKE ?" => '%[to_paid]%'
		]);
		dump($trades->count("*"));
		//$trades = self::getGroupTrades( $modelFactory, $user, 1);
		foreach($trades as $t){
			dump(
				$tradeModel->hasStatusEvent("to_paid",$t),
				$tradeModel->hasStatusEventNew("to_paid",$t)
			);
			echo '<hr>';
		}

		$this->terminate();
	}

	public function getInfomeetingCount(ActiveRow $user, \DateTimeInterface $start, \DateTimeInterface $end) : int {
		$where = [
			"user" => $user->id,
			"datetime >=" => $start,
			"datetime <=" => $end
		];

		return $this->modelFactory->create( "meeting" )->findBy( $where  )->count( "*" );
	}


	public function getReport(): ?array {
		//dump();
		$val = $this->getSession("report_".$this->getUser()->getId())->values;
		//dump($val);
		if(empty($val)){
			return null;
		}
		$where = [
			"user" => $val->user,
			"datetime >=" => $val->start,
			"datetime <=" => $val->end
		];
		$r = ["data" => [],"label"=>[]];
		//if($val->infomeeting_on) {
			dump($val);
			$counter = 0;
			foreach($val->infomeeting as $key => $values) {
				if($key == "type") {
					foreach ( $values as $v ) {
						$r["label"][ "infomeeting_" . $key . "_" . $v ] = "" . MeetingModel::TYPES[ (int) $v ] . "";
						$r["data"][ "infomeeting_" . $key . "_" . $v ]  = $this->modelFactory->create( "meeting" )->findBy( $where + [ $key => $v ] )->count( "*" );
						$counter                                        += $r["data"][ "infomeeting_" . $key . "_" . $v ];
					}
				}
			}
			$r["label"]["count"] = "<strong>Spolu meetingov</strong>";
			$r["data"]["count"] = $counter;
			$r["class"]["count"] = "table-success";
			if($val->infomeeting->points) {
				$r["label"]["points"] = "Rozpracované body";
				$r["data"]["points"] = $this->modelFactory->create( "meeting" )->findBy( $where )->sum("points" );;
			}
		//}

		//dump($r);
		return $r;//$start->format("d.m.Y")." - ".$end->format("d.m.Y")." - user ".$user_id;
	}

	protected function createComponentReportForm( string $name ): Form {
		$f = $this->formFactory->create($this, $name);
		$f->addDate("start","Dátum od")->setRequired();
		$f->addDate("end","Dátum do")->setRequired();
		$f->addText("humanDate","Sledované obdobie");

		if($this->isSuperadmin()){
			$coworkers = $this->userModel->fetchList(["type" => [UserModel::TYPE_COWORKER,UserModel::TYPE_SPECIALIST]],false,false);
		} else {
			$coworkers = [];
			foreach($this->userModel->getPath($this->getUser()->getId()) as $user){
				$coworkers[$user->id] = UserModel::formatName($user,false,false);
			}

		}

		$f->addMultiSelect("user","Spolupracovník",$coworkers)->setRequired();

		/*$f->addCheckbox("infomeeting_on","Infomeeting")->addCondition($f::EQUAL,true)->toggle("meeting-container");
		$infomeeting = $f->addContainer("infomeeting");
		$infomeeting->addMultiSelect("type","Typ meetingu",["Typ meetingu" => MeetingModel::TYPES])
		  ->addConditionOn($f["infomeeting_on"],Form::FILLED,true)
		    ->setRequired();
		$infomeeting->addCheckbox("points","Rozpracované body");
*/

		$f->addSubmit("save","Filtrovať");
		$f->onError[] = function (Form $form){
			bdump($form);
		};
		$f->onSuccess[] = function (Form $form){

			$values = $form->getValues();
			/*foreach($values->user as $key => $id){
				$values->user[$key] = ArrayHash::from($this->userModel->find($id)->fetch()->toArray());
			}
			bdump($values);*/
			$this->getSession("report_".$this->getUser()->getId())->values = $values;
			$this->redrawControl();
		};
		return $f;
	}
	public $provisedTradesSelection;
	public function getDivisionSelection(int $l_section_type, \DateTimeInterface $dateStart, \DateTimeInterface $dateEnd, int $user_id = null) : Selection
	{
		$whereOr = [];
		if($user_id !== null) {
			$path = $this->userModel->getPath( $user_id, true, UserModel::LIST_TYPE_ID );
			$whereOr = [ "trade.client" => $path, "trade.user" => $path];
		}

		$dateStart->modify("today");
		$dateEnd->modify("tomorrow")->modify("-1 second");


		$company_id = $this->companyService->getCompanyId();

		$select = $params = [];
		$select[] = "l_section_type.*";
		if(empty($whereOr)) {
			$select[] = "(SELECT COUNT(id) FROM trade WHERE l_section_type = l_section_type.id AND datetime >= '" . $dateStart . "' AND datetime <= '" . $dateEnd . "') AS created_trades";
		} else {
			$select[] = "(SELECT COUNT(id) FROM trade WHERE l_section_type = l_section_type.id AND datetime >= '" . $dateStart . "' AND datetime <= '" . $dateEnd . "' AND (client IN (?) OR user IN (?))) AS created_trades";
			$params[] = $path;
			$params[] = $path;
		}

		$realisedTrades = $this->tradeModel->findAll()->where([
			":trade_statuses.trade_status.event LIKE ?" => '%[realised]%',
			":trade_statuses.datetime >=" => $dateStart,
			":trade_statuses.datetime <=" => $dateEnd,
			"trade.l_section_type" => "l_section_type.id"
		])->whereOr($whereOr);
		$realisedTradesValue = clone $realisedTrades;

		$this->provisedTradesSelection = $this->tradeModel->findAll()->where([
			":trade_statuses.trade_status.event LIKE ?" => '%[to_paid]%',
			":trade_statuses.datetime >=" => $dateStart,
			":trade_statuses.datetime <=" => $dateEnd,
			//"trade.l_section_type" => "l_section_type.id"
		])->whereOr($whereOr);

		$provisedTrades = $this->tradeModel->findAll()->where([
			":trade_statuses.trade_status.event LIKE ?" => '%[to_paid]%',
			":trade_statuses.datetime >=" => $dateStart,
			":trade_statuses.datetime <=" => $dateEnd,
			"trade.l_section_type" => "l_section_type.id"
		])->whereOr($whereOr);

		$provisedTradesValue = clone $provisedTrades;
		$provisedTradesPoints = clone $provisedTrades;
		$provisedTradesProvision = clone $provisedTrades;
		$provisedTradesInvoice = clone $provisedTrades;
		$provisedTradesInvoicePeriod = clone $provisedTrades;


		$params[] = $company_id;
		$params[] = '%[realised]%';
		$params[] = $dateStart;
		$params[] = $dateEnd;

		if(!empty($whereOr)){
			$params[] = $path;
			$params[] = $path;
		}

		$select[] = str_replace("l_section_type` = ?","l_section_type` = l_section_type.id", "(".$realisedTrades->select("COUNT(trade.id)")->getSql().") AS realised_trades");

		$params[] = $company_id;
		$params[] = '%[to_paid]%';
		$params[] = $dateStart;
		$params[] = $dateEnd;
		if(!empty($whereOr)){
			$params[] = $path;
			$params[] = $path;
		}

		$select[] = str_replace("l_section_type` = ?","l_section_type` = l_section_type.id", "(".$provisedTrades->select("COUNT(trade.id)")->getSql().") AS provised_trades");

		$params[] = $company_id;
		$params[] = '%[realised]%';
		$params[] = $dateStart;
		$params[] = $dateEnd;
		if(!empty($whereOr)){
			$params[] = $path;
			$params[] = $path;
		}

		$select[] = str_replace("l_section_type` = ?","l_section_type` = l_section_type.id", "(".$realisedTradesValue->select("SUM(trade.base)")->getSql().") AS realised_value");

		$params[] = $company_id;
		$params[] = '%[to_paid]%';
		$params[] = $dateStart;
		$params[] = $dateEnd;
		if(!empty($whereOr)){
			$params[] = $path;
			$params[] = $path;
		}

		$select[] = str_replace("l_section_type` = ?","l_section_type` = l_section_type.id", "(".$provisedTradesValue->select("SUM(trade.base)")->getSql().") AS provised_value");

		$params[] = $company_id;
		$params[] = '%[to_paid]%';
		$params[] = $dateStart;
		$params[] = $dateEnd;
		if(!empty($whereOr)){
			$params[] = $path;
			$params[] = $path;
		}

		if($user_id !== null){
			$provisedTradesPoints->where([":user_points.user" => $user_id]);
			$params[] = $user_id;
		} else {
			$provisedTradesPoints->where([":user_points.user" => [16,33]]);
			$params[] = array_keys($this->getOwner());
			//bdump($this->getOwner());
		}

		/*$provisedTradesPoints->where([":user_points.datetime >=" => $dateStart, ":user_points.datetime <=" => $dateEnd]);
		$params[] = $dateStart;
		$params[] = $dateEnd;
*/
		$select[] = str_replace("l_section_type` = ?","l_section_type` = l_section_type.id", "(".$provisedTradesPoints->select("SUM(:user_points.points)")->getSql().") AS provised_points");

		$params[] = $company_id;
		$params[] = '%[to_paid]%';
		$params[] = $dateStart;
		$params[] = $dateEnd;

		if(!empty($whereOr)){
			$params[] = $path;
			$params[] = $path;
		}

		$provisedTradesProvision->where([":trade_provision.datetime >=" => $dateStart, ":trade_provision.datetime <=" => $dateEnd]);
		$params[] = $dateStart;
		$params[] = $dateEnd;

		$select[] = str_replace("l_section_type` = ?","l_section_type` = l_section_type.id", "(".$provisedTradesProvision->select("SUM(:trade_provision.provision)")->getSql().") AS provised_provision");

		$params[] = $company_id;
		$params[] = '%[to_paid]%';
		$params[] = $dateStart;
		$params[] = $dateEnd;
		if(!empty($whereOr)){
			$params[] = $path;
			$params[] = $path;
		}

		$select[] = str_replace("l_section_type` = ?","l_section_type` = l_section_type.id", "(".$provisedTradesInvoice->select("SUM(:bank_movements_trade.bank_movements.volume)")->getSql().") AS provised_invoice");

		$params[] = $company_id;
		$params[] = '%[to_paid]%';
		$params[] = $dateStart;
		$params[] = $dateEnd;
		if(!empty($whereOr)){
			$params[] = $path;
			$params[] = $path;
		}
		$params[] = $dateStart;
		$params[] = $dateEnd;

		$select[] = str_replace("l_section_type` = ?","l_section_type` = l_section_type.id", "(".$provisedTradesInvoicePeriod->select("SUM(:bank_movements_trade.bank_movements.volume)")->where([":bank_movements_trade.bank_movements.date >="=>$dateStart,":bank_movements_trade.bank_movements.date <="=>$dateEnd])->getSql().") AS provised_invoice_period");

		return $this->modelFactory->create("lSectionType")
								//	->findAll()
		                          ->findBy(["l_section" => $l_section_type])
		                          ->select(implode(", ", $select), ... $params)->order("l_section");
	}


	public function preparePeriodType(string $periodType, \DateTimeInterface $dateStart, \DateTimeInterface $dateEnd) {

		if($periodType === 'year') {
			$this->template->periodNameFormat = 'Rok ' . $dateStart->format("Y");
			$this->template->prevDate = $dateStart->modifyClone('-1 year')->format('Y-m-d');
			$this->template->nextDate = $dateStart->modifyClone('+1 year')->format('Y-m-d');
		} elseif ($periodType === "month"){
			$this->template->periodNameFormat =  $dateStart->format("m/Y");
			$this->template->prevDate = $dateStart->modifyClone('-1 month')->format('Y-m-d');
			$this->template->nextDate = $dateStart->modifyClone('+1 month')->format('Y-m-d');
		} elseif($periodType === "custom"){
			$this->template->periodNameFormat =  $dateStart->format("d.m.Y") ." - ". $dateEnd->format("d.m.Y");
			$this->template->prevDate = null;
			$this->template->nextDate = null;
		}
	}

	public function actionDivision(){
		$this["breadcrumb"]->addItem("division","Divízie", "Report:division");
		//unset($this->getSession("division_".$this->getUser()->getId());
		//$this->getSession("division_".$this->getUser()->getId())->section = [];

		$sessionDateStart = $this->getSession("division_".$this->getUser()->getId())->dateStart;
		$sessionDateEnd = $this->getSession("division_".$this->getUser()->getId())->dateEnd;
		$sessionUser = $this->getSession("division_".$this->getUser()->getId())->user;
		$sessionSection = $this->getSession("division_".$this->getUser()->getId())->section;
		$sessionReportType = $this->getSession("division_".$this->getUser()->getId())->reportType;

		if(!$this->isSuperadmin()){
			$sessionUser = $this->getUser()->getId();
			$this->getSession("division_".$this->getUser()->getId())->user = $sessionUser;
		}

		if($sessionSection === null){
			$this->getSession("division_".$this->getUser()->getId())->section = [];
			$sessionSection = [];
		}

		if($sessionDateEnd === null || $sessionDateStart === null){
			$dateStart = (new DateTime())->modify("first day of this month");
			$dateEnd = (new DateTime())->modify("last day of this month");

			$this->getSession("division_".$this->getUser()->getId())->dateStart = $dateStart;
			$this->getSession("division_".$this->getUser()->getId())->dateEnd = $dateEnd;
		} else {
			$dateStart = $sessionDateStart;// (new DateTime("01.".$month.".".$year));
			$dateEnd = $sessionDateEnd;// $dateStart->modifyClone("last day of this month");
		}

		if($sessionReportType === null){
			$this->getSession("division_".$this->getUser()->getId())->reportType = $sessionReportType =  "month";
		}

		$this->template->periodTypeCheck = $sessionReportType;
		$this->template->periodType = ["month" => "mesiac", "year" => "rok", "custom" => "vlastné"];

		$this->preparePeriodType($sessionReportType, $dateStart, $dateEnd);

		//bdump($dateStart);
		//bdump($dateEnd);
		$this->template->dateStart = $dateStart;
		$this->template->dateEnd = $dateEnd;

		$this->template->users = $this->userModel->fetchList(["type" => [UserModel::TYPE_COWORKER, UserModel::TYPE_SPECIALIST]], false, false);

		$this->template->sections = [];
		$this->template->sectionsAll = $this->modelFactory->create("lSection")->findAll()->fetchPairs("id","name");

		foreach($this->modelFactory->create("lSection")->findBy(["id IN (?)" => $sessionSection]) as $section) {
			/** @var DataGrid $datagrid */
			$datagrid = $this["divisionGrid-".$section->id];
			$datagrid->addFilterSearchSelect("name","",$section->related("l_section_type")->fetchPairs("id","name"), "l_section_type.id");
			$datagrid->setDataSource( $this->getDivisionSelection( $section->id, $dateStart, $dateEnd, $sessionUser ) );
			$this->template->sections[$section->id] = $section->name;
		}
		//dumpe($dateStart, $dateEnd);

	}

	public function handleChangeDate($date) {
		//bdump($date);
		$periodType = $this->getSession("division_".$this->getUser()->getId())->reportType;
		$date = new DateTime($date);
		if($periodType === "month") {
			$this->template->dateStart = $date->modifyClone("first day of this month");
			$this->template->dateEnd   = $date->modifyClone("last day of this month");
		} elseif ($periodType === "year"){
			$this->template->dateStart = $date->modifyClone("first day of january ".$date->format("Y"));
			$this->template->dateEnd   = $date->modifyClone("last day of december ".$date->format("Y"));
		}

		$this->getSession("division_".$this->getUser()->getId())->dateStart = $this->template->dateStart;
		$this->getSession("division_".$this->getUser()->getId())->dateEnd = $this->template->dateEnd;
		$user = $this->getSession("division_".$this->getUser()->getId())->user;

		if(!$this->isSuperadmin()){
			$user = $this->getUser()->getId();
			$this->getSession("division_".$this->getUser()->getId())->user = $user;
		}

		foreach($this->modelFactory->create("lSection")->findAll() as $section) {
			$this["divisionGrid-".$section->id]->setDataSource($this->getDivisionSelection($section->id,$this->template->dateStart, $this->template->dateEnd, $user));
			$this["divisionGrid-".$section->id]->reload();
		}

		$this->preparePeriodType($periodType, $this->template->dateStart,$this->template->dateEnd);
		//bdump($this->template->dateStart);
		$this->redrawControl("reportHeader");
		$this->redrawControl("periodFilter");
	}

	public function handleReportUser(){

		$id = ((int) $this->getParameter("user") > 0 ? $this->getParameter("user") : null);


		$this->getSession("division_".$this->getUser()->getId())->user = $id;
		$this->template->dateStart = $this->getSession("division_".$this->getUser()->getId())->dateStart;
		$this->template->dateEnd = $this->getSession("division_".$this->getUser()->getId())->dateEnd;

		if(!$this->isSuperadmin()){
			$id = $this->getUser()->getId();
			$this->getSession("division_".$this->getUser()->getId())->user = $id;
		}

		foreach($this->modelFactory->create("lSection")->findAll() as $section) {
			$this["divisionGrid-".$section->id]->setDataSource($this->getDivisionSelection($section->id,$this->template->dateStart, $this->template->dateEnd, $id));
			$this["divisionGrid-".$section->id]->reload();
		}

		$this->redrawControl("reportHeader");

	}

	public function handleReportSection(){
		$section = (int) $this->getParameter("section");

		$sessionSections = $this->getSession("division_".$this->getUser()->getId())->section;
		if(isset($sessionSections[$section])){
			unset($this->getSession("division_".$this->getUser()->getId())->section[$section]);
		} else {
			$this->getSession("division_".$this->getUser()->getId())->section[$section] = $section;
		}
		//[(int) $this->getParameter("section")] = (int) $this->getParameter("section");
		$this->redirect("this");
	}

	public function handleReportPeriodType() {
		bdump($this->getParameters());
		$reportType = $this->getParameter("periodType");
		$this->getSession("division_".$this->getUser()->getId())->reportType = $reportType;
		if($reportType === "month"){
			$month = $this->getParameter("month");
			$month = new DateTime($month);

			$this->getSession("division_".$this->getUser()->getId())->dateStart = $month->modifyClone("first day of this month");
			$this->getSession("division_".$this->getUser()->getId())->dateEnd = $month->modifyClone("last day of this month")->modify("23:59:59");
		} elseif ($reportType === "year"){
			$year = $this->getParameter("year");
			$year = new DateTime($year."-01-01");
			$this->getSession("division_".$this->getUser()->getId())->dateStart = $year->modifyClone("first day of january this year")->modify("today midnight");
			$this->getSession("division_".$this->getUser()->getId())->dateEnd = $year->modifyClone("last day of december this year")->modify("23:59:59");
		} elseif($reportType === "custom"){
			$from = new DateTime($this->getParameter("from"));
			$to = (new DateTime($this->getParameter("to")))->modifyClone("+1 day")->modify("today midnight");
			$this->getSession("division_".$this->getUser()->getId())->dateStart = $from;
			$this->getSession("division_".$this->getUser()->getId())->dateEnd = $to;
		}

		//bdump($this->getSession("division_".$this->getUser()->getId())->dateStart);
		$this->redirect("this");
	}

	public function createComponentDivisionGrid($name) {

		return new Multiplier(function (){
			$d = $this->dataGridFactory->create();
			//$d->addColumnText( "section", "Oblasť obchodu","l_section.name" )->addCellAttributes( [ "width" => 150 ] );//->setFilterSearchSelect($this->modelFactory->create("lSection")->findAll()->fetchPairs("name","name"));
			$d->setItemsDetail();
			$d->setTemplateFile(__DIR__ . '/../templates/Report/@detail_row.latte');
			$d->addColumnText( "name", "Druh obchodu", "l_section_type.id" )->addCellAttributes(["width" => 480])->setRenderer( function ( $item ) {
				return $item->name;
			} );
			$d->addColumnText( "created_trades", "Počet tipov (ks)" )->setAlign( "center" )->setSortable();
			$d->addColumnText( "realised_trades", "Počet zrealizovaných (ks)" )->setAlign( "center" )->addCellAttributes(["style" => "border-left: 2px solid #bbb"])->setSortable();
			$d->addColumnText( "realised_value",
				"Hodnota zrealizovaných" )->setAlign( "right" )->setRenderer( function ( $item ) {
				return ( new CurrencyFilter() )( $item->realised_value );
			} )->addCellAttributes(["style" => "border-right: 2px solid #bbb"])->setSortable();

			$d->addColumnText( "provised_trades", "Počet provizovaných (ks)" )->setAlign( "center" )->setSortable();
			$d->addColumnText( "provised_value", "Hodnota provizovaných" )->setAlign( "right" )->setRenderer( function (
				$item
			) {
				return ( new CurrencyFilter() )( $item->provised_value );
			} )->setSortable();
			$d->addColumnText( "provised_points", "Body provizovaných" )->setAlign( "right" )->setRenderer( function (
				$item
			) {
				return ( new PointsFilter() )( $item->provised_points );
			} )->addCellAttributes(["style" => "border-right: 2px solid #bbb"])->setSortable();
			if($this->isSuperadmin()) {
				$d->addColumnText( "provised_provision",
					"Prijatá provízia" )->setAlign( "right" )->setRenderer( function (
					$item
				) {
					return ( new CurrencyFilter() )( $item->provised_provision );
				} )->setSortable();


				$d->addColumnText( "provised_invoice",
					"Prijaté platby za obchody" )->setAlign( "right" )->setRenderer( function ( $item ) {
					return ( new CurrencyFilter() )( $item->provised_invoice );
				} )->setSortable();
				$d->addColumnText( "provised_invoice_period",
					"Prijaté platby za obdobie" )->setAlign( "right" )->setRenderer( function ( $item ) {
					return ( new CurrencyFilter() )( $item->provised_invoice_period );
				} )->setSortable();
				$d->setColumnsSummary( [
					"created_trades",
					"realised_trades",
					"provised_trades",
					"realised_value",
					"provised_value",
					"provised_points",
					"provised_provision",
					"provised_invoice",
					"provised_invoice_period"
				] )
				  ->setFormat( "created_trades", 0, ".", " " )
				  ->setFormat( "realised_trades", 0, ".", " " )
				  ->setFormat( "provised_trades", 0, ".", " " )
				  ->setFormat( "realised_value", 2, ".", " ", " €" )
				  ->setFormat( "provised_value", 2, ".", " ", " €" )
				  ->setFormat( "provised_points", 4, ".", " ", " b" )
				  ->setFormat( "provised_provision", 2, ".", " ", " €" )
				  ->setFormat( "provised_invoice", 2, ".", " ", " €" )
				  ->setFormat( "provised_invoice_period", 2, ".", " ", " €" );
			} else {
				$d->removeColumn("provised_provision");
				$d->removeColumn("provised_invoice");
				$d->removeColumn("provised_invoice_period");
			}

			$d->setStrictSessionFilterValues( false );
			$d->setPagination( false );

			return $d;
		});
	}
}