<?php

namespace BackendModule;

use App\Services\PayBySquareDecoderApi;
use GuzzleHttp\Client;
use Imagick;
use <PERSON><PERSON>mi<PERSON><PERSON>\FileManager\Adapters\S3Adapter;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\FileManager\FileManager;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Utils\Strings;
use Nette\DI\Attributes\Inject;
use Nette\Http\Url;
use Nette\Utils\FileSystem;
use Nette\Utils\Image,
	Nette\Application\BadRequestException;

use Smalot\PdfParser\Parser;
use Tracy\Debugger;
use Webtec\UI\Forms\Form;

class PhotoPresenter extends BaseUnloggedPresenter
{
	protected $checkPrivileges = false;

	const CACHE_DIR = WWW_DIR."/imgcache";
	const EXT_TO_TYPE = [
		"jpg" => Image::JPEG,
		"jpeg" => Image::JPEG,
		"png" => Image::PNG
	];


	public function actionPdf(string $url, string $ext, int $width=null,int $height=null, int $type = null, ?int $page = null/*, ?string $crc = null*/) : void {
		$cached_file = "pdf/".$url.($width > 0 ? '_w' . $width : null) . ($height > 0 ? '_h' . $height : null) . ($type > 0 ?"_t".$type:null).($page !== null ? '_p'.$page : null).".".$ext;
		/** @var PayBySquareDecoderApi $payBySquareDecoderApi */
		$payBySquareDecoderApi = $this->context->getByType(PayBySquareDecoderApi::class);

		$page = $page ?? 1;

		/*try {
			if(Strings::contains($url, "amazonaws.com")) {
				$file_url    = "https://" . $url . ".pdf";
				$guzzle      = new Client();
				$response    = $guzzle->get($file_url);
				$source_file = $response->getBody()->getContents();
			} else {
				$source_file = file_get_contents(WWW_DIR."/".$url.".pdf");
			}
		} catch (\Throwable $e){
			throw new BadRequestException( "Source image not exists: '$cached_file'" );
		}*/

		if(Strings::contains($url, "amazonaws.com")) {
			$source_file = "https://" . $url . ".pdf";
		} else {
			$source_file = $this->companyService->getCronDomain(false)."/".$url.".pdf";
		}
		//dump($source_file);
		//exit;
		try {
			$response = $payBySquareDecoderApi->convertPdfToJpg($source_file, isBlob: false);
		} catch (\Throwable){
			$response = null;
		}

		$responseIndex = $page - 1;

		try {
			if (isset($response["images"][$responseIndex])) {
				$image = Image::fromString(base64_decode($response["images"][$responseIndex]["content"]));
			} else {
				$tempFile = TEMP_DIR . "/" . uniqid(time()) . "." . $ext;
				file_put_contents($tempFile, $source_file);
				$im = new Imagick();
				$im->setResolution(50,50);
				$im->readImage($tempFile . '['.$responseIndex.']');
				if ($ext === "jpg") {
					$im->setImageFormat("jpeg");
					$im->setImageCompression(Imagick::COMPRESSION_LOSSLESSJPEG);
					$im->setImageCompressionQuality(100);
				} elseif ($ext === "png") {
					$im->setImageFormat("png");
				}

				$im = $im->mergeImageLayers(Imagick::LAYERMETHOD_FLATTEN);

				unlink($tempFile);

				$image = Image::fromString($im->getImageBlob());
				$im->clear();
				$im->destroy();
			}
		} catch (\Throwable $e){
			throw new BadRequestException('Error while creating thumbnail of PDF: '.$e->getMessage());
		}


		$this->createDirectoryTree($cached_file);
		$this->createCachedImage($image, $cached_file, $width, $height, $type, $ext);

		$this->terminate();
	}


	public function actionResize(string $url, string $ext, int $width=null,int $height=null, int $type = null):void{
		if(!Strings::contains($url, "amazonaws.com")) {
			$source_file = WWW_DIR . "/" . $url . "." . $ext;
			if ( ! file_exists( $source_file ) ) {
				throw new BadRequestException( "Source image not exists: '$source_file'" );
			}
			$source_file = Image::fromFile($source_file);
		} else {
			try {
				$image_url = "https://" . $url . "." . $ext ;

				$guzzle = new Client();
				$response = $guzzle->get( $image_url );
				$source_file = $response->getBody()->getContents();
				$source_file = Image::fromString( $source_file );
			} catch (\Throwable $e){
				bdump($e);
				throw new BadRequestException("Source image not exists: '$image_url'");
			}
		}
		//dumpe($source_file, Image::detectTypeFromString($source_file));
		//$cached_file = $url."_w".$width."_h".$height.".".$ext;
		$cached_file = $url.($width > 0 ? '_w' . $width : NULL) . ($height > 0 ? '_h' . $height : NULL) . ($type > 0 ?"_t".$type:NULL).".".$ext;
		list($save_dir,$filename) = $this->createDirectoryTree($cached_file);
		$this->createCachedImage($source_file,$cached_file,$width,$height,$type,Strings::lower($ext));
	}

	/**
	 * @param string $file
	 *
	 * @return array
	 */
	private function createDirectoryTree(string $file):array{
		$dirs = explode("/",$file);
		$filename = end($dirs);
		unset($dirs[count($dirs) - 1]);
		$path = self::CACHE_DIR;
		foreach($dirs as $dir){
			$create = $path."/".$dir;
			if(!file_exists($create)) {
				FileSystem::createDir( $create );
			}
			$path .= "/".$dir;
		}
		return [$path,$filename];

	}

	/**
	 * @param string $source_file
	 * @param string $cached_file
	 * @param int|null $width
	 * @param int|null $height
	 * @param int|null $type
	 * @param string $ext
	 *
	 * @throws BadRequestException
	 * @throws \Nette\Application\AbortException
	 * @throws \Nette\Utils\ImageException
	 * @throws \Nette\Utils\UnknownImageFileException
	 */
	private function createCachedImage(/*string $source_file*/$image,string $cached_file,int $width=null,int $height = null, int $type = null,string $ext):void{
		//dumpe($cached_file);
		//try {
			//$image = Image::fromFile($source_file);
			if($type !== null) {
				$iwidth=$image->getWidth();
				$iheight=$image->getHeight();

				$thumb_width= (int) $width;
				$thumb_height=(int) $height;

				if($type === 5) {
					$original_aspect = $iwidth / $iheight;
					$thumb_aspect    = $thumb_width / $thumb_height;
					if ( $original_aspect >= $thumb_aspect ) {
						$new_height = $thumb_height;
						$new_width  = $iwidth / ( $iheight / $thumb_height );
						$top        = 0;
						$left       = (int) round(( ( $new_width - $thumb_width ) / 2 ),0);
					} else {
						$new_width  = $thumb_width;
						$new_height = $iheight / ( $iwidth / $thumb_width );
						$left       = 0;
						$top        = (int) round(( $new_height - $thumb_height ) / 2,0);
					}
					//dump($new_width,$new_height,$top,$left);
					$new_height = (int) round($new_height,0);
					$new_width = (int) round($new_width,0);
					$image->resize( $new_width, $new_height );
					$image->sharpen();
					$image->crop( $left, $top, $new_width, $new_height );

					$white = Image::rgb( 255, 255, 255 );
					$im    = Image::fromBlank( $thumb_width, $thumb_height, $white );
					$im->place( $image );
					$image = $im;
				} elseif ($type == 1){
					$image->resize($thumb_width,$thumb_height,Image::STRETCH);
				} elseif ($type == 2){
					$image->resize($thumb_width,null,Image::SHRINK_ONLY | Image::FIT);
				} elseif ($type == 3){
					$white = Image::rgb(255,255,255);
					$im = Image::fromBlank($thumb_width,$thumb_height,$white);
					$im->place($image->resize($thumb_width,$thumb_height,Image::SHRINK_ONLY | Image::FIT),"50%","50%");
					$image = $im;
				} elseif ($type == 4){
					$image->resize(null,$thumb_height,Image::SHRINK_ONLY | Image::FIT);
				} else {
					throw new BadRequestException("Invalid type of crop specified");
				}
			} else {
				if ( $width && $height ) {
					if ( $image->width < $width && $image->height < $height ) {
						$image = Image::fromBlank( $width, $height, Image::rgb( 0xFF, 0xFF, 0xFF ) )->place( $image,
							'50%', '50%' );
					} else {
						$copy = $image;
						$copy->resize( $width, $height );
						$image = Image::fromBlank( $width, $height, Image::rgb( 0xFF, 0xFF, 0xFF ) );
						$image->place( $copy, '50%', '50%' );
					}
					$image->crop( '50%', '50%', $width, $height );

				} else if ( $width || $height ) {
					$image->resize( $width, $height, Image::FIT | Image::FILL );
				}
			}

			$cached_file_path = self::CACHE_DIR."/".$cached_file;

			// Save to cache
			$image->save($cached_file_path);

			// http caching
			$response = $this->getHttpResponse();
			$response->setHeader('Last-modified', date('r', strtotime('-1 day')));
			$response->setHeader('Expires', date('r', strtotime('+1 day')));
			$response->setHeader('ETag', md5(filesize($cached_file_path)));
			$response->setHeader('Cache-control', 'max-age=86400');

			// render & save to cache
			$image->send(self::EXT_TO_TYPE[$ext]);
			//$image->save(self::CACHE_DIR."/".$cached_file);

		//} catch (\Exception $e) {
			//Image::fromFile(WWW_DIR . '/images/none.gif')->resize($width, $height, Image::FILL)->send(Image::GIF);
		//}

		$this->terminate();

	}
}
