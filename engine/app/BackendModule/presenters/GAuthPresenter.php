<?php
namespace BackendModule;

use IPub\FlashMessages\Entities\Message;
use Nette\Application\Responses\JsonResponse;
use Nette\Database\Connection;
use Nette\Http\Request;
use Nette\Http\SessionSection;
use Nette\Utils\DateTime;
use <PERSON>\Debugger;
use Tracy\Logger;
use Webtec\Models\ModelFactory;
use Webtec\Models\NameListModel;
use Webtec\Models\TradeModel;
use Webtec\Models\UserModel;

class GAuthPresenter extends BasePresenter {

	protected bool $checkPrivileges = false;

	private string $client_secret = __DIR__ . "/../../../temp/client_secret.json";
	private array $scopeToAction = [
		\Google_Service_Calendar::CALENDAR => ":Backend:GAuth:calendar"
	];

	private SessionSection $ses;

	public function startup() {
		parent::startup();
		$this->ses = $this->getSession("googleAuth");
		$this->ses->setExpiration("+1 day");
	}

	public function actionCalendar(){
		$link = $this->link(":Backend:Default:default", ["do" => "calendar-synchronizeCalendar"]);
		bdump($link);
		$this->redirectUrl($link);

	}

	/**
	 * @param null $code
	 * @param null $scope
	 *
	 * @throws \Google_Exception
	 * @throws \Nette\Application\AbortException
	 * @throws \Nette\Application\UI\InvalidLinkException
	 */
	public function actionAuthenticate($code = null,$scope = null,$error = null){
		//$ses = $this->ses;
		$redirect_uri = $this->httpRequest->getUrl()->getHostUrl() . $this->link( ":Backend:GAuth:authenticate" );
		if($_SERVER["HTTP_HOST"] != "localhost"){
			$redirect_uri = str_replace("http://","https://",$redirect_uri);
		}

		$client = new \Google_Client();
		$client->setAuthConfig($this->client_secret);
		$client->setRedirectUri($redirect_uri);
		$client->addScope(\Google_Service_Calendar::CALENDAR);
		$client->setAccessType("online");

		if ($code === null) {
			$auth_url = $client->createAuthUrl();
			//echo $auth_url;
			//echo '<br>'.$redirect_uri;
			$this->redirectUrl(filter_var($auth_url, FILTER_SANITIZE_URL));
		} else {
			//dumpe($code,$scope);
			$access_token = $client->fetchAccessTokenWithAuthCode($code);
			$this->ses->access_token8 = $client->getAccessToken();
			//$this->ses->setExpiration($access_token["expires_in"]);
			//dump($this->ses);
			//dump($client,$this->ses,$client->getAccessToken(),$access_token);
			$this->redirect( $this->scopeToAction[$scope] );
		}

		exit;
	}
}