<?php

namespace BackendModule\SettingModule;



use IPub\FlashMessages\Entities\Message;
use JetBrains\PhpStorm\Deprecated;
use Kubomi<PERSON>ta\Utils\Csv;
use Nette\Application\BadRequestException;
use Nette\Application\ForbiddenRequestException;
use Nette\Application\UI\Multiplier;
use Nette\Caching\Cache;
use Nette\Database\Table\ActiveRow;
use Nette\DI\Attributes\Inject;
use Nette\Http\FileUpload;
use Nette\Schema\Expect;
use Nette\Schema\Processor;
use Nette\Utils\ArrayHash;
use Nette\Utils\DateTime;
use Nette\Utils\FileSystem;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Nette\Utils\Strings;
use Nette\Utils\Validators;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\Column\ColumnText;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\ImportCoworkerItemsModel;
use Webtec\Models\ImportCoworkerModel;
use Webtec\Models\ImportModel;
use Webtec\Models\ImportProvisionItemsModel;
use Webtec\Models\ImportProvisionModel;
use Webtec\Models\TradeModel;
use Webtec\Models\UserModel;
use Webtec\UI\Forms\Form;

class ImportCoworkerPresenter extends ImportPresenter
{

	protected string $MODULE_NAME = 'ImportCoworker';

	#[Inject]
	public ImportCoworkerModel $importModel;
	#[Inject]
	public ImportCoworkerItemsModel $importItemsModel;
	#[Inject]
	public UserModel $userModel;
	#[Inject]
	public TradeModel $tradeModel;

	protected function startup()
	{
		parent::startup();
		$this["breadcrumb"]->addItem("import", "Import spolupracovníkov", ":Backend:Setting:ImportCoworker:default");

	}

	public function actionDefault()
	{
		parent::actionDefault(); // TODO: Change the autogenerated stub

		$this["modalImportForm"]->addCheckbox("skipEmpty", "Neprepisovať prázdne hodnoty");
		$this["importGrid"]->addColumnText("skipEmpty","Neprepisovať prázdne hodnoty")->addCellAttributes(["width" => 50])->setAlign("center")->setRenderer(fn($item) => $item->skipEmpty ? '<i class="fas fa-check-square tx-success"></i>' : '<i class="far fa-minus-square tx-danger"></i>')->setTemplateEscaping(false);
		$this["importGrid"]->addAction("email","Overiť e-maily","checkEmails!")->setIcon("at")->setClass("btn btn-xs btn-warning ajax");
		$this["importGrid"]->allowRowsAction("email", function ($item){
			return (Strings::contains((string) $item->file_message, "Neplatný e-mail")) && !Strings::contains((string) $item->file_message,"Neplatné e-maily:");
		});
	}

	public function handleCheckEmails(int $id){

		$import = $this->importModel->find($id)->fetch();

		$f = new \SplFileObject($import->file);
		$f->setFlags(\SplFileObject::READ_CSV);
		$f->setCsvControl(";");

		$badEmails = [];
		foreach ($f as $i => $cols) {
			if ($i === 0 && $import->file_first_row) {
				continue;
			}
			if ( ! Csv::isCsvRowEmpty($cols)) {
				$email = $cols[$this->importModel::CSV_MAP["email"]];
				if(strlen(trim($email)) > 0 && !Validators::isEmail($email)){
					$badEmails[] = $email;
				}
			}
		}
		if(!empty($badEmails)){
			$sliced = array_slice($badEmails,0, 30);
			$message = implode(", ", $sliced);
			$import->update(["file_message" => Strings::replace($import->file_message,'/\<div.+\>(.*)\<\/div\>/m', '')."<div class='tx-danger'>Neplatné e-maily: ".$message."</div>"]);
		}

		$this->flashMessage("Kontrola e-mailov bola dokončená.");
		$this["importGrid"]->reload();
	}

	public function actionDetail(int $id)
	{
		/*$message = "The item 'reg_id' expects to match pattern '[0-9]{6}', 'Pavol' given.";

		$values = \Kubomikita\Utils\Strings::matchAll($message, '/\'(.+?)\'/m');
		$message = str_replace(["The item","expects to match pattern","given"],["Hodnota v stĺpci","musí vyhovovať regulárnemu výrazu","vyplnené"],$message);
		bdump($message);
		bdump($values);*/

		/*$schema = Expect::structure([
			"email" => Expect::email()->nullable(),
		]);

		$data = [
			//["email" => "<EMAIL>"],
			["email" => null],
			["email" => ""],
			["email" => "<EMAIL>"]
		];

		$processor = new Processor;

		foreach($data as $row){
			$processor->process($schema, $row);
		}

		exit;*/

		parent::actionDetail($id);
		$this["importItemsGrid-fail"]->addFilterText("col_name","");
		$this["importItemsGrid-fail"]->addFilterText("col_reg_id","");
		$this["importItemsGrid-fail"]->addFilterText("col_surname","");
		$this["importItemsGrid-fail"]->addFilterText("col_email","");
		$this["importItemsGrid-fail"]->addFilterText("col_company_name","");
		$this["importItemsGrid-fail"]->addExportCsv("Export do CSV","import-failed","windows-1250")->setIcon("file-csv")->setClass("btn btn-xs btn-success");

		$this["importItemsGrid-processed"]->getColumn("message")->setName("Správa o výsledku")->setColumnClass("tx-success");
		$this["importItemsGrid-processed"]->addFilterText("col_name","");
		$this["importItemsGrid-processed"]->addFilterText("col_reg_id","");
		$this["importItemsGrid-processed"]->addFilterText("col_surname","");
		$this["importItemsGrid-processed"]->addFilterText("col_email","");
		$this["importItemsGrid-processed"]->addFilterText("col_company_name","");
		$this["importItemsGrid-processed"]->setDataSource($this->importItemsModel->findBy(["import_coworker" => $id, "processed" => 1/*, "message IS NOT NULL"*/]));
		$this["importItemsGrid-processed"]->addExportCsv("Export do CSV","import-success","windows-1250")->setIcon("file-csv")->setClass("btn btn-xs btn-success");;
	}

	#[Deprecated]
	public function createComponentImportForm($name): Form
	{
		$f = $this->formFactory->create($this,$name);
		$f->addUpload("file","CSV súbor")->setRequired();
		$f->addCheckbox("first_row","Prvý riadok je hlavička")->setDefaultValue(1);
		$f->addSubmit("import","Importovať");
		$f->onSuccess[] = function (Form $form){
			$val = $form->getValues();

			$fileUpload = $val->file;
			$check = $this->checkUploadedFile($fileUpload);
			if($check !== true){
				$this->flashMessage($check, Message::LEVEL_ERROR);
				$form->addError($check);
			}

			if(!$form->hasErrors()) {
				if ( $fileUpload->isOk() ) {
					$this->startImport(ImportModel::TYPE_COWORKER_CSV, $fileUpload, WWW_DIR . "/uploads/import_coworker", (bool) $val->first_row);
					$this->flashMessage( "O výsledku spracovania budete informovaný e-mailom.", Message::LEVEL_SUCCESS,"Súbor bol zaradený do fronty na spracovanie." );
				} else {
					$this->flashMessage( "Pri importe nastala chyba", Message::LEVEL_ERROR );
				}
			}
			$this->redirect("this");
		};
		return $f;
	}

	public function handleDelete(int $id)
	{
		$this->importModel->find($id)->delete();
		$this->flashMessage("Import a jeho dáta boli vymazané.", Message::LEVEL_ERROR);
		$this["importGrid"]->reload();
	}

	public function handleUnsync(int $item_id, int $import_id)
	{
		// TODO: Implement handleUnsync() method.
	}
}