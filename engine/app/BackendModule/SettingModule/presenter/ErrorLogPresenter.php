<?php
namespace BackendModule\SettingModule;


use GuzzleHttp\Client;
use IPub\FlashMessages\Entities\Message;
use Nette\Application\Attributes\Persistent;
use Nette\Application\BadRequestException;
use Nette\Application\ForbiddenRequestException;
use Nette\Application\Responses\CallbackResponse;
use Nette\Application\Responses\VoidResponse;
use Nette\Caching\Cache;
use Nette\Database\Table\ActiveRow;
use Nette\Http\FileUpload;
use Nette\Utils\DateTime;
use Nette\Utils\Finder;
use Nette\Utils\Strings;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\DocumentDirModel;
use Webtec\Models\ImportModel;
use Webtec\Models\TradeDataModel;
use Webtec\Models\TradeModel;
use Webtec\Models\UserModel;
use Webtec\UI\Forms\Form;

class ErrorLogPresenter extends BasePresenter {

	#[Persistent]
	public ?int $filter = null;

	protected function startup()
	{
		parent::startup();
		if(!$this->isSuperSuperadmin()){
			throw new ForbiddenRequestException();
		}
		$this["breadcrumb"]->addItem("errorLog","Error log");
	}

	public function renderDefault()
	{
		$this->template->files = $this->findFiles();
		$this->template->filter = $this->filter;
	}

	public function renderDetail(string $filename)
	{
		$file = LOG_DIR."/".$filename;
		if(!file_exists($file)){
			throw new BadRequestException();
		}

		$this->sendResponse(new CallbackResponse(function ($httoRequest, $httpResponse) use ($file){
			echo readfile($file);
		}));
	}

	public function handleFilter(?int $type = null){
		$this->filter = $type;
		$this->template->files = $this->findFiles();
		$this->template->filter = $this->filter;
		$this->redrawControl("errorFiles");
		$this->redrawControl("errorMenu");
	}

	public function handleDelete(string $filename)
	{
		if(@unlink(LOG_DIR."/".$filename)){
			$this->flashMessage("Súbor s error logom bol vymazaný.", Message::LEVEL_ERROR);
			$this->template->files = $this->findFiles();
			$this->template->filter = $this->filter;
			$this->redrawControl("errorFiles");
			$this->redrawControl("errorMenu");
		} else {
			$this->flashMessage("Súbor sa nepodarilo vymazať.");
		}

	}

	public function handleFlush(){

		foreach($this->findFiles() as $file){
			@unlink((string) $file);
		}
		$this->redrawControl("errorFiles");
		$this->flashMessage("Súbory s error logomi boli vymazané.", Message::LEVEL_ERROR);
	}

	private function findFiles(): Finder
	{
		bdump($this->filter, "FILTER");
		return Finder::findFiles("*.html")->filter(function(\SplFileInfo $file){
			$title = \Kubomikita\Utils\Strings::getTitleFromFile(LOG_DIR."/".$file);

			return match ($this->filter) {
				default => true,
				404 => Strings::contains($title, "#404"),
				403 => Strings::contains($title, "#403"),
				500 => !Strings::contains($title, "#404") && !Strings::contains($title, "#403"),
			};
		})->in(LOG_DIR);
	}
}