<?php

namespace BackendModule\SettingModule;


use IPub\FlashMessages\Entities\Message;
use Kubomikita\Attributes\Acl;
use Nette\Application\BadRequestException;
use Nette\Forms\Container;
use Nette\Utils\DateTime;
use Nette\Utils\Html;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\ApiTokenModel;
use Webtec\Models\ApiTokenSearchModel;
use Webtec\Models\UserModel;

#[Acl("Api")]
class ApiTokenPresenter extends BasePresenter {
	/** @var ApiTokenModel */
	public $apiTokenModel;
	/** @var ApiTokenSearchModel */
	public $apiTokenSearchModel;

	protected function startup() {
		parent::startup();
		$this["breadcrumb"]->addItem("api","API kľúče",":Backend:Setting:ApiToken:");
		$this->apiTokenModel = $this->modelFactory->create("apiToken");
		$this->apiTokenSearchModel = $this->modelFactory->create("apiTokenSearch");
	}

	public function actionDefault(){
		$at = $this->apiTokenModel->findAll();

		$this["apiTokenGrid"]->setDataSource($at);
	}
	public function actionEdit(int $id){
		$at = $this->apiTokenModel->find($id)->fetch();
		if(!$at){
			throw new BadRequestException();
		}
		$this["breadcrumb"]->addItem("edit",$at->token);
		$this["apiTokenSearchGrid"]->setDataSource($at->related("api_token_search"));
	}

	/**
	 * @param $name
	 *
	 * @return DataGrid
	 * @throws \Ublaboo\DataGrid\Exception\DataGridException
	 */
	public function createComponentApiTokenGrid($name):DataGrid{
		$d = $this->dataGridFactory->create($this,$name);
		$d->addColumnNumber("id","ID");
		$d->addColumnText("type","Typ prístupu")->setRenderer(function ($item){
			return ApiTokenModel::TYPES[$item->type];
		});
		$d->addColumnText("token","API kľúč (token)")->setRenderer(function ($item){
			$token = $item->token;
			$url = $this->context->parameters["url"]."/api/".$token."/trade";
			return $token.'<div class="tx-12"><a href="'.$url.'" target="_blank">Zoznam obchodov</a></div>';
		})->setTemplateEscaping(false);
		$d->addColumnText("user","Užívateľ")->setRenderer(function ($item){
			if($item->user !== null) {
				return UserModel::formatName( $item->ref( "user" ) );
			}
		});
		$d->addColumnNumber("where","WHERE count")->setRenderer(function ($item){
			return $item->related("api_token_search")->count("*");
		});
		$d->addColumnNumber("access","Prístupov za 30dni")->setRenderer(function ($item){
			$now = new DateTime();
			$diff = $now->modifyClone("-30 DAY");
			return $item->related("api_token_access")->where(["datetime >=" => $diff])->count("*");
		});

		$d->setRowCallback(function ($item, Html $tr){
			if(!$item->active){
				$tr->addAttributes(["class"=>"table-light"]);
			}
		});
		$d->addAction("activate","","activate!")->setClass("btn btn-xs btn-success ajax")->setIcon("check")->setTitle("Aktivovať");
		$d->addAction("deactivate","","activate!")->setClass("btn btn-xs btn-outline-danger ajax")->setIcon("power-off")->setTitle("Deaktivovať");
		$d->allowRowsAction("activate",function ($item){ return (bool) !$item->active; });
		$d->allowRowsAction("deactivate",function ($item){ return (bool) $item->active; });
		$d->addAction("edit","","edit")->setIcon("pencil-alt")->setClass("btn btn-primary btn-xs");
		if($this->isSuperSuperadmin()) {
			$d->addAction( "delete", "",
				"delete!" )->setIcon( "times" )->setClass( "btn btn-danger btn-xs ajax" )->setConfirmation( new StringConfirmation( "Naozaj vymazať?" ) );
		}
		return $d;
	}

	/**
	 * @param int $id
	 */
	public function handleActivate(int $id){
		$at = $this->apiTokenModel->find($id)->fetch();
		$at->update(["active" => !$at->active]);
		$this->flashMessage("API kľúč bol aktivovaný / deaktivovaný.",Message::LEVEL_SUCCESS);
		$this["apiTokenGrid"]->reload();
	}

	/**
	 * @param int $id
	 */
	public function handleDelete(int $id){
		$this->apiTokenModel->find($id)->delete();
		$this->flashMessage("API kľúč bol vymazaný.",Message::LEVEL_ERROR);
		$this["apiTokenGrid"]->reload();
	}

	public function createComponentApiTokenSearchGrid($name) : DataGrid{
		$d = $this->dataGridFactory->create($this,$name);
		$d->addColumnNumber("id","ID");
		$d->addColumnText("type","Typ");
		$d->addColumnText("presenter","Presenter");
		$d->addColumnText("key","Key");
		$d->addColumnText("value","Value");
		$d->addColumnText("desc","Popis");
		$d->setRowCallback(function ($item, Html $tr){
			if(!$item->active){
				$tr->addAttributes(["class"=>"table-light"]);
			}
		});
		if($this->isSuperSuperadmin()) {

			$d->addAction( "activate", "",
				"activateSearch!",["api_token_id"=> "id"] )->setClass( "btn btn-xs btn-success ajax" )->setIcon( "check" )->setTitle( "Aktivovať" );
			$d->addAction( "deactivate", "",
				"activateSearch!",["api_token_id"=> "id"] )->setClass( "btn btn-xs btn-outline-danger ajax" )->setIcon( "power-off" )->setTitle( "Deaktivovať" );
			$d->allowRowsAction( "activate", function ( $item ) {
				return (bool) ! $item->active;
			} );
			$d->allowRowsAction( "deactivate", function ( $item ) {
				return (bool) $item->active;
			} );

			$d->addAction( "delete", "",
				"deleteSearch!",["api_token_id"=> "id"] )->setIcon( "times" )->setClass( "btn btn-danger btn-xs ajax" )->setConfirmation( new StringConfirmation( "Naozaj vymazať?" ) );


			$d->addInlineEdit()->setClass( "btn btn-primary btn-xs ajax" )->onControlAdd[] = function (
				Container $container
			) {
				$container->addText( "type", "typ" );
				$container->addText( "presenter", "presenter" );
				$container->addText( "key", "key" );
				$container->addText( "value", "value" );
				$container->addText( "desc", "popis" );
			};
			$d->getInlineEdit()->onSetDefaults[]                                           = function (
				Container $container,
				$item
			) {
				$container->setDefaults( $item->toArray() );
			};
			$d->getInlineEdit()->onSubmit[]                                                = function ( $id, $values ) {
				$this->apiTokenSearchModel->find( $id )->update( (array) $values );
				$this->flashMessage( "Podmienka zobrazovania bola upravená.", Message::LEVEL_SUCCESS );
			};
			$d->allowRowsInlineEdit( function ( $item ) {
				return (bool) $item->active;
			} );
			$d->addInlineAdd()->onControlAdd[] = function (Container $container){
				$container->addHidden("api_token")->setDefaultValue($this->presenter->getParameter("id"));
				$container->addText( "type", "typ" )->setRequired();
				$container->addText( "presenter", "presenter" )->setRequired();
				$container->addText( "key", "key" );
				$container->addText( "value", "value" )->setRequired();
				$container->addText( "desc", "popis" );
			};
			$d->getInlineAdd()->onSubmit[] = function ($values){
				$this->apiTokenSearchModel->insert((array) $values);
				$this->flashMessage("Nová podmienka bola vytvorená.",Message::LEVEL_SUCCESS);
			};
		}
		$d->setPagination(false);

		return $d;
	}

	public function handleActivateSearch(int $api_token_id){
		$at = $this->apiTokenSearchModel->find($api_token_id)->fetch();
		$at->update(["active" => !$at->active]);
		$this->flashMessage("Podmienka bola aktivovaná / deaktivovaná.",Message::LEVEL_SUCCESS);
		$this["apiTokenSearchGrid"]->reload();
	}
	public function handleDeleteSearch(int $api_token_id){
		$this->apiTokenSearchModel->find($api_token_id)->delete();
		$this->flashMessage("Podmienka bola vymazaná.",Message::LEVEL_ERROR);
		$this["apiTokenSearchGrid"]->reload();
	}

}