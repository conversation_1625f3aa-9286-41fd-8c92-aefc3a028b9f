<?php

namespace BackendModule\SettingModule;


use IPub\FlashMessages\Entities\Message;
use Nette\Application\BadRequestException;
use Nette\Database\Table\ActiveRow;
use Nette\Http\FileUpload;
use Nette\Utils\FileSystem;
use Nette\Utils\Json;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\PresentationModel;
use Webtec\Models\PresentationSlidesAreaModel;
use Webtec\Models\PresentationSlidesModel;
use Webtec\Models\UserModel;
use Webtec\UI\Forms\Form;

class PresentationPresenter extends BasePresenter {
	/** @var PresentationModel */
	public $presentationModel;
	/** @var PresentationSlidesModel */
	public $presentationSlidesModel;
	/** @var PresentationSlidesAreaModel */
	public $presentationSlidesAreaModel;

	public function startup() {
		parent::startup();
		$this["breadcrumb"]->addItem( "presentation", "Prezentácie", ":Backend:Setting:Presentation:" );
		$this->presentationModel = $this->modelFactory->create("presentation");
		$this->presentationSlidesModel = $this->modelFactory->create("presentationSlides");
		$this->presentationSlidesAreaModel = $this->modelFactory->create("presentationSlidesArea");
	}


	public function actionDefault() {
		$this["presentationGrid"]->setDataSource($this->presentationModel->findAll());
	}

	public function actionDetail(int $id){
		$p = $this->presentationModel->find($id)->fetch();
		if(!$p){
			throw new BadRequestException();
		}

		$this["breadcrumb"]->addItem("detail","Detail prezentácie: ".$p->name,"Presentation:detail",[], ["id"=>$id]);
		$this["addSlides"]->setDefaults(["id" => $id]);
		//$this->template->p = $p;
	}

	public function actionEditSlide(int $id){
		$slide = $this->presentationSlidesModel->find($id)->fetch();
		if(!$slide){
			throw new BadRequestException();
		}
		$this["slideEdit"]->setDefaults($slide->toArray());
		$this["breadcrumb"]->addItem("editslide","Editácia slidu ");
		$this->template->slide = $slide;
		$this->template->mapArea = $this->getMapAreaJson($slide);
	}

	public function getMapAreaJson(ActiveRow $slide){
		$ret = [];
		foreach($slide->related("presentation_slides_area") as $a){
			$aaa = [
				"shape" => $a->shape,
				"href" => $a->href,
				"target" => $a->target,
				"title" => $a->title,
				"coords" => [
					[
						"naturalX" => 100,
						"naturalY" => 100,
					],
					[
						"naturalX" => 200,
						"naturalY" => 200
					]
				]
			];
			$coords = [];

			$c = explode(",",$a->coords);
			$y = 0; $keys = ["naturalX","naturalY"];
			$aa = [];
			foreach($c as $coord){
				$aa[$keys[$y]] = (int) $coord;
				if($y==1){
					$coords[] = $aa;
					$aa = [];
					$y=0;
				}else {
					$y++;
				}
			}

			$aaa["coords"] = $coords;
			$ret[] = $aaa;

		}

		return Json::encode($ret, Json::PRETTY);
	}

	public function handleDeleteSlide(int $slideid) {
		$s = $this->presentationSlidesModel->find($slideid)->fetch();//->delete();
		$pres = $s->presentation;
		$s->delete();
		$i=1;
		foreach ($this->presentationSlidesModel->findBy(["presentation" =>$pres])->order("ordr") as $pr){
			$pr->update(["ordr" => $i]);
			$i++;
		}

		$this->flashMessage("Slide bol vymazaný.",Message::LEVEL_SUCCESS);
		$this->redrawControl();
	}
	/*public function handleMoveUp(int $slideid) {
		$s = $this->presentationSlidesModel->find($slideid)->fetch();
		if($s->ordr > 1){
			$this->presentationSlidesModel->findBy(["ordr" => $s->ordr - 1,"presentation"=>$s->presentation])->update(["ordr" => $s->ordr]);
			$s->update(["ordr" => $s->ordr - 1]);
		}
		$this->redrawControl();
	}
	public function handleMoveDown(int $slideid) {
		$s = $this->presentationSlidesModel->find($slideid)->fetch();
		//if($s->ordr > 1){
			$this->presentationSlidesModel->findBy(["ordr" => $s->ordr + 1,"presentation"=>$s->presentation])->update(["ordr" => $s->ordr]);
			$s->update(["ordr" => $s->ordr + 1]);
		//}
		$this->redrawControl();
	}*/

	public function createComponentAddSlides($name){
		$f = $this->formFactory->create($this,$name);
		$f->addHidden("id");
		$f->addMultiUpload("slide","Vyberte obrázky")
			->addRule(Form::MIME_TYPE,"Súbor musí byť .jpg alebo .png",["image/jpeg","image/png"])
			->addRule(Form::MAX_FILE_SIZE, "Max. velkosť je 5MB", 5*1024*1024)
		    ->setRequired();
		$f->addSubmit("save","Nahrať");

		$f->onSuccess[] = function (Form $form){
			bdump($form->getValues());
			$values = $form->getValues();

			$max = $this->presentationSlidesModel->findBy(["presentation"=>$values->id])->max("ordr");
			$i = $max + 1;

			$dir = WWW_DIR."/uploads/presentation/";
			$images = [];
			/** @var FileUpload $image */
			foreach($values->slide as $image){
				if($image->isOk() && $image->isImage()){
					$e = explode(".",$image->getName());
					$ext = end($e);
					$name = uniqid($values->id."_").".".$ext;

					FileSystem::copy($image->getTemporaryFile(),$dir.$name);
					$images[] = [
						"image" => str_replace(WWW_DIR,"",$dir.$name),
						"presentation" => $values->id,
						"ordr" => $i
					];
					$i++;
				}
			}
			$this->presentationSlidesModel->insert($images);
			$this->flashMessage("Slajdy boli nahraté.",Message::LEVEL_SUCCESS);
			$this->redrawControl();

		};
	}

	public function createComponentPresentationGrid($name){
		$d = $this->dataGridFactory->create($this,$name);
		$d->addColumnNumber("id","ID")->addCellAttributes(["width" => 50]);
		$d->addColumnDateTime("datetime","Dátum vytvorenia")->setFormat("d.m.Y H:i:s")->addCellAttributes(["width" => 150]);
		$d->addColumnText("name","Názov");
		$d->addColumnText("user","Vytvoril")->addCellAttributes(["width"=>200])->setRenderer(function ($item){
			return UserModel::formatName($item->ref("user"));
		});
		$d->setPagination(false);
		$d->addAction("detail","","Presentation:detail",["id"=>"id"])->setIcon("search")->setClass("btn btn-primary btn-xs");

		return $d;
	}

	public function createComponentSlideEdit($name){
		$f = $this->formFactory->create($this,$name);
		$f->addHidden("id");
		$f->addSubmit("save","Uložiť");
		$f->onSuccess[] = function (Form $form){
			bdump($form->getHttpData());
			bdump($form->getValues());
			$values = $form->getValues();
			$map = $form->getHttpData()["im"];
			$id = $values->id;
			if($id > 0){
				$this->presentationSlidesModel->find($id)->update($values);
				$this->presentationSlidesAreaModel->findBy(["presentation_slides" => $id])->delete();
				$area = [];
				if(!empty($map)){
					foreach ($map as $mapval){
						unset($mapval["active"]);
						$area[] = $mapval + ["presentation_slides" => $id];
					}
					$this->presentationSlidesAreaModel->insert($area);
				}

			}
			$this->flashMessage("Slide bol uložený.",Message::LEVEL_SUCCESS);
		};
		return $f;
	}
}