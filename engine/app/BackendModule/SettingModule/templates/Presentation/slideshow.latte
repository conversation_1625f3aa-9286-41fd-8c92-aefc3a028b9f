{var $title="slideshow"}

{block content}
<div id="carouselExampleIndicators" class="carousel slide presentation" data-ride="carousel">
	<ol class="carousel-indicators">
		<li data-target="#carouselExampleIndicators" data-slide-to="0" class="active"></li>
		<li data-target="#carouselExampleIndicators" data-slide-to="1"></li>
		<li data-target="#carouselExampleIndicators" data-slide-to="2"></li>
	</ol>
	<div class="carousel-inner">
		<div class="carousel-item active">
			<img src="https://www.protein.sk/imgcache/c-cms-1283.jpg?v=1560247066-protein2.020" class="map" usemap="#image-map-1">

			<map name="image-map-1" id="map-1">
				<area target="" alt="" title="" href="#test1" coords="200,159,599,319" shape="rect">
				<area target="" alt="" title="" href="#test2" coords="725,161,1085,361" shape="rect">
				<area target="" alt="" title="" href="#test3" coords="1414,165,1595,321" shape="rect">
			</map>
		</div>
		<div class="carousel-item">
			<img class="d-block w-100" src="..." alt="Second slide">
		</div>
		<div class="carousel-item">
			<img class="d-block w-100" src="..." alt="Third slide">
		</div>
	</div>
	<a class="carousel-control-prev" href="#carouselExampleIndicators" role="button" data-slide="prev">
		<span class="carousel-control-prev-icon" aria-hidden="true"></span>
		<span class="sr-only">Previous</span>
	</a>
	<a class="carousel-control-next" href="#carouselExampleIndicators" role="button" data-slide="next">
		<span class="carousel-control-next-icon" aria-hidden="true"></span>
		<span class="sr-only">Next</span>
	</a>
</div>
<button onclick="openFullscreen();">fullscreen</button>
<style>
	.presentation map area {
		stroke: gray;
		fill: rgba(0,0,0,0.5);
	}
</style>

{/block}
{block scripts}
<script src="{$basePath}/assets/admin/js/maphilight.js"></script>
<script>
	$(document).ready(function () {
		$(".map").maphilight();
	})

	var elem = document.getElementById("carouselExampleIndicators");
	function openFullscreen() {
		if (elem.requestFullscreen) {
			elem.requestFullscreen();
		} else if (elem.mozRequestFullScreen) { /* Firefox */
			elem.mozRequestFullScreen();
		} else if (elem.webkitRequestFullscreen) { /* Chrome, Safari & Opera */
			elem.webkitRequestFullscreen();
		} else if (elem.msRequestFullscreen) { /* IE/Edge */
			elem.msRequestFullscreen();
		}
	}
</script>