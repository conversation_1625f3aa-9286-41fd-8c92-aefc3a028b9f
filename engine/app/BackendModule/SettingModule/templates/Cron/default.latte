{var $title="Automaticky spúšťané skripty"}

{block content}

<div class="row">
	<div class="col-xxl-5">


		<div class="card">
			<div class="card-header bg-gray-400 d-flex align-items-center">
				<h6 class="card-title mb-0">Zoznam úloh </h6>
                <div class="ml-auto">
                    <a n:href="createDefaults!" class="btn btn-xs btn-primary ajax">Vytvoriť základné úlohy</a>
                </div>
			</div>
			<div class="card-body pd-0 bd-color-gray-lighter">
				{control cronGrid}
			</div>
		</div>
	</div>
    <div class="col-xxl-7">
        <div n:snippet="started">
            <div class="card mb-3" n:if="$started > 0">
                <div class="card-header bg-danger d-flex align-items-center" >
                    <h6 class="card-title mb-0 tx-white">S<PERSON><PERSON><PERSON></h6>
                    <div class="ml-auto">
                        <a n:href="refreshQueue!" class="btn btn-xs btn-primary ajax"><i class="fas fa-refresh"></i> Obnoviť</a>
                    </div>
                </div>
                <div class="card-body pd-0 bd-color-gray-lighter">
                    {control cronQueueStartedGrid}
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-gray-400 d-flex align-items-center">
                <h6 class="card-title mb-0">Čakajúce úlohy</h6>
                <div class="ml-auto">
                    <a n:href="refreshQueue!" class="btn btn-xs btn-primary ajax"><i class="fas fa-refresh"></i> Obnoviť</a>
                </div>
            </div>
            <div class="card-body pd-0 bd-color-gray-lighter">
                {control cronQueueGrid}
            </div>
        </div>
    </div>
</div>
