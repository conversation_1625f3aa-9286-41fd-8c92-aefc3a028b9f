<?php

namespace App\ORM;


use Nette\Database\Table\Selection;
use Traversable;

class QueryBuilder implements \IteratorAggregate {

	protected Selection $selection;
	public function __construct(protected EntityRepository $entityRepository)
	{
		$this->selection = $this->entityRepository->getEntityModel()->findAll();// ->where();
	}

	public function where($condition, ...$params): static
	{
		$this->selection->where($condition, ... $params);
		return $this;
	}

	public function whereOr(array $parameters) : static
	{
		$this->selection->whereOr($parameters);
		return $this;
	}

	public function order(string $columns, ...$params) : static
	{
		$this->selection->order($columns, ...$params);
		return $this;
	}
	public function limit(?int $limit, ?int $offset = null) : static
	{
		$this->selection->limit($limit,$offset);
		return $this;
	}
	public function group(string $columns, ...$params) : static
	{
		$this->selection->group($columns, ...$params);
		return $this;
	}

	/**
	 * @internal
	 */
	public function getSelection(): Selection
	{
		return $this->selection;
	}

	protected function getEntityRepository(): EntityRepository
	{
		return $this->entityRepository;
	}

	/**
	 * @internal
	 */
	protected function getArrayResult() : ArrayResult
	{
		return $this->getEntityRepository()->getArrayResult($this->getSelection());
	}
	/**
	 * @internal
	 */
	protected function getResult() : object
	{
		return $this->getEntityRepository()->getResult($this->getSelection());
	}

	public function fetch() : object
	{
		return $this->getResult();
	}

	public function fetchAll() : array
	{
		return $this->getArrayResult()->fetchAll();
	}

	public function fetchPairs($key = null, $value = null) : array
	{
		return $this->getArrayResult()->fetchPairs($key, $value);
	}

	public function getIterator(): Traversable
	{
		return $this->getArrayResult();
	}
}