<?php

namespace App\ORM;



final class RepositoryFactory {

	/**
	 * The list of EntityRepository instances.
	 *
	 * @var EntityRepository[]
	 * @psalm-var array<string, EntityRepository>
	 */
	private array $repositoryList = [];

	/**
	 * Gets the repository for an entity class.
	 *
	 * @param EntityManager $entityManager The EntityManager instance.
	 * @param class-string<T>        $entityName    The name of the entity.
	 *
	 * @return EntityRepository<T>
	 *
	 * @template T of object
	 */
	public function getRepository(EntityManager $entityManager, string $entityName) : EntityRepository
	{
		$repositoryHash = $entityName . spl_object_id($entityManager);
		//bdump($repositoryHash);

		if (isset($this->repositoryList[$repositoryHash])) {
			return $this->repositoryList[$repositoryHash];
		}

		return $this->repositoryList[$repositoryHash] = $this->createRepository($entityManager, $entityName);
	}

	private function createRepository(
		EntityManager $entityManager,
		string $entityName
	): EntityRepository
	{

		$repositories = $entityManager->getRepositories();


		//$metadata            = $entityManager->getClassMetadata($entityName);
		$repositoryClassName = $repositories[$entityName] ?? EntityRepository::class; /*$metadata->customRepositoryClassName
			?: $entityManager->getConfiguration()->getDefaultRepositoryClassName();*/

		return new $repositoryClassName($entityManager, $entityName);


		//return $repository;
	}
}