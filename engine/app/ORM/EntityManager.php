<?php

namespace App\ORM;
use App\ORM\Mapping\Column;
use App\ORM\Mapping\Model;
use App\ORM\Mapping\Repository;
use <PERSON>bomikita\Utils\DateTime;
use Nette\Database\Table\ActiveRow;
use Nette\DI\Container;
use Nette\InvalidArgumentException;
use Nette\Loaders\RobotLoader;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;



class EntityManager {

	private array $entities = [];
	private array $repositories = [];
	private array $refs = [];

	private array $createdEntities = [];







	protected RepositoryFactory $repositoryFactory;

	public function __construct(
		protected Container $container,
		protected array $entityPaths,
		protected array $repositoryPaths = [APP_DIR."/ORM/Repository"],
		protected string $tempDirectory = TEMP_DIR."/cache/orm",
		protected string $entityNamespace = "App\\ORM\\Entity\\",
		protected array $loadMethods = []
	)
	{
		$this->repositoryFactory = new RepositoryFactory();
		$this->indexEntities();
	}

	private function indexEntities() : void
	{
		$loader = new RobotLoader();
		$loader->setTempDirectory($this->tempDirectory);
		foreach ($this->entityPaths as $dir) {
			$loader->addDirectory($dir);
		}
		//$loader->rebuild();

		foreach ($loader->getIndexedClasses() as $entityClass => $entityFile){
			$ref = new \ReflectionClass($entityClass);
			$modelAttrs = $ref->getAttributes(Model::class);

			if(empty($modelAttrs)){
				throw new InvalidArgumentException('Entity "'.$entityClass.'" does not have specified attribute "'.Model::class.'"');
			}

			$repositoryAttrs = $ref->getAttributes(Repository::class);
			//bdump($repositoryAttrs);
			if(!empty($repositoryAttrs)){
				$this->repositories[$entityClass] = $repositoryAttrs[0]->newInstance()->name;
			}

			$this->entities[$entityClass] = $modelAttrs[0]->newInstance()->name;
		}
	}

	public function getContainer(): Container
	{
		return $this->container;
	}

	private function getRef(ActiveRow $row, string $table, string $columnName) : ActiveRow
	{
		if ( ! isset($this->refs[$table][$row->{$columnName}])) {
			$this->refs[$table][$row->{$columnName}] = $row->ref($table, $columnName);
		}

		return $this->refs[$table][$row->{$columnName}];

	}
	public function getActiveRow(string $table, int $id) : ?ActiveRow
	{
		return $this->refs[$table][$id] ?? null;
	}

	/**
	 * Gets the repository for an entity class.
	 *
	 * @param string $entity The name of the entity.
	 * @psalm-param class-string<T> $entity
	 *
	 * @return EntityRepository The repository class.
	 * @psalm-return EntityRepository<T>|<T>
	 *
	 * @template T of object
	 */
	public function getRepository(string $entity): EntityRepository
	{
		/*if($key = array_search($entity, $this->repositories)){
			//dumpe($key);
			return $this->repositoryFactory->getRepository($this, $key);
		}*/
		if(!isset($this->entities[$entity])){
			throw new InvalidArgumentException('Entity "'.$entity.'" does not exists.');
		}

		return $this->repositoryFactory->getRepository($this, $entity);
	}

	protected function isEntity(string $entityName): bool
	{
		return isset($this->entities[$entityName]);
	}

	/**
	 * Gets the repository for an entity class.
	 *
	 * @param ActiveRow $row
	 * @param string $entityName The name of the entity.
	 * @psalm-param class-string<T> $entityName
	 *
	 * @return object The repository class.
	 * @psalm-return <T>
	 *
	 * @template T of object
	 *
	 */
	public function createEntity(ActiveRow $row, string $entityName) : object
	{

		$entityHash = $entityName . spl_object_id($row);

		if (isset($this->createdEntities[$entityHash])) {
			return $this->createdEntities[$entityHash];
		}
		$this->refs[$row->getTable()->getName()][$row->id] = $row;
		//$entityName = $entityName ?? $this->entityName;

		$reflection = new \ReflectionClass($entityName);
		$entity = new $entityName;
		foreach ($reflection->getProperties(\ReflectionProperty::IS_PUBLIC) as $property){
			$columnAttrs = $property->getAttributes(Column::class);
			if(/*empty($columnAttrs) ||*/ $property->isStatic()){
				continue;
			}
			if(empty($columnAttrs)){
				$column = new class(){
					public string|null $name = null;
					public string|null $ref = null;
					public string|null $related = null;
					public string|null $type =null;
					public bool $lazyLoad = false;
				};
			} else {
				$column = $columnAttrs[0]->newInstance();
			}
			$columnName = $column->name ?? $property->getName();

			/*$propertyTypes = [];
			if(($propertyType = $property->getType()) instanceof \ReflectionUnionType) {
				//dump($property->getType()?->getTypes());
				foreach ($propertyType->getTypes() as $type){
					$propertyTypes[] = $type->getName();
				}
				//dump($propertyType->allowsNull());
			} else {
				$propertyTypes[] = $propertyType->getName();
				if($propertyType->allowsNull()){
					$propertyTypes[] = "null";
				}
			}*/
			$propertyType = $property->getType() instanceof \ReflectionNamedType ? $property->getType()->getName() : null;
			//dump($propertyType, $propertyTypes);

			if($column->related !== null){
				//$value =  $row->related($column->related)->fetchAll();
				if($column->type === null){
					$value = array_values($row->related($column->related)->fetchAssoc("id"));
				} elseif ($column->type === ArrayHash::class){
					$value = ArrayHash::from(array_values($row->related($column->related)->fetchAssoc("id")));
				} elseif($this->isEntity($column->type)){
					$value = [];
					foreach ($row->related($column->related) as $item){
						$value[] = $this->createEntity($item, $column->type);
					}
				}
				//dumpe($value);
			} else {
				$value = $row->{$columnName};
			}


			if($column->ref !== null && $value !== null && !$column->lazyLoad){
				$ref = $this->getRef($row, $column->ref, $columnName); //$row->ref($column->ref, $columnName);
				//dump($ref);
				$value = $ref->toArray();

				if($propertyType === ArrayHash::class){
					$value = ArrayHash::from($value);
				}

				if($propertyType !== null && $this->isEntity($propertyType)){
					$value = $this->createEntity($ref, $propertyType);
				}
			}

			if($column->ref === null && $propertyType !== null && $this->isEntity($propertyType)){
				throw new InvalidArgumentException('Please specify property attribute Column, with ref: ... for property "$'.$columnName.'".');
			}





			if($propertyType === DateTime::class && $value instanceof \DateTimeInterface){
				$value = DateTime::from($value->getTimestamp());
			}

			if($column->type !== null){
				if($column->type === 'json' && $value !== null){
					$value = Json::decode($value, Json::FORCE_ARRAY);
				}
			}


			//dump($column->name,$property->getName());
			$entity->{$property->getName()} = $value;
			//dump($property, $column->name);

		}

		$this->initializeEntity($entity, $reflection);

		if(!empty($this->loadMethods)){
			foreach ($this->loadMethods as $method){
				if(method_exists($entityName, $method)){
					//$entity->{$method}($this->container);
				}
				/*try {
					dump($reflection->getMethod($method));
				} catch (\ReflectionException $e){

				}*/
			}
		}

		return $this->createdEntities[$entityHash] = $entity;
	}

	private function initializeEntity(object &$entity, \ReflectionClass $reflectionClass): void
	{
		$setupMethod = null;
		foreach ($this->loadMethods as $method) {
			if (method_exists($reflectionClass->getName(), $method)) {
				$setupMethod = $method;
			}
		}

		if($setupMethod !== null){
			$params = [];
			foreach ($reflectionClass->getMethod($setupMethod)->getParameters() as $reflectionParameter){
				if($reflectionParameter->getType()->isBuiltin()) {
					throw new InvalidArgumentException('Please provide type of object from DI container.');
				}
				$classType = $reflectionParameter->getType()->getName();
				$params[] = $this->container->getByType($classType);

			}
			$entity->{$setupMethod}(...$params);
		}


	}

	public function getRepositories(): array
	{
		return $this->repositories;
	}

	public function getEntities(): array
	{
		return $this->entities;
	}


}