<?php

declare(strict_types=1);

namespace App\ORM\Traits;

trait AccessorTrait {
	public function __call($method, $args): mixed
	{
		if (($isSetter = str_starts_with($method, "set")) ||
		    str_starts_with($method, "get")) {

			$property = lcfirst(substr($method, 3));
			dump($property);
			if ($isSetter) {
				if ($this->hasSetterAttribute($property)) {
					$this->applyFromSetter($property, $args[0]);
					return $this;
				}
			} else  {
				if ($this->hasGetterAttribute($property)) {
					return $this->$property;
				}
			}
		}

		throw new BadMethodCallException("$method not found.");
	}
}