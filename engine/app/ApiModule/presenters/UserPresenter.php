<?php
namespace ApiModule;

use Nette\Database\Table\ActiveRow;
use Nette\Utils\ArrayHash;
use Nette\Utils\Finder;
use Webtec\Models\ApiTokenModel;
use Webtec\Models\ApiTokenSearchModel;
use Webtec\Models\UserModel;

class UserPresenter extends BaseApiPresenter {

	public $reality_l_section_type = [11,12,43,55,56];

	public function startup() {
		parent::startup();
		if($this->API_TYPE == ApiTokenModel::TYPE_REALITY){
			$this->onPrepare[] = [$this, "prepareDataReal"];
			$q = $this->modelFactory->create("specialistSectionType")->findBy(["l_section_type" => $this->reality_l_section_type])->group("user")->fetchPairs("user","user");
			//$q[1] = 1;
			$this->API_WHERE = ["id" => $q];
			$this->onCleanup[] = [$this, "cleanupRowReal"];
		}

	}
	public function prepareDataReal(ActiveRow $r, ArrayHash $row) : ArrayHash{
		$nl = $r->name_list;

		$keys = ["name","surname","email","phone"];

		foreach($nl as $k=>$v){
			if(in_array($k,$keys)) {
				$row->$k = $v;
			}
		}
		$row->image_original = null;
		$row->image_thumb = null;
		if($r->image !== null) {
			$img = WWW_DIR . "/uploads/profile/" . $r->image;
			if ( file_exists( $img ) ) {
				$f        = new \SplFileInfo( $img );
				$img_name = str_replace( [ WWW_DIR, "\\" ], [ "", "/" ], $f->getPathname() );
				list( $iname, $iext ) = explode( ".", $img_name );


				$row->image_original = $this->context->parameters["url"] . $img_name;
				$row->image_thumb    = $this->context->parameters["url"] . "/imgcache" . $iname . "_w[width]_h[height]_t[type]." . $iext;

			}
		}

		return $row;
	}
	public function cleanupRowReal(ArrayHash $row):ArrayHash{
		unset(
			$row->partner_id, $row->type,$row->company_id,$row->bank_account_id,$row->acl_role_id,$row->user_groups_id,$row->password,
			$row->ikros_api_key,$row->pay_provisions,$row->ikros_invoice_sequence,$row->l_person_type_id,$row->name_list_id,$row->image
		);
		return $row;

	}
	/**
	 * @param int|null $id
	 * @param int|null $trade_section_id
	 * @param int|null $limit
	 * @param int $offset
	 *
	 * @throws \Nette\Application\AbortException
	 */
	public function actionRead(int $id = null,int $limit = null, int $offset = null){
		if($id !== null){
			$payload = $this->getById($id);
		} else {
			$payload = $this->getAll($limit,$offset);
		}

		if(empty($payload)){
			$this->returnError();
		}
		if($this->result_count > 100 && $limit === null){
			$this->redirect("this",["limit" => 100, "offset" => 0,"hash" => $this->hash]);
		}

		$this->sendJson($payload);
	}

	/**
	 * @param null $limit
	 * @param int $offset
	 *
	 * @return array
	 */
	protected function getAll($limit = null,$offset = null){
		if($this->user !== null) {
			if ( $this->userData->type === UserModel::TYPE_PARTNER ) {
				$select = $this->userModel->findBy( [ "partner" => $this->user ] );//->order( "datetime ASC" );
			} else {
				$select = $this->userModel->findAll();//->order( "datetime ASC" );
			}
		} else {
			$where = [];

			$where += $this->API_WHERE;

			$select = $this->userModel->findBy( $where );//->order( "datetime ASC" );
		}
		$this->count = $select->count("*");
		if($limit !== null){
			$offset = ($offset === null) ? 0 :$offset;
			$select->limit($limit,$offset);
		}
		//$result = $select->fetchAssoc("id");
		$result = [];
		foreach($select as $r){
			$result[$r->id] = $this->prepareRow($r);
		}
		$this->result_count = count($result);

		/*foreach($result as $id => $r){
			$result[]
		}*/

		return ["result" => $result];
	}

	/**
	 * @param int $id
	 *
	 * @return array
	 * @throws \Nette\Application\AbortException
	 */
	/*private function getById(int $id){
		if($this->user !== null) {
			$select = $this->tradeModel->findBy( [ "id" => $id, "user" => $this->user ] );
		} else {
			$select = $this->tradeModel->findBy( [ "id" => $id ] );
		}

		if(!empty($this->API_WHERE)){
			$select->where($this->API_WHERE);
		}


		$this->count = $this->result_count = $select->count("*");
		$trade = $select->fetch();
		if($trade) {
			return [ "result" => $this->prepareRow($trade) ];
		}
		$this->returnError();
	}*/
}
