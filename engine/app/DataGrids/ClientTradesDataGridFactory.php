<?php

declare(strict_types=1);

namespace Kubomikita\Factory;

use Nette\Security\User;
use Nette\SmartObject;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\ModelFactory;


final class ClientTradesDataGridFactory extends BaseTradeDataGridFactory implements IDatagridItemFactory {
	use SmartObject;

	/**
	 * NoProvisedTradesDataGridFactory constructor.
	 *
	 * @param DataGridFactory $dataGridFactory
	 * @param ModelFactory $modelFactory
	 * @param User $user
	 */
	public function __construct( DataGridFactory $dataGridFactory, ModelFactory $modelFactory, User $user ) {
		parent::__construct( $dataGridFactory, $modelFactory );
		$this->onCreate[] = function ( DataGrid $d ) use ( $user ) : DataGrid {
			$d->removeColumn("id1");
			$d->removeColumn("provision");//->setDefaultHide();
			$d->removeColumn("commission_next");//->setDefaultHide();
			//$d->removeColumn("datetime_update");
			$d->removeColumn("date_change");
			$d->removeColumn("vyrocie_zmluvy");
			$d->removeColumn("frequency_date");

			if($user->isAllowed("Trade","edit")) {
				$d->addAction( "edit", "", ":Backend:Trade:edit",
					[ "id" ] )->setClass( "btn btn-xs btn-primary" )->setIcon( "pencil-alt" );
			}
			$d->setItemsPerPageList([20,50,100]);
			return $d;
		};
	}
}