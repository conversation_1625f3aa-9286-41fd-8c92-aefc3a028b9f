<?php

declare(strict_types=1);

namespace Kubomikita\Factory;

use Nette\Caching\Cache;
use Nette\ComponentModel\IContainer;
use Nette\SmartObject;
use Nette\Utils\Json;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Webtec\Models\UserModel;

final class AssistantDataGridFactory implements IDatagridItemFactory{
	use SmartObject;
	/** @var DataGridFactory */
	private $factory;
	/** @var CacheFactory */
	private $cacheFactory;

	public function __construct(DataGridFactory $factory, CacheFactory $cacheFactory)
	{
		$this->factory = $factory;
		$this->cacheFactory = $cacheFactory;
	}

	public function create(IContainer $presenter = null, string $name = null) : DataGrid
	{
		$d = $this->factory->create($presenter,$name);
		$d->addColumnDateTime("datetime","Vytvorené")->setFormat("d.m.Y H:i:s");
		$d->addColumnText("user","Asistent")->setRenderer(function ($item){
			return UserModel::formatName($item->ref("user","assistant"));
		});
		$d->addColumnStatus("coworker","Ako spolupracovník")
		  ->addOption(1,"áno")
		  ->setClass("btn-success")
		  ->endOption()
		  ->addOption(0,"nie")
		  ->setClass("btn-danger")
		  ->endOption()->onChange[] = function ($id, $new_value) use ($presenter){
			$id = intval($id);
			$new_value = intval($new_value);

			$row = $presenter->modelFactory->create("userAssistant")->find($id)->fetch();
			$row->update(["coworker" => $new_value]);
			$this->cacheFactory->create()->clean([Cache::TAGS => ["trademenu-".$row->user, "trademenu-".$row->assistant]]);

			$presenter["assistantGrid"]->reload();
		};
		$d->addColumnStatus("specialist","Ako špecialista")
		  ->addOption(1,"áno")
		  ->setClass("btn-success")
		  ->endOption()
		  ->addOption(0,"nie")
		  ->setClass("btn-danger")
		  ->endOption()->onChange[] = function ($id, $new_value) use ($presenter){
			$id = intval($id);
			$new_value = intval($new_value);

			$row = $presenter->modelFactory->create("userAssistant")->find($id)->fetch();
			$row->update(["specialist" => $new_value]);
			$this->cacheFactory->create()->clean([Cache::TAGS => ["trademenu-".$row->user, "trademenu-".$row->assistant]]);

			$presenter["assistantGrid"]->reload();
		};

		$d->addAction("delete","","deleteAssistant!")->setIcon("times")->setClass("btn btn-xs btn-danger ajax")->setConfirmation(new StringConfirmation("Naozaj vymazať ?"));
		$d->addToolbarButton("User:addAsisstant","Pridať")->setIcon("plus")->setClass("btn btn-xs btn-success modal-open")->addAttributes(["data-toggle"=>"modal","data-target"=>"#modalAddAssistant","data-form"=>"modalAddAssistantForm","data-pv"=>Json::encode(["user"=>$presenter->getUser()->getId()])]);

		$d->setPagination(false);
		return $d;
	}
}