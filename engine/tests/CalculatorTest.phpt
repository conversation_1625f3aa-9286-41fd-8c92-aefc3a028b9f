<?php
include  __DIR__."/bootstrap.php";
use Tester\Assert;
\Tester\Environment::skip("first demo test");
class CalculatorTest extends \Tester\TestCase {
	/** @var \Kubomikita\Provision\AdvMlmCalculator */
	public $calculator;
	public function setUp() {
		parent::setUp();
		$this->calculator = ServiceLocator::getCalculatorFactory(495);
	}
	public function testADVcalculator(){
		Assert::type(\Kubomikita\Provision\AdvMlmCalculator::class,$this->calculator);
		$this->calculator->setPoints(100);
		Assert::same(100.0, $this->calculator->getProvisionBase());
		Assert::same(100.0, $this->calculator->getPoints());

		$c = $this->calculator->calculate();

		foreach($c as $last) {

			Assert::equal( [
				"datetime"       => \Tester\Expect::type( \Nette\Utils\DateTime::class ),
				"user"           => \Tester\Expect::type( "int" ),
				"trade"          => 495,
				"points"         => \Tester\Expect::type( "float" )->and( function ( $value ) {
					return $value > 0;
				} ),
				"frequency_koef" => \Tester\Expect::type( "float" ),
				"point_value"    => \Tester\Expect::type( "float" ),
				"money"          => \Tester\Expect::type( "float" ),
				"note" => \Tester\Expect::type("null"),
				"first" => \Tester\Expect::type("null"),
				"type" => \Tester\Expect::type("null")
			], $last );
		}
	}
}

(new CalculatorTest())->run();
