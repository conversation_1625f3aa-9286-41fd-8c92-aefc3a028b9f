<?php
include  __DIR__."/bootstrap.php";
use Tester\Assert;

class UserModelTest extends \Tester\TestCase {
	/** @var \Webtec\Models\ModelFactory */
	public $modelFactory;
	/** @var \Webtec\Models\UserModel */
	public $userModel;
	public function setUp() {
		parent::setUp();
		$this->modelFactory = ServiceLocator::getModelFactory();
		$this->userModel = $this->modelFactory->create("user");
	}

	public function testOne(){

		$user = $this->userModel->find(1)->fetch();

		Assert::type(\Nette\Database\Table\ActiveRow::class,$user);
		//Assert::same(16, $this->userModel->getParent($user->id));
		Assert::error(function () use($user){
			$this->userModel->getParent($user->id);
		}, E_USER_DEPRECATED);

		Assert::type("array",$this->userModel->getParents($user->id));
		Assert::count(4,$this->userModel->getParents($user->id));

		Assert::true($this->userModel->inPath(87, $user->id));
		//Assert::fail("test fail");

	}
	public function testTwo(){
		$user = $this->userModel->find(16)->fetch();
		foreach($this->userModel->getPath($user->id,true, \Webtec\Models\UserModel::LIST_TYPE_ROW) as $u){
			Assert::type("array",$this->userModel->getParents($u->id));
			Assert::same(\Webtec\Models\UserModel::formatName($u),\Webtec\Models\UserModel::formatName($u->name_list));
		}
	}
	public function testThree(){
		$user = $this->userModel->find(436)->fetch();

		Assert::type("array",$this->userModel->getPath($user->id,true));
	}
	public function testFour(){
		$user = $this->userModel->find(16)->fetch();
		$name_list = $user->name_list;

		Assert::exception(function (){
			\Webtec\Models\UserModel::formatName(null);
		},\Nette\InvalidArgumentException::class);

		//Assert::same(\Webtec\Models\UserModel::formatName($user),\Webtec\Models\UserModel::formatName($name_list));
		Assert::same(\Webtec\Models\UserModel::formatName($this->userModel->find(436)->fetch()),\Webtec\Models\UserModel::formatName($this->userModel->find(436)->fetch()->name_list));

	}
	public function testFive(){
		foreach($this->userModel->findBy(["type NOT IN"=>[\Webtec\Models\UserModel::TYPE_CLIENT]]) as $user){
			Assert::true(\Nette\Utils\Validators::isEmail($user->username),$user->id." - ".$user->username);
		}
	}
	public function tearDown() {
		parent::tearDown();
		//\Nette\Utils\FileSystem::delete(TMP_DIR);
	}
}

(new UserModelTest())->run();